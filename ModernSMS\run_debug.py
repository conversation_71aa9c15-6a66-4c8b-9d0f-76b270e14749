#!/usr/bin/env python3
"""
Run Modern SMS Manager with debug output
"""
import os

# Force production mode
os.environ['FLASK_ENV'] = 'production'

# Import and run the app
from app import create_app

if __name__ == '__main__':
    print("="*60)
    print("🚀 MODERN SMS MANAGER - DEBUG MODE")
    print("="*60)
    
    try:
        app = create_app()
        print("✅ App created successfully")
        
        print("⚠️  PRODUCTION MODE ENABLED")
        print("📡 SMS will be sent to REAL GSM hardware")
        print("📱 SMS receiver will connect to TG SMS server")
        print("🌐 Web interface: http://127.0.0.1:5000")
        print("👤 Login: admin / admin123")
        print("="*60)
        
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,  # Enable debug for better error messages
            threaded=True
        )
    except Exception as e:
        print(f"❌ Error starting application: {str(e)}")
        import traceback
        traceback.print_exc()
