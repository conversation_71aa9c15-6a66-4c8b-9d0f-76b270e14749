#!/usr/bin/env python3
"""
Manual test for delete functionality
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSMessage
import uuid
from datetime import datetime

def create_test_message():
    """Create a single test message"""
    with app.app_context():
        print("📱 Creating Test Message for Manual Delete Test")
        print("=" * 50)
        
        # Create test message
        message = SMSMessage(
            message_id=f'MANUAL_TEST_{uuid.uuid4().hex[:8]}',
            direction='outbound',
            phone_number='+************',
            content='Manual test message for delete functionality',
            status='sent',
            gsm_port='1',
            created_at=datetime.now()
        )
        
        db.session.add(message)
        db.session.commit()
        
        print(f"✅ Created test message with ID: {message.id}")
        print(f"   Message ID: {message.message_id}")
        print(f"   Phone: {message.phone_number}")
        print(f"   Content: {message.content}")
        print(f"   Direction: {message.direction}")
        print(f"   Port: {message.gsm_port}")
        
        return message

def check_message_exists(message_id):
    """Check if message exists in database"""
    with app.app_context():
        message = SMSMessage.query.get(message_id)
        return message is not None

def show_all_messages():
    """Show all messages in database"""
    with app.app_context():
        print(f"\n📊 All Messages in Database:")
        print("=" * 35)
        
        messages = SMSMessage.query.all()
        
        if not messages:
            print("   No messages found")
            return
        
        for msg in messages:
            print(f"   ID {msg.id}: {msg.direction} from {msg.phone_number}")
            print(f"      Content: {msg.content[:50]}...")
            print(f"      Port: {msg.gsm_port}, Status: {msg.status}")
            print()

def manual_delete_test():
    """Manual delete test"""
    print("🧪 MANUAL DELETE FUNCTIONALITY TEST")
    print("=" * 40)
    
    # Show current state
    show_all_messages()
    
    # Create test message
    test_message = create_test_message()
    message_id = test_message.id
    
    # Show state after creation
    print(f"\n📊 After Creating Test Message:")
    show_all_messages()
    
    # Check if message exists
    exists_before = check_message_exists(message_id)
    print(f"\n🔍 Message {message_id} exists before delete: {exists_before}")
    
    # Instructions for manual testing
    print(f"\n📋 MANUAL TEST INSTRUCTIONS:")
    print("=" * 30)
    print(f"1. Open your browser and go to: http://127.0.0.1:5000")
    print(f"2. Login as admin (username: admin, password: admin123)")
    print(f"3. Go to Outbox")
    print(f"4. Find the test message with ID {message_id}")
    print(f"   Phone: {test_message.phone_number}")
    print(f"   Content: {test_message.content}")
    print(f"5. Click the delete button (trash icon) for this message")
    print(f"6. Confirm the deletion")
    print(f"7. Come back here and press Enter to check if it was deleted")
    
    input("\nPress Enter after you've tested the delete functionality in the web interface...")
    
    # Check if message was deleted
    exists_after = check_message_exists(message_id)
    print(f"\n🔍 Message {message_id} exists after delete: {exists_after}")
    
    if not exists_after:
        print("✅ SUCCESS: Message was successfully deleted!")
    else:
        print("❌ FAILED: Message still exists in database")
    
    # Show final state
    print(f"\n📊 Final State:")
    show_all_messages()
    
    # Clean up if message still exists
    if exists_after:
        with app.app_context():
            message = SMSMessage.query.get(message_id)
            if message:
                db.session.delete(message)
                db.session.commit()
                print(f"🧹 Cleaned up test message {message_id}")

if __name__ == "__main__":
    manual_delete_test()
