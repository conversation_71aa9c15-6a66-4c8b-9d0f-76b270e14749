#!/usr/bin/env python3
"""
Web Interface for Multi-GSM Gateway Management
"""

import sys
import os
sys.path.append('backend')

from flask import Flask, render_template_string, request, jsonify, flash, redirect, url_for
from multi_gsm_system import app, db, GSMGateway, EnhancedSMSPort, UserPortAssignment
from advanced_port_assignment import MultiGSMPortManager
from app import User, login_required, require_permission, current_user

# Multi-GSM Gateway Management Routes
@app.route('/multi_gsm')
@login_required
@require_permission('can_manage_settings')
def multi_gsm_dashboard():
    """Multi-GSM gateway management dashboard"""
    try:
        overview = MultiGSMPortManager.get_gateway_overview()
        
        if overview['success']:
            gateways = overview['gateways']
            total_ports = sum(gw['total_ports'] for gw in gateways)
            assigned_ports = sum(gw['assigned_ports'] for gw in gateways)
            available_ports = sum(gw['available_ports'] for gw in gateways)
            
            stats = {
                'total_gateways': overview['total_gateways'],
                'total_ports': total_ports,
                'assigned_ports': assigned_ports,
                'available_ports': available_ports,
                'utilization_rate': round((assigned_ports / total_ports * 100) if total_ports > 0 else 0, 1)
            }
        else:
            gateways = []
            stats = {
                'total_gateways': 0,
                'total_ports': 0,
                'assigned_ports': 0,
                'available_ports': 0,
                'utilization_rate': 0
            }
        
        return render_template_string(MULTI_GSM_DASHBOARD_TEMPLATE, 
                                    gateways=gateways, 
                                    stats=stats)
    except Exception as e:
        flash(f'Error loading multi-GSM dashboard: {str(e)}', 'error')
        return render_template_string(MULTI_GSM_DASHBOARD_TEMPLATE, 
                                    gateways=[], 
                                    stats={})

@app.route('/multi_gsm/add_gateway', methods=['GET', 'POST'])
@login_required
@require_permission('can_manage_settings')
def add_gsm_gateway():
    """Add new GSM gateway"""
    if request.method == 'POST':
        try:
            gateway_data = {
                'name': request.form.get('name'),
                'ip_address': request.form.get('ip_address'),
                'api_port': request.form.get('api_port', '80'),
                'tg_sms_port': request.form.get('tg_sms_port', '5038'),
                'username': request.form.get('username'),
                'password': request.form.get('password'),
                'max_ports': int(request.form.get('max_ports', 8)),
                'location': request.form.get('location', ''),
                'description': request.form.get('description', ''),
                'is_primary': request.form.get('is_primary') == 'on'
            }
            
            # Check if gateway with same IP already exists
            existing = GSMGateway.query.filter_by(ip_address=gateway_data['ip_address']).first()
            if existing:
                flash(f'Gateway with IP {gateway_data["ip_address"]} already exists', 'error')
                return redirect(url_for('add_gsm_gateway'))
            
            # Create gateway
            gateway = GSMGateway(**gateway_data)
            db.session.add(gateway)
            db.session.flush()  # Get the ID
            
            # Create ports for this gateway
            for port_num in range(1, gateway_data['max_ports'] + 1):
                port = EnhancedSMSPort(
                    gateway_id=gateway.id,
                    port_number=str(port_num),
                    status='active',
                    network_name=f'Network_{port_num}',
                    signal_quality='Unknown',
                    sim_phone_number=f'+639{gateway.id:02d}{port_num:02d}000000',
                    is_active=True
                )
                db.session.add(port)
            
            db.session.commit()
            
            flash(f'Gateway "{gateway_data["name"]}" added successfully with {gateway_data["max_ports"]} ports', 'success')
            return redirect(url_for('multi_gsm_dashboard'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error adding gateway: {str(e)}', 'error')
    
    return render_template_string(ADD_GATEWAY_TEMPLATE)

@app.route('/multi_gsm/assign_ports', methods=['GET', 'POST'])
@login_required
@require_permission('can_manage_users')
def assign_user_ports():
    """Assign ports to users across multiple gateways"""
    if request.method == 'POST':
        try:
            username = request.form.get('username')
            assignments = []
            
            # Parse form data for assignments
            gateways = GSMGateway.query.all()
            for gateway in gateways:
                selected_ports = request.form.getlist(f'ports_{gateway.id}')
                if selected_ports:
                    assignment_type = request.form.get(f'assignment_type_{gateway.id}', 'exclusive')
                    priority = int(request.form.get(f'priority_{gateway.id}', 1))
                    
                    assignments.append({
                        'gateway_name': gateway.name,
                        'ports': [int(p) for p in selected_ports],
                        'assignment_type': assignment_type,
                        'priority': priority,
                        'notes': f'Assigned via web interface by {current_user.username}'
                    })
            
            if assignments:
                result = MultiGSMPortManager.assign_user_to_ports(
                    username=username,
                    assignments=assignments,
                    assigned_by_username=current_user.username
                )
                
                if result['success']:
                    flash(f'Port assignments updated for user {username}', 'success')
                else:
                    flash(f'Error assigning ports: {result["error"]}', 'error')
            else:
                flash('No ports selected for assignment', 'warning')
            
            return redirect(url_for('assign_user_ports'))
            
        except Exception as e:
            flash(f'Error processing assignment: {str(e)}', 'error')
    
    # Get data for form
    users = User.query.all()
    gateways = GSMGateway.query.all()
    
    # Get current assignments for each user
    user_assignments = {}
    for user in users:
        assignments = MultiGSMPortManager.get_user_assignments(user.username)
        if assignments['success']:
            user_assignments[user.username] = assignments['gateways']
        else:
            user_assignments[user.username] = {}
    
    return render_template_string(ASSIGN_PORTS_TEMPLATE, 
                                users=users, 
                                gateways=gateways,
                                user_assignments=user_assignments)

@app.route('/multi_gsm/api/gateway_status/<int:gateway_id>')
@login_required
def get_gateway_status(gateway_id):
    """Get real-time status of a specific gateway"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)
        ports = EnhancedSMSPort.query.filter_by(gateway_id=gateway_id).all()
        
        port_data = []
        for port in ports:
            assignments = UserPortAssignment.query.filter_by(
                port_id=port.id,
                is_active=True
            ).all()
            
            port_data.append({
                'port_number': port.port_number,
                'status': port.status,
                'is_active': port.is_active,
                'signal_quality': port.signal_quality,
                'sim_phone': port.sim_phone_number,
                'assignments': [
                    {
                        'user': a.user.username,
                        'type': a.assignment_type,
                        'priority': a.priority
                    } for a in assignments
                ]
            })
        
        return jsonify({
            'success': True,
            'gateway': {
                'name': gateway.name,
                'ip_address': gateway.ip_address,
                'status': gateway.status,
                'location': gateway.location,
                'is_primary': gateway.is_primary,
                'total_ports': len(ports),
                'active_ports': len([p for p in ports if p.is_active])
            },
            'ports': port_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# HTML Templates for Multi-GSM Interface
MULTI_GSM_DASHBOARD_TEMPLATE = '''
{% extends "base.html" %}
{% block title %}Multi-GSM Gateway Management{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="bi bi-hdd-network"></i> Multi-GSM Gateway Management
            </h1>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.total_gateways }}</h4>
                            <p class="mb-0">Total Gateways</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-hdd-network fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.total_ports }}</h4>
                            <p class="mb-0">Total Ports</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-router fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.assigned_ports }}</h4>
                            <p class="mb-0">Assigned Ports</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-check fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.utilization_rate }}%</h4>
                            <p class="mb-0">Utilization Rate</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-graph-up fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <a href="{{ url_for('add_gsm_gateway') }}" class="btn btn-primary me-2">
                <i class="bi bi-plus-circle"></i> Add New Gateway
            </a>
            <a href="{{ url_for('assign_user_ports') }}" class="btn btn-success me-2">
                <i class="bi bi-person-gear"></i> Assign User Ports
            </a>
            <button class="btn btn-info" onclick="refreshAllGateways()">
                <i class="bi bi-arrow-clockwise"></i> Refresh Status
            </button>
        </div>
    </div>

    <!-- Gateways List -->
    <div class="row">
        {% for gateway in gateways %}
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-hdd-network"></i> {{ gateway.name }}
                        {% if gateway.is_primary %}
                        <span class="badge bg-primary ms-2">Primary</span>
                        {% endif %}
                    </h5>
                    <span class="badge bg-{{ 'success' if gateway.status == 'active' else 'danger' }}">
                        {{ gateway.status.title() }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>IP Address:</strong> {{ gateway.ip_address }}</p>
                            <p class="mb-1"><strong>Location:</strong> {{ gateway.location }}</p>
                            <p class="mb-1"><strong>Total Ports:</strong> {{ gateway.total_ports }}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Assigned:</strong> {{ gateway.assigned_ports }}</p>
                            <p class="mb-1"><strong>Available:</strong> {{ gateway.available_ports }}</p>
                            <p class="mb-1"><strong>Active:</strong> {{ gateway.active_ports }}</p>
                        </div>
                    </div>
                    
                    <!-- Port Status Grid -->
                    <div class="mt-3">
                        <h6>Port Status:</h6>
                        <div class="d-flex flex-wrap">
                            {% for port in gateway.ports %}
                            <span class="badge me-1 mb-1 
                                {% if port.assignments %}bg-success
                                {% elif port.is_active %}bg-secondary
                                {% else %}bg-danger{% endif %}">
                                P{{ port.port_number }}
                                {% if port.assignments %}
                                ({{ port.assignments|length }})
                                {% endif %}
                            </span>
                            {% endfor %}
                        </div>
                        <small class="text-muted">
                            <span class="badge bg-success">●</span> Assigned
                            <span class="badge bg-secondary">●</span> Available
                            <span class="badge bg-danger">●</span> Inactive
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
function refreshAllGateways() {
    location.reload();
}
</script>
{% endblock %}
'''

ADD_GATEWAY_TEMPLATE = '''
{% extends "base.html" %}
{% block title %}Add GSM Gateway{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <h1 class="mb-4">
                <i class="bi bi-plus-circle"></i> Add New GSM Gateway
            </h1>
            
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Gateway Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ip_address" class="form-label">IP Address *</label>
                                    <input type="text" class="form-control" id="ip_address" name="ip_address" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="api_port" class="form-label">API Port</label>
                                    <input type="text" class="form-control" id="api_port" name="api_port" value="80">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="tg_sms_port" class="form-label">TG SMS Port</label>
                                    <input type="text" class="form-control" id="tg_sms_port" name="tg_sms_port" value="5038">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="max_ports" class="form-label">Number of Ports</label>
                                    <select class="form-select" id="max_ports" name="max_ports">
                                        <option value="4">4 Ports</option>
                                        <option value="8" selected>8 Ports</option>
                                        <option value="16">16 Ports</option>
                                        <option value="32">32 Ports</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="location" name="location" placeholder="e.g., Main Office - Floor 2">
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_primary" name="is_primary">
                            <label class="form-check-label" for="is_primary">
                                Set as Primary Gateway
                            </label>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Add Gateway
                            </button>
                            <a href="{{ url_for('multi_gsm_dashboard') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
'''

ASSIGN_PORTS_TEMPLATE = '''
{% extends "base.html" %}
{% block title %}Assign User Ports{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <h1 class="mb-4">
        <i class="bi bi-person-gear"></i> Assign User Ports Across Gateways
    </h1>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Port Assignment</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">Select User</label>
                            <select class="form-select" id="username" name="username" required onchange="showCurrentAssignments()">
                                <option value="">Choose a user...</option>
                                {% for user in users %}
                                <option value="{{ user.username }}">{{ user.username }} ({{ user.role }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        {% for gateway in gateways %}
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    {{ gateway.name }} ({{ gateway.ip_address }})
                                    {% if gateway.is_primary %}
                                    <span class="badge bg-primary">Primary</span>
                                    {% endif %}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Select Ports:</label>
                                        <div class="d-flex flex-wrap">
                                            {% for port_num in range(1, gateway.max_ports + 1) %}
                                            <div class="form-check me-3 mb-2">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="port_{{ gateway.id }}_{{ port_num }}" 
                                                       name="ports_{{ gateway.id }}" 
                                                       value="{{ port_num }}">
                                                <label class="form-check-label" for="port_{{ gateway.id }}_{{ port_num }}">
                                                    Port {{ port_num }}
                                                </label>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="assignment_type_{{ gateway.id }}" class="form-label">Assignment Type:</label>
                                        <select class="form-select" name="assignment_type_{{ gateway.id }}">
                                            <option value="exclusive">Exclusive</option>
                                            <option value="shared">Shared</option>
                                            <option value="backup">Backup</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="priority_{{ gateway.id }}" class="form-label">Priority:</label>
                                        <select class="form-select" name="priority_{{ gateway.id }}">
                                            <option value="1">1 (Highest)</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5 (Lowest)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Assignments
                            </button>
                            <a href="{{ url_for('multi_gsm_dashboard') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Current Assignments</h6>
                </div>
                <div class="card-body" id="currentAssignments">
                    <p class="text-muted">Select a user to view current assignments</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
const userAssignments = {{ user_assignments | tojson }};

function showCurrentAssignments() {
    const username = document.getElementById('username').value;
    const container = document.getElementById('currentAssignments');
    
    if (!username || !userAssignments[username]) {
        container.innerHTML = '<p class="text-muted">No assignments found</p>';
        return;
    }
    
    const assignments = userAssignments[username];
    let html = '';
    
    for (const [gatewayName, gatewayInfo] of Object.entries(assignments)) {
        html += `<div class="mb-3">
            <h6>${gatewayName}</h6>
            <small class="text-muted">${gatewayInfo.gateway_ip}</small>
            <ul class="list-unstyled ms-3">`;
        
        for (const port of gatewayInfo.ports) {
            html += `<li>
                <span class="badge bg-secondary">Port ${port.port_number}</span>
                <small>(${port.assignment_type}, Priority: ${port.priority})</small>
            </li>`;
        }
        
        html += '</ul></div>';
    }
    
    container.innerHTML = html;
}
</script>
{% endblock %}
'''

if __name__ == "__main__":
    print("🌐 Multi-GSM Web Interface Routes Added!")
    print("✅ /multi_gsm - Dashboard")
    print("✅ /multi_gsm/add_gateway - Add new gateway")
    print("✅ /multi_gsm/assign_ports - Assign user ports")
    print("✅ /multi_gsm/api/gateway_status/<id> - Gateway status API")
