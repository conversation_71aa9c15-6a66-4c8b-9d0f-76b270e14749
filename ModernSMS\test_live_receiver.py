#!/usr/bin/env python3
"""
Test live SMS receiver connection and events
"""
import os
import sys
import socket
import time
import threading
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway, SMSMessage

def test_live_connection():
    """Test live connection to TG SMS server"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔍 LIVE SMS RECEIVER TEST")
        print("="*60)
        
        # Get primary gateway
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        
        if not gateway:
            print("❌ No active primary gateway found!")
            return False
        
        print(f"📡 Testing Gateway: {gateway.name}")
        print(f"   IP: {gateway.ip_address}:{gateway.tg_sms_port}")
        print(f"   Username: {gateway.username}")
        
        try:
            # Connect to TG SMS server
            print(f"\n🔌 Connecting to TG SMS server...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            print("✅ Connected successfully")
            
            # Send login
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            print(f"\n🔐 Sending login...")
            sock.send(login_cmd.encode())
            
            # Read login response
            response = sock.recv(1024).decode()
            print(f"📥 Login response: {response.strip()}")
            
            if "Response: Success" not in response:
                print("❌ Login failed!")
                sock.close()
                return False
            
            print("✅ Login successful")
            
            # Subscribe to SMS events
            print(f"\n📱 Subscribing to SMS events...")
            event_cmd = "Action: Events\r\nEventMask: sms\r\n\r\n"
            sock.send(event_cmd.encode())
            
            # Read event subscription response
            response = sock.recv(1024).decode()
            print(f"📥 Event subscription response: {response.strip()}")
            
            # Listen for events for 30 seconds
            print(f"\n👂 Listening for SMS events for 30 seconds...")
            print("   Send an SMS to your gateway number now!")
            
            sock.settimeout(1)  # 1 second timeout for non-blocking reads
            start_time = time.time()
            event_count = 0
            
            while time.time() - start_time < 30:
                try:
                    data = sock.recv(4096).decode()
                    if data:
                        event_count += 1
                        print(f"\n📨 Event {event_count} received:")
                        print(f"   {data.strip()}")
                        
                        # Check if it's an SMS event
                        if "Event: ReceivedSMS" in data or "ReceivedSMS" in data:
                            print("🎉 SMS EVENT DETECTED!")
                            
                            # Parse the event
                            lines = data.strip().split('\n')
                            sender = None
                            content = None
                            port = None
                            
                            for line in lines:
                                if line.startswith('Sender:'):
                                    sender = line.split(':', 1)[1].strip()
                                elif line.startswith('Content:'):
                                    content = line.split(':', 1)[1].strip()
                                elif line.startswith('GsmPort:'):
                                    port = line.split(':', 1)[1].strip()
                            
                            print(f"   📞 From: {sender}")
                            print(f"   💬 Message: {content}")
                            print(f"   📱 Port: {port}")
                        
                except socket.timeout:
                    # No data received, continue listening
                    print(".", end="", flush=True)
                    continue
                except Exception as e:
                    print(f"\n❌ Error reading data: {str(e)}")
                    break
            
            print(f"\n\n📊 Summary:")
            print(f"   Events received: {event_count}")
            
            if event_count == 0:
                print("⚠️  No events received during test period")
                print("   This could mean:")
                print("   1. No SMS was sent during the test")
                print("   2. TG SMS events are not properly configured")
                print("   3. The gateway is not receiving SMS")
            
            sock.close()
            return True
            
        except Exception as e:
            print(f"❌ Connection test failed: {str(e)}")
            return False

def check_recent_messages():
    """Check for recent messages in database"""
    app = create_app()
    
    with app.app_context():
        print(f"\n📊 RECENT MESSAGES CHECK:")
        print("="*60)
        
        # Get messages from last 10 minutes
        from datetime import datetime, timedelta
        ten_minutes_ago = datetime.now() - timedelta(minutes=10)
        
        recent_messages = SMSMessage.query.filter(
            SMSMessage.direction == 'inbound',
            SMSMessage.created_at >= ten_minutes_ago
        ).order_by(SMSMessage.created_at.desc()).all()
        
        print(f"📥 Messages received in last 10 minutes: {len(recent_messages)}")
        
        for msg in recent_messages:
            print(f"   - {msg.created_at}: From {msg.phone_number}")
            print(f"     Content: {msg.content[:50]}...")
            print(f"     Port: {msg.port.port_number if msg.port else 'Unknown'}")
        
        if len(recent_messages) == 0:
            print("⚠️  No recent messages found")
            print("   Try sending an SMS to your gateway number")

if __name__ == '__main__':
    print("🚀 Starting live SMS receiver test...")
    print("📱 Make sure to send an SMS during the test!")
    
    success = test_live_connection()
    check_recent_messages()
    
    print("\n" + "="*60)
    print("🎯 TROUBLESHOOTING TIPS:")
    print("="*60)
    print("1. Ensure your GSM gateway has a SIM card with active number")
    print("2. Send SMS to the gateway's phone number during the test")
    print("3. Check if TG SMS events are enabled in gateway configuration")
    print("4. Verify the gateway is not in maintenance mode")
    print("5. Try restarting the SMS receiver from the dashboard")
    print("="*60)
