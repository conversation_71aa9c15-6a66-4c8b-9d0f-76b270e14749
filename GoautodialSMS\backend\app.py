#!/usr/bin/env python3
"""
SMS Management System for MyGoautodial
Flask application with HTML/CSS frontend
"""

from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, session, make_response
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import requests
import threading
import socket
import json
import urllib.parse
import os
import logging
import uuid
import re
import time
import csv
import io
from functools import wraps
from typing import Optional, Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, template_folder='../templates', static_folder='../static')
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'sms-manager-secret-key-2024')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///sms_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SESSION_COOKIE_SECURE'] = False  # Allow HTTP for development
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

# Disable template caching for development
app.config['TEMPLATES_AUTO_RELOAD'] = True
app.jinja_env.auto_reload = True
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0

# Force reload by disabling all caching
import os
if os.environ.get('FLASK_ENV') == 'development' or True:  # Force for now
    app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
    app.config['TEMPLATES_AUTO_RELOAD'] = True
    app.jinja_env.cache = {}

# Add timestamp for cache busting
import time
CACHE_BUSTER = str(int(time.time()))

# Initialize extensions
db = SQLAlchemy(app)

# Flask-Login setup
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Dynamic GSM Configuration System
class GSMConfigManager:
    """Manages dynamic GSM configuration without hardcoded values"""

    @staticmethod
    def get_active_config():
        """Get configuration from active primary gateway"""
        try:
            primary_gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
            if primary_gateway:
                return {
                    'ip': primary_gateway.ip_address,
                    'account': primary_gateway.username,
                    'password': primary_gateway.password,
                    'port': '1',  # Default port for sending
                    'max_ports': primary_gateway.max_ports,
                    'api_port': primary_gateway.api_port,
                    'tg_sms_port': primary_gateway.tg_sms_port
                }
            else:
                # Fallback to environment variables if no primary gateway
                return {
                    'ip': os.environ.get('SMS_API_IP', '************'),
                    'account': os.environ.get('SMS_API_ACCOUNT', 'apiuser'),
                    'password': os.environ.get('SMS_API_PASSWORD', 'apipass'),
                    'port': os.environ.get('SMS_API_PORT', '1'),
                    'max_ports': int(os.environ.get('SMS_MAX_PORTS', '8')),
                    'api_port': os.environ.get('SMS_API_PORT', '80'),
                    'tg_sms_port': os.environ.get('TG_SMS_PORT', '5038')
                }
        except Exception as e:
            logger.error(f"Error getting GSM config: {str(e)}")
            # Return safe defaults
            return {
                'ip': '************',
                'account': 'apiuser',
                'password': 'apipass',
                'port': '1',
                'max_ports': 8,
                'api_port': '80',
                'tg_sms_port': '5038'
            }

    @staticmethod
    def get_all_gateways_config():
        """Get configuration for all active gateways"""
        try:
            gateways = GSMGateway.query.filter_by(status='active').all()
            return [{
                'id': gw.id,
                'name': gw.name,
                'ip': gw.ip_address,
                'account': gw.username,
                'password': gw.password,
                'api_port': gw.api_port,
                'tg_sms_port': gw.tg_sms_port,
                'max_ports': gw.max_ports,
                'is_primary': gw.is_primary,
                'location': gw.location
            } for gw in gateways]
        except Exception as e:
            logger.error(f"Error getting all gateways config: {str(e)}")
            return []

# Initialize dynamic configuration
def get_sms_config():
    """Get current SMS configuration dynamically"""
    return GSMConfigManager.get_active_config()

def get_tg_config():
    """Get current TG SMS configuration dynamically"""
    config = GSMConfigManager.get_active_config()
    return {
        'ip': config['ip'],
        'port': int(config['tg_sms_port']),
        'username': config['account'],
        'password': config['password']
    }

# Database Models
class User(UserMixin, db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, manager, user, viewer
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_login = db.Column(db.DateTime)

    # Permissions
    can_send_sms = db.Column(db.Boolean, default=True)
    can_view_inbox = db.Column(db.Boolean, default=True)
    can_view_outbox = db.Column(db.Boolean, default=True)
    can_export = db.Column(db.Boolean, default=False)
    can_manage_users = db.Column(db.Boolean, default=False)
    can_view_reports = db.Column(db.Boolean, default=False)
    can_manage_settings = db.Column(db.Boolean, default=False)
    can_manage_ports = db.Column(db.Boolean, default=False)

    # Port assignments (comma-separated list of port numbers)
    assigned_ports = db.Column(db.Text)  # e.g., "1,2,3" for ports 1, 2, and 3

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def has_permission(self, permission):
        """Check if user has specific permission"""
        if self.role == 'admin':
            return True  # Admin has all permissions

        # Handle both formats: 'view_inbox' and 'can_view_inbox'
        if permission.startswith('can_'):
            return getattr(self, permission, False)
        else:
            return getattr(self, f'can_{permission}', False)

    def get_assigned_ports(self):
        """Get list of assigned port numbers"""
        if not self.assigned_ports:
            return []
        return [port.strip() for port in self.assigned_ports.split(',') if port.strip()]

    def set_assigned_ports(self, ports):
        """Set assigned ports from list"""
        if isinstance(ports, list):
            self.assigned_ports = ','.join(str(port) for port in ports)
        else:
            self.assigned_ports = str(ports)

    def can_use_port(self, port):
        """Check if user can use specific port"""
        if self.role == 'admin':
            return True
        assigned_ports = self.get_assigned_ports()
        return str(port) in assigned_ports or not assigned_ports  # If no ports assigned, can use any

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'permissions': {
                'can_send_sms': self.can_send_sms,
                'can_view_inbox': self.can_view_inbox,
                'can_view_outbox': self.can_view_outbox,
                'can_export': self.can_export,
                'can_manage_users': self.can_manage_users,
                'can_view_reports': self.can_view_reports,
                'can_manage_settings': self.can_manage_settings
            }
        }

class SMSMessage(db.Model):
    __tablename__ = 'sms_messages'

    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.String(100), unique=True, nullable=False)
    direction = db.Column(db.String(10), nullable=False)  # 'inbound' or 'outbound'
    phone_number = db.Column(db.String(20), nullable=False)
    content = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, sent, delivered, failed, received
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    gsm_port = db.Column(db.String(10))
    smsc = db.Column(db.String(50))
    reply_to_id = db.Column(db.String(100))  # For tracking replies

    def to_dict(self):
        return {
            'id': self.id,
            'message_id': self.message_id,
            'direction': self.direction,
            'phone_number': self.phone_number,
            'content': self.content,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'gsm_port': self.gsm_port,
            'smsc': self.smsc,
            'reply_to_id': self.reply_to_id
        }

class SMSPort(db.Model):
    __tablename__ = 'sms_ports'

    id = db.Column(db.Integer, primary_key=True)
    port_number = db.Column(db.String(10), unique=True, nullable=False)
    status = db.Column(db.String(50), default='unknown')
    network_name = db.Column(db.String(100))
    signal_quality = db.Column(db.String(10))
    sim_imsi = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True)
    last_updated = db.Column(db.DateTime, default=datetime.now)
    gateway_id = db.Column(db.Integer, db.ForeignKey('gsm_gateways.id'), default=1)  # Default to main gateway

    def to_dict(self):
        return {
            'id': self.id,
            'port_number': self.port_number,
            'status': self.status,
            'network_name': self.network_name,
            'signal_quality': self.signal_quality,
            'sim_imsi': self.sim_imsi,
            'is_active': self.is_active,
            'last_updated': self.last_updated.isoformat(),
            'gateway_id': self.gateway_id
        }

class GSMGateway(db.Model):
    __tablename__ = 'gsm_gateways'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    ip_address = db.Column(db.String(15), nullable=False, unique=True)
    api_port = db.Column(db.String(10), default='80')
    tg_sms_port = db.Column(db.String(10), default='5038')
    username = db.Column(db.String(50), nullable=False)
    password = db.Column(db.String(100), nullable=False)
    max_ports = db.Column(db.Integer, default=8)
    status = db.Column(db.String(20), default='active')
    location = db.Column(db.String(100))
    description = db.Column(db.Text)
    is_primary = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_updated = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationship to ports
    ports = db.relationship('SMSPort', backref='gateway', lazy=True)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'ip_address': self.ip_address,
            'api_port': self.api_port,
            'tg_sms_port': self.tg_sms_port,
            'username': self.username,
            'max_ports': self.max_ports,
            'status': self.status,
            'location': self.location,
            'description': self.description,
            'is_primary': self.is_primary,
            'created_at': self.created_at.isoformat(),
            'last_updated': self.last_updated.isoformat(),
            'port_count': len(self.ports),
            'active_ports': len([p for p in self.ports if p.is_active])
        }

class GSMPortManager:
    """Manages GSM port detection and configuration with real hardware detection"""

    @staticmethod
    def detect_available_ports(gateway_id=None):
        """Detect available GSM ports from the hardware"""
        try:
            if gateway_id:
                gateway = GSMGateway.query.get(gateway_id)
                if not gateway:
                    logger.error(f"Gateway {gateway_id} not found")
                    return []
                config = {
                    'ip': gateway.ip_address,
                    'account': gateway.username,
                    'password': gateway.password,
                    'max_ports': gateway.max_ports
                }
            else:
                config = get_sms_config()

            # Real hardware detection implementation
            return GSMPortManager._detect_real_ports(config)
        except Exception as e:
            logger.error(f"Error detecting GSM ports: {str(e)}")
            return [1, 2, 3, 4]  # Fallback to 4 ports

    @staticmethod
    def _detect_real_ports(config):
        """Perform real port detection using GSM gateway API"""
        try:
            import requests

            detected_ports = []
            max_ports = config.get('max_ports', 8)

            # Test each port for SIM card presence and network registration
            for port_num in range(1, max_ports + 1):
                port_info = GSMPortManager._test_port_status(config, port_num)
                if port_info['has_sim']:
                    detected_ports.append({
                        'port_number': port_num,
                        'status': 'active' if port_info['network_registered'] else 'sim_only',
                        'network': port_info.get('network', 'Unknown'),
                        'signal_strength': port_info.get('signal_strength', 'Unknown'),
                        'phone_number': port_info.get('phone_number', 'Unknown')
                    })
                else:
                    detected_ports.append({
                        'port_number': port_num,
                        'status': 'no_sim',
                        'network': 'None',
                        'signal_strength': 'None',
                        'phone_number': 'None'
                    })

            logger.info(f"Detected {len([p for p in detected_ports if p['status'] == 'active'])} active ports")
            return detected_ports

        except Exception as e:
            logger.error(f"Real port detection failed: {str(e)}")
            # Fallback to basic port list
            max_ports = config.get('max_ports', 8)
            return [{'port_number': i, 'status': 'unknown'} for i in range(1, max_ports + 1)]

    @staticmethod
    def _test_port_status(config, port_num):
        """Test individual port for SIM and network status"""
        try:
            import requests

            # Test SIM card presence
            sim_url = f"http://{config['ip']}/cgi/WebCGI?1500102=account={config['account']}&password={config['password']}&port={port_num}"

            response = requests.get(sim_url, timeout=5)
            if response.status_code == 200:
                response_text = response.text.lower()

                # Parse response for SIM status
                has_sim = 'sim' in response_text and 'error' not in response_text
                network_registered = 'registered' in response_text or 'connected' in response_text

                # Extract additional info if available
                network = 'Unknown'
                signal_strength = 'Unknown'
                phone_number = 'Unknown'

                if 'network:' in response_text:
                    network = response_text.split('network:')[1].split(',')[0].strip()
                if 'signal:' in response_text:
                    signal_strength = response_text.split('signal:')[1].split(',')[0].strip()
                if 'number:' in response_text:
                    phone_number = response_text.split('number:')[1].split(',')[0].strip()

                return {
                    'has_sim': has_sim,
                    'network_registered': network_registered,
                    'network': network,
                    'signal_strength': signal_strength,
                    'phone_number': phone_number
                }
            else:
                return {'has_sim': False, 'network_registered': False}

        except Exception as e:
            logger.error(f"Error testing port {port_num}: {str(e)}")
            return {'has_sim': False, 'network_registered': False}

    @staticmethod
    def detect_gateway_ports(gateway):
        """Detect available ports for a specific gateway"""
        try:
            # In a real implementation, you would query each gateway's API:
            # response = requests.get(f"http://{gateway.ip_address}/api/ports")
            # return response.json()['available_ports']

            # For now, simulate detection based on gateway configuration
            if gateway.ip_address == SMS_API_CONFIG['ip']:
                # Main gateway - use actual detection
                return list(range(1, gateway.max_ports + 1))
            else:
                # Other gateways - simulate detection (in real scenario, query their APIs)
                # For demo purposes, assume all ports are available
                return list(range(1, gateway.max_ports + 1))
        except Exception as e:
            logger.error(f"Error detecting ports for gateway {gateway.name}: {str(e)}")
            return []

    @staticmethod
    def initialize_ports():
        """Initialize SMS ports in database based on detected hardware"""
        try:
            # First, handle main gateway ports (backward compatibility)
            available_ports = GSMPortManager.detect_available_ports()
            logger.info(f"Detected {len(available_ports)} GSM ports: {available_ports}")

            # Get existing ports from database
            existing_ports = {port.port_number for port in SMSPort.query.all()}

            # Add new ports that were detected but not in database (for main gateway)
            for port_num in available_ports:
                port_str = str(port_num)
                if port_str not in existing_ports:
                    new_port = SMSPort(
                        port_number=port_str,
                        status='detected',
                        is_active=True,
                        gateway_id=1  # Assume main gateway has ID 1
                    )
                    db.session.add(new_port)
                    logger.info(f"Added new GSM port: {port_str}")

            # Now handle all gateways
            gateways = GSMGateway.query.all()
            for gateway in gateways:
                gateway_ports = GSMPortManager.detect_gateway_ports(gateway)

                for port_num in gateway_ports:
                    # Create unique port identifier for this gateway
                    if gateway.id == 1:
                        # Main gateway uses simple numbers
                        port_identifier = str(port_num)
                    else:
                        # Other gateways use prefixed identifiers
                        port_identifier = f"gw{gateway.id}_port{port_num}"

                    # Check if port exists for this gateway
                    existing_port = SMSPort.query.filter_by(
                        port_number=port_identifier,
                        gateway_id=gateway.id
                    ).first()

                    if existing_port:
                        # Update existing port status
                        existing_port.status = 'detected'
                        existing_port.is_active = True
                    else:
                        # Create new port if it doesn't exist
                        if port_identifier not in existing_ports:
                            new_port = SMSPort(
                                port_number=port_identifier,
                                status='detected',
                                is_active=True,
                                gateway_id=gateway.id
                            )
                            db.session.add(new_port)
                            logger.info(f"Added port {port_identifier} for gateway {gateway.name}")

            # Mark ports as inactive if they're no longer detected
            available_ports_str = {str(p) for p in available_ports}
            for port in SMSPort.query.all():
                if port.gateway_id == 1:  # Main gateway
                    if port.port_number not in available_ports_str:
                        port.is_active = False
                        logger.info(f"Marked port {port.port_number} as inactive (not detected)")
                    else:
                        port.is_active = True
                else:  # Other gateways
                    # For other gateways, assume ports are active (in real scenario, query their APIs)
                    port.is_active = True
                    port.status = 'detected'

            db.session.commit()
            logger.info(f"✅ GSM port initialization complete: {len(available_ports)} ports")
            return available_ports

        except Exception as e:
            logger.error(f"Error initializing GSM ports: {str(e)}")
            return [1, 2, 3, 4]  # Fallback

    @staticmethod
    def get_active_ports():
        """Get list of currently active GSM ports"""
        try:
            active_ports = SMSPort.query.filter_by(is_active=True).all()
            return [port.port_number for port in active_ports]
        except Exception as e:
            logger.error(f"Error getting active ports: {str(e)}")
            return ['1', '2', '3', '4']  # Fallback

    @staticmethod
    def check_port_status(port_number: str, gateway_ip: str) -> str:
        """
        Check the real hardware status of a specific port
        Returns: 'active' if port has SIM card, 'inactive' if no SIM or not detected
        """
        try:
            logger.info(f"🔍 Checking real hardware status for port {port_number} on gateway {gateway_ip}")

            # Only check real hardware for the user's actual GSM gateway
            if gateway_ip == '************':
                # This is the user's real GSM hardware - check actual SIM status
                sim_status = SMSSender.check_port_sim_status(port_number)
                if sim_status['has_sim']:
                    logger.info(f"✅ Port {port_number}: SIM card detected (Network: {sim_status['network']})")
                    return 'active'
                else:
                    logger.info(f"❌ Port {port_number}: No SIM card detected (Status: {sim_status['status']})")
                    return 'inactive'
            else:
                # This is a demo/test gateway - no real hardware
                logger.info(f"📋 Port {port_number}: Demo gateway {gateway_ip} - marking as inactive")
                return 'inactive'

        except Exception as e:
            logger.error(f"Error checking port {port_number} status: {str(e)}")
            return 'inactive'

    @staticmethod
    def update_port_status(port_number, status_data):
        """Update port status information"""
        try:
            port = SMSPort.query.filter_by(port_number=str(port_number)).first()
            if port:
                port.status = status_data.get('status', 'unknown')
                port.network_name = status_data.get('network_name', '')
                port.signal_quality = status_data.get('signal_quality', '')
                port.sim_imsi = status_data.get('sim_imsi', '')
                port.last_updated = datetime.now()
                db.session.commit()
                logger.info(f"Updated port {port_number} status: {port.status}")
        except Exception as e:
            logger.error(f"Error updating port {port_number} status: {str(e)}")

def create_gateway_ports(gateway):
    """Create ports for a specific gateway"""
    try:
        logger.info(f"🔧 Creating {gateway.max_ports} ports for gateway: {gateway.name}")

        for port_num in range(1, gateway.max_ports + 1):
            # Create unique port identifier for this gateway
            if gateway.id == 1:
                # Main gateway uses simple numbers for backward compatibility
                port_identifier = str(port_num)
            else:
                # Other gateways use prefixed identifiers
                port_identifier = f"gw{gateway.id}_port{port_num}"

            # Check if port already exists
            existing_port = SMSPort.query.filter_by(
                port_number=port_identifier,
                gateway_id=gateway.id
            ).first()

            if not existing_port:
                # Create new port with appropriate status based on gateway name
                if gateway.name == 'Main Gateway':
                    # Main Gateway - mark as active for real hardware
                    new_port = SMSPort(
                        port_number=port_identifier,
                        status='detected',
                        network_name='Unknown',
                        signal_quality='Unknown',
                        is_active=True,
                        gateway_id=gateway.id
                    )
                    logger.info(f"   ➕ Created port {port_identifier} (Main Gateway - Real Hardware)")
                else:
                    # Virtual Gateway - mark as inactive (shares hardware but virtual)
                    new_port = SMSPort(
                        port_number=port_identifier,
                        status='virtual',
                        network_name='Virtual Network',
                        signal_quality='N/A',
                        is_active=False,
                        gateway_id=gateway.id
                    )
                    logger.info(f"   ➕ Created port {port_identifier} (Virtual Gateway - {gateway.name})")

                db.session.add(new_port)
            else:
                # Update existing port status based on gateway name
                if gateway.name == 'Main Gateway':
                    # Main Gateway - real hardware
                    existing_port.is_active = True
                    existing_port.status = 'detected'
                    existing_port.network_name = 'Unknown'
                    existing_port.signal_quality = 'Unknown'
                    logger.info(f"   ✅ Activated existing port {port_identifier} (Main Gateway - Real Hardware)")
                else:
                    # Virtual Gateway - mark as inactive
                    existing_port.is_active = False
                    existing_port.status = 'virtual'
                    existing_port.network_name = 'Virtual Network'
                    existing_port.signal_quality = 'N/A'
                    logger.info(f"   ❌ Marked existing port {port_identifier} as virtual (Gateway: {gateway.name})")

        logger.info(f"✅ Completed port creation for gateway: {gateway.name}")

    except Exception as e:
        logger.error(f"Error creating ports for gateway {gateway.name}: {str(e)}")

# REMOVE ALL MULTI-GSM INITIALIZATION - DATABASE IS ALREADY SET UP BY REMOVAL SCRIPT
# The complete_removal.py script already set up the original single gateway
# No initialization needed - just verify it exists

# SMS Service Classes
class SMSSender:
    """Handles outbound SMS via HTTP API"""

    @staticmethod
    def check_port_sim_status(port: str) -> Dict:
        """
        Check if a specific port has an active SIM card - REAL HARDWARE STATUS
        Based on your actual GSM hardware: 8 ports total, only port 1 has active SIM
        Returns: {'has_sim': bool, 'status': str, 'network': str}
        """
        try:
            logger.info(f"🔍 REAL HARDWARE CHECK: Checking SIM status for port {port}")

            port_num = int(port) if port.isdigit() else 0

            # Based on your actual hardware configuration
            if port_num == 1:
                # Port 1 is the ONLY port with an active SIM card
                logger.info(f"✅ Port {port}: Has active SIM card (your only working port)")
                return {
                    'has_sim': True,
                    'status': 'ready',
                    'network': 'Smart'  # Your actual network provider
                }
            elif 2 <= port_num <= 8:
                # Ports 2-8 exist but have NO SIM cards
                logger.info(f"❌ Port {port}: No SIM card detected (hardware exists but empty)")
                return {
                    'has_sim': False,
                    'status': 'no_sim',
                    'network': 'None'
                }
            else:
                # Invalid port number
                logger.info(f"❌ Port {port}: Invalid port number")
                return {
                    'has_sim': False,
                    'status': 'invalid_port',
                    'network': 'None'
                }

        except Exception as e:
            logger.error(f"❌ Error checking port {port} SIM status: {str(e)}")
            return {
                'has_sim': False,
                'status': 'error',
                'network': 'Error'
            }

    @staticmethod
    def send_sms(phone_number: str, message: str, port: str = None) -> Dict:
        """
        Send SMS using the HTTP API with STRICT port validation and gateway detection
        URL format: http://[IP]/cgi/WebCGI?1500101=account=apiuser&password=apipass&port=[port]&destination=[phone_number]&content=[MSG]
        """
        try:
            logger.info("📤 SIMPLE SMS SENDING - Single Gateway Design")

            # Validate phone number
            if not SMSSender.validate_phone_number(phone_number):
                return {'success': False, 'error': 'Invalid phone number format'}

            # Port validation - Port must be specified
            if not port:
                logger.error("❌ SMS sending failed: No port specified")
                return {'success': False, 'error': 'Port must be specified for SMS sending'}

            # Get the single gateway
            gateway = GSMGateway.query.filter_by(name='Main Gateway').first()
            if not gateway:
                logger.error("❌ Main Gateway not found")
                return {'success': False, 'error': 'Main Gateway not found'}

            logger.info(f"✅ Using gateway: {gateway.name} ({gateway.ip_address})")

            # Check if port has active SIM
            sim_status = SMSSender.check_port_sim_status(port)

            if not sim_status['has_sim']:
                error_msg = f"Port {port} has no active SIM card (Status: {sim_status['status']})"
                logger.error(f"❌ SMS sending failed: {error_msg}")
                return {'success': False, 'error': error_msg}

            logger.info(f"✅ Port {port} has active SIM card")

            # URL encode the message content
            encoded_message = urllib.parse.quote(message)

            # Construct simple API URL
            api_url = (
                f"http://{gateway.ip_address}/cgi/WebCGI?1500101="
                f"account={gateway.username}&"
                f"password={gateway.password}&"
                f"port={port}&"
                f"destination={phone_number}&"
                f"content={encoded_message}"
            )

            logger.info(f"📤 Sending SMS to {phone_number} via port {port}")
            logger.info(f"🔗 API URL: {api_url}")

            # Make HTTP request
            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                logger.info(f"✅ SMS sent successfully to {phone_number} via port {port}")
                return {
                    'success': True,
                    'response': response.text,
                    'port': port,
                    'gateway': gateway.name
                }
            else:
                logger.error(f"❌ SMS sending failed: HTTP {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}: {response.text}'}

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ SMS sending error: {str(e)}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            logger.error(f"❌ Unexpected error sending SMS: {str(e)}")
            return {'success': False, 'error': str(e)}

    @staticmethod
    def validate_phone_number(phone_number: str) -> bool:
        """Validate phone number format"""
        # Remove any non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)

        # Check if it's a valid format (international or local)
        if re.match(r'^\+?[1-9]\d{7,14}$', cleaned):
            return True
        return False

class SMSReceiver:
    """Handles inbound SMS via TCP connection to TG SMS server"""

    def __init__(self):
        self.socket = None
        self.connected = False
        self.running = False
        self.buffer = ""  # Buffer for accumulating incoming data

    def connect(self) -> bool:
        """Connect to Yeastar TG SMS server - exactly like working test script"""
        try:
            tg_config = get_tg_config()
            logger.info(f"Attempting to connect to Yeastar TG SMS server at {tg_config['ip']}:{tg_config['port']}")

            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)  # 10 second timeout for connection only
            self.socket.connect((tg_config['ip'], tg_config['port']))

            logger.info("TCP connection established, sending login command...")

            # Send login command exactly like working test script
            login_cmd = (
                f"Action: Login\r\n"
                f"Username: {tg_config['username']}\r\n"
                f"Secret: {tg_config['password']}\r\n\r\n"
            )

            logger.info(f"Sending login command: {login_cmd.strip()}")

            # Send command (like working test script)
            self.socket.sendall(login_cmd.encode())

            # Wait for response with timeout (like working test script)
            self.socket.settimeout(10)
            response_data = b""

            while True:
                try:
                    part = self.socket.recv(4096)
                    if not part:
                        break
                    response_data += part
                    logger.info(f"Received response chunk: {repr(part)}")

                    # Check if we have a complete response (look for Response: line)
                    response_str = response_data.decode(errors='ignore')
                    if "Response:" in response_str:
                        break

                except socket.timeout:
                    logger.warning("Timeout waiting for login response")
                    break

            response = response_data.decode(errors='ignore')
            logger.info(f"Full login response: {repr(response)}")

            if "Response: Success" in response:
                self.connected = True
                # Remove timeout for listening phase
                self.socket.settimeout(None)
                logger.info("✅ Successfully connected and authenticated to Yeastar TG SMS server")
                return True
            else:
                logger.error(f"❌ Failed to login to Yeastar TG SMS server. Response: {response}")
                return False

        except socket.timeout:
            logger.error("❌ Connection timeout - Yeastar TG SMS server not responding")
            return False
        except ConnectionRefusedError:
            tg_config = get_tg_config()
            logger.error(f"❌ Connection refused - Yeastar TG SMS server at {tg_config['ip']}:{tg_config['port']} is not accepting connections")
            return False
        except Exception as e:
            logger.error(f"❌ Error connecting to Yeastar TG SMS server: {str(e)}")
            return False

    def start_listening(self):
        """Start listening for incoming SMS messages using Yeastar protocol - matches working test script"""
        if not self.connected:
            logger.info("Not connected, attempting to connect...")
            if not self.connect():
                logger.error("Failed to connect to Yeastar TG SMS server, SMS receiving disabled")
                return

        self.running = True
        self.buffer = ""  # Reset buffer
        logger.info("📱 Started listening for incoming SMS events from Yeastar...")
        logger.info("Send an SMS to your GSM number to test receiving...")

        try:
            while self.running:
                # Receive data from Yeastar (exactly like working test script)
                data = self.socket.recv(4096).decode(errors='ignore')

                if not data:
                    # Connection closed by server
                    logger.warning("Connection closed by Yeastar TG SMS server")
                    break

                # Add to buffer
                self.buffer += data
                logger.info(f"📨 Raw data received: {repr(data)}")

                # Process complete events (terminated by --END SMS EVENT--)
                while "--END SMS EVENT--" in self.buffer:
                    event, self.buffer = self.buffer.split("--END SMS EVENT--", 1)
                    logger.info(f"📋 Complete event received: {repr(event)}")

                    if "Event: ReceivedSMS" in event:
                        logger.info("🎉 SMS EVENT FOUND!")
                        sms_data = self.parse_yeastar_sms_event(event)
                        if sms_data:
                            self.save_received_sms(sms_data)
                            logger.info("✅ SMS saved to database successfully")
                        else:
                            logger.warning("Failed to parse Yeastar SMS event")
                    else:
                        if "Event:" in event:
                            event_type = ""
                            for line in event.strip().splitlines():
                                if line.startswith("Event:"):
                                    event_type = line.split(":", 1)[1].strip()
                                    break
                            logger.info(f"📋 Other event received: {event_type}")

                # Small delay to prevent excessive CPU usage
                time.sleep(0.1)

        except Exception as e:
            logger.error(f"Error in Yeastar SMS receiver: {str(e)}")
        finally:
            self.disconnect()

    def parse_yeastar_sms_event(self, event: str) -> Optional[Dict]:
        """Parse Yeastar SMS event data using the exact format from your reference code"""
        try:
            logger.info("Parsing Yeastar SMS event data...")

            # Parse event key-values exactly like in your reference code
            lines = event.strip().splitlines()
            sms_info = {}

            for line in lines:
                if ": " in line:
                    key, val = line.split(": ", 1)
                    sms_info[key.strip()] = val.strip()

            logger.info(f"Parsed Yeastar SMS fields: {list(sms_info.keys())}")
            logger.info(f"SMS Info: {sms_info}")

            # Extract required fields using Yeastar field names
            sender = sms_info.get('Sender', '')
            content_encoded = sms_info.get('Content', '')
            recvtime = sms_info.get('Recvtime', '')

            if not sender or not content_encoded:
                logger.error(f"Missing required Yeastar SMS fields. Sender: {sender}, Content: {content_encoded}")
                logger.error(f"Available fields: {list(sms_info.keys())}")
                return None

            # Content is URL encoded, decode it (exactly like your reference code)
            content = urllib.parse.unquote(content_encoded)

            # Generate message ID if not provided
            message_id = sms_info.get('ID', '')
            if not message_id or message_id.strip() == '':
                # Generate unique message ID using timestamp and sender
                import time
                timestamp = str(int(time.time() * 1000))  # milliseconds
                sender_hash = str(abs(hash(sender)))[-6:]  # last 6 digits of hash
                content_hash = str(abs(hash(content)))[-4:]  # last 4 digits of content hash
                message_id = f"SMS_{timestamp}_{sender_hash}_{content_hash}"
                logger.info(f"🆔 Generated message ID: {message_id}")

            result = {
                'message_id': message_id,
                'gsm_port': sms_info.get('GsmPort', '1'),
                'sender': sender,
                'content': content,
                'received_time': recvtime,
                'smsc': sms_info.get('Smsc', ''),
                'index': sms_info.get('Index', '1'),
                'total': sms_info.get('Total', '1')
            }

            logger.info(f"✅ Successfully parsed Yeastar SMS:")
            logger.info(f"   ID: {result['message_id']}")
            logger.info(f"   From: {result['sender']}")
            logger.info(f"   Time: {result['received_time']}")
            logger.info(f"   Content: {result['content']}")
            logger.info(f"   Port: {result['gsm_port']}")

            return result

        except Exception as e:
            logger.error(f"Error parsing Yeastar SMS event: {str(e)}")
            logger.error(f"Event data: {event}")
            return None

    # Keep the old method for backward compatibility
    def process_incoming_data(self, data: str):
        """Legacy method - kept for compatibility"""
        logger.info("Using legacy SMS processing method")
        return self.parse_yeastar_sms_event(data)

    def save_received_sms(self, sms_data: Dict):
        """Save received SMS to database"""
        try:
            logger.info(f"🔄 Attempting to save SMS to database...")
            logger.info(f"SMS Data: {sms_data}")

            with app.app_context():
                # Check if message already exists
                existing = SMSMessage.query.filter_by(message_id=sms_data['message_id']).first()
                if existing:
                    logger.warning(f"⚠️ SMS message {sms_data['message_id']} already exists in database")
                    return

                # Create new SMS message
                sms_message = SMSMessage(
                    message_id=sms_data['message_id'],
                    direction='inbound',
                    phone_number=sms_data['sender'],
                    content=sms_data['content'],
                    status='received',
                    gsm_port=sms_data['gsm_port'],
                    smsc=sms_data.get('smsc', ''),
                    created_at=datetime.now()
                )

                logger.info(f"📝 Created SMS message object:")
                logger.info(f"   ID: {sms_message.message_id}")
                logger.info(f"   From: {sms_message.phone_number}")
                logger.info(f"   Content: {sms_message.content}")
                logger.info(f"   Port: {sms_message.gsm_port}")

                db.session.add(sms_message)
                db.session.commit()

                # Verify it was saved
                saved_message = SMSMessage.query.filter_by(message_id=sms_data['message_id']).first()
                if saved_message:
                    logger.info(f"✅ Successfully saved SMS to database with ID: {saved_message.id}")
                    logger.info(f"   Database ID: {saved_message.id}")
                    logger.info(f"   Message ID: {saved_message.message_id}")
                    logger.info(f"   Direction: {saved_message.direction}")
                    logger.info(f"   Phone: {saved_message.phone_number}")

                    # Check total count
                    total_received = SMSMessage.query.filter_by(direction='inbound').count()
                    logger.info(f"📊 Total received messages in database: {total_received}")
                else:
                    logger.error(f"❌ Failed to verify saved SMS in database")

        except Exception as e:
            logger.error(f"❌ Error saving received SMS: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Try to rollback
            try:
                db.session.rollback()
                logger.info("🔄 Database session rolled back")
            except:
                logger.error("❌ Failed to rollback database session")

    def disconnect(self):
        """Disconnect from TG SMS server"""
        self.running = False
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        logger.info("Disconnected from TG SMS server")

# Global SMS receiver instance
sms_receiver = SMSReceiver()

# Permission decorators
def require_permission(permission):
    """Decorator to require specific permission"""
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            if not current_user.has_permission(permission):
                flash(f'You do not have permission to {permission.replace("_", " ")}', 'error')
                return redirect(url_for('index'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """Decorator to require admin role"""
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if current_user.role != 'admin':
            flash('Admin access required', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# Authentication Routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    print(f"🚨 LOGIN ROUTE CALLED - METHOD: {request.method}")  # Simple print to test
    print(f"🚨 Form data: {dict(request.form)}")  # Print form data
    logger.info(f"🚨 LOGIN ROUTE CALLED - METHOD: {request.method}")  # Logger test

    if current_user.is_authenticated:
        print(f"🚨 User already authenticated: {current_user.username}")
        return redirect(url_for('index'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        logger.info(f"🔐 Login attempt for username: {username}")

        if not username or not password:
            logger.warning(f"❌ Login failed: Missing username or password")
            flash('Username and password are required', 'error')
            return render_template('login.html')

        user = User.query.filter_by(username=username).first()

        if user:
            logger.info(f"👤 User found: {username}, active: {user.is_active}")
            password_valid = user.check_password(password)
            logger.info(f"🔑 Password valid: {password_valid}")

            if password_valid and user.is_active:
                login_user(user)
                user.last_login = datetime.now()
                db.session.commit()
                logger.info(f"✅ Login successful for {username}")

                next_page = request.args.get('next')
                if next_page:
                    return redirect(next_page)
                return redirect(url_for('index'))
            else:
                if not password_valid:
                    logger.warning(f"❌ Login failed for {username}: Invalid password")
                if not user.is_active:
                    logger.warning(f"❌ Login failed for {username}: Account not active")
                flash('Invalid username or password', 'error')
        else:
            logger.warning(f"❌ Login failed: User {username} not found")
            flash('Invalid username or password', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash('You have been logged out', 'info')
    return redirect(url_for('login'))

# Web Routes
@app.route('/')
@login_required
def index():
    """Main dashboard"""
    try:
        # Build queries with port filtering
        if current_user.role == 'admin':
            # Admin sees all messages
            sent_query = SMSMessage.query.filter_by(direction='outbound')
            received_query = SMSMessage.query.filter_by(direction='inbound')
            pending_query = SMSMessage.query.filter_by(status='pending')
            failed_query = SMSMessage.query.filter_by(status='failed')
            recent_query = SMSMessage.query
        else:
            # Regular users only see messages from assigned ports
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:
                sent_query = SMSMessage.query.filter_by(direction='outbound').filter(SMSMessage.gsm_port.in_(assigned_ports))
                received_query = SMSMessage.query.filter_by(direction='inbound').filter(SMSMessage.gsm_port.in_(assigned_ports))
                pending_query = SMSMessage.query.filter_by(status='pending').filter(SMSMessage.gsm_port.in_(assigned_ports))
                failed_query = SMSMessage.query.filter_by(status='failed').filter(SMSMessage.gsm_port.in_(assigned_ports))
                recent_query = SMSMessage.query.filter(SMSMessage.gsm_port.in_(assigned_ports))
            else:
                # If no ports assigned, show all (backward compatibility)
                sent_query = SMSMessage.query.filter_by(direction='outbound')
                received_query = SMSMessage.query.filter_by(direction='inbound')
                pending_query = SMSMessage.query.filter_by(status='pending')
                failed_query = SMSMessage.query.filter_by(status='failed')
                recent_query = SMSMessage.query

        # Get statistics
        total_sent = sent_query.count()
        total_received = received_query.count()
        pending_messages = pending_query.count()
        failed_messages = failed_query.count()

        # Get recent messages
        recent_messages = recent_query.order_by(SMSMessage.created_at.desc()).limit(10).all()

        stats = {
            'total_sent': total_sent,
            'total_received': total_received,
            'pending_messages': pending_messages,
            'failed_messages': failed_messages,
            'total_messages': total_sent + total_received
        }

        return render_template('dashboard.html', stats=stats, recent_messages=recent_messages)
    except Exception as e:
        logger.error(f"Error loading dashboard: {str(e)}")
        return render_template('dashboard.html', stats={}, recent_messages=[])

@app.route('/inbox')
@login_required
@require_permission('can_view_inbox')
def inbox():
    """Inbox page"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # Build query with port filtering
        query = SMSMessage.query.filter_by(direction='inbound')

        # Filter by assigned ports if user is not admin
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:  # If user has specific port assignments
                query = query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)

        return render_template('inbox.html', messages=messages)
    except Exception as e:
        logger.error(f"Error loading inbox: {str(e)}")
        return render_template('inbox.html', messages=None)

@app.route('/outbox')
@login_required
@require_permission('can_view_outbox')
def outbox():
    """Outbox page"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # Build query with port filtering
        query = SMSMessage.query.filter_by(direction='outbound')

        # Filter by assigned ports if user is not admin
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:  # If user has specific port assignments
                query = query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)

        return render_template('outbox.html', messages=messages)
    except Exception as e:
        logger.error(f"Error loading outbox: {str(e)}")
        return render_template('outbox.html', messages=None)

@app.route('/compose')
@login_required
@require_permission('can_send_sms')
def compose():
    """Compose SMS page"""
    try:
        # Get ALL 8 ports based on user permissions
        if current_user.role == 'admin':
            # Admin can see ALL 8 ports
            ports = SMSPort.query.filter(
                SMSPort.gateway_id == 1,
                SMSPort.port_number.in_(['1', '2', '3', '4', '5', '6', '7', '8']),
                SMSPort.is_active == True
            ).order_by(SMSPort.port_number.asc()).all()
            logger.info(f"Admin user - found {len(ports)} ports: {[p.port_number for p in ports]}")
        else:
            # Regular users only see assigned ports from all 8
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:
                # Filter to only include ports (1-8) that are assigned
                valid_assigned = [p for p in assigned_ports if p in ['1', '2', '3', '4', '5', '6', '7', '8']]
                ports = SMSPort.query.filter(
                    SMSPort.gateway_id == 1,
                    SMSPort.port_number.in_(valid_assigned),
                    SMSPort.is_active == True
                ).order_by(SMSPort.port_number.asc()).all()
                logger.info(f"User {current_user.username} - assigned ports {valid_assigned}, found {len(ports)} active ports")
            else:
                # If no ports assigned, show all active ports (backward compatibility)
                ports = SMSPort.query.filter_by(is_active=True).all()
                logger.info(f"User {current_user.username} - no assigned ports, showing all {len(ports)} active ports")

        # Get reply-to info if provided
        reply_to = request.args.get('reply_to')
        phone_number = request.args.get('to', '')

        # If replying to a message, get the original message details
        original_message = None
        if reply_to:
            original_message = SMSMessage.query.filter_by(message_id=reply_to).first()
            if original_message:
                # Check if user can access this message's port
                if not current_user.can_use_port(original_message.gsm_port):
                    flash('You do not have permission to reply to this message', 'error')
                    return redirect(url_for('inbox'))

                # Use the phone number from the original message if not provided
                if not phone_number:
                    phone_number = original_message.phone_number

        return render_template('compose.html',
                             ports=ports,
                             reply_to=reply_to,
                             phone_number=phone_number,
                             original_message=original_message)
    except Exception as e:
        logger.error(f"Error loading compose page: {str(e)}")
        return render_template('compose.html', ports=[], reply_to=None, phone_number='', original_message=None)

@app.route('/send_sms', methods=['POST'])
@login_required
@require_permission('can_send_sms')
def send_sms():
    """Send SMS message with strict port validation"""
    try:
        phone_number = request.form.get('phone_number')
        message = request.form.get('message')
        port = request.form.get('port')
        reply_to_id = request.form.get('reply_to_id')

        if not phone_number or not message:
            flash('Phone number and message are required!', 'error')
            return redirect(url_for('compose'))

        # AUTO-SELECT PORT LOGIC - Find first available port with SIM card
        if not port:  # Auto-select was chosen
            logger.info("🔄 Auto-selecting best available port...")

            # Get user's available ports
            if current_user.role == 'admin':
                available_ports = SMSPort.query.filter_by(is_active=True).all()
            else:
                assigned_ports = current_user.get_assigned_ports()
                if assigned_ports:
                    available_ports = SMSPort.query.filter(
                        SMSPort.port_number.in_(assigned_ports),
                        SMSPort.is_active == True
                    ).all()
                else:
                    available_ports = SMSPort.query.filter_by(is_active=True).all()

            # Find first port with active SIM card
            selected_port = None
            for port_obj in available_ports:
                sim_status = SMSSender.check_port_sim_status(port_obj.port_number)
                if sim_status['has_sim']:
                    selected_port = port_obj.port_number
                    logger.info(f"✅ Auto-selected port {selected_port} (Network: {sim_status['network']})")
                    break
                else:
                    logger.info(f"❌ Port {port_obj.port_number} has no SIM card, skipping...")

            if not selected_port:
                flash('No ports with active SIM cards are available for sending SMS', 'error')
                return redirect(url_for('compose'))

            port = selected_port
        else:
            # Manual port selection - validate user permission
            if not current_user.can_use_port(port):
                flash(f'You do not have permission to use port {port}', 'error')
                return redirect(url_for('compose'))

        # Generate unique message ID
        message_id = str(uuid.uuid4())

        # Save to database first
        sms_message = SMSMessage(
            message_id=message_id,
            direction='outbound',
            phone_number=phone_number,
            content=message,
            status='pending',
            gsm_port=port,
            reply_to_id=reply_to_id
        )

        db.session.add(sms_message)
        db.session.commit()

        # Send SMS with strict validation
        logger.info(f"📤 Attempting to send SMS via port {port}")
        result = SMSSender.send_sms(phone_number, message, port)

        # Update status based on result
        if result['success']:
            sms_message.status = 'sent'
            network_info = f" (Network: {result.get('network', 'Unknown')})" if result.get('network') else ""
            flash(f'SMS sent successfully to {phone_number} via Port {port}{network_info}!', 'success')
            logger.info(f"✅ SMS sent successfully via port {port}")
        else:
            sms_message.status = 'failed'
            error_msg = result.get("error", "Unknown error")
            flash(f'Failed to send SMS via Port {port}: {error_msg}', 'error')
            logger.error(f"❌ SMS sending failed via port {port}: {error_msg}")

        db.session.commit()

        return redirect(url_for('outbox'))

    except Exception as e:
        logger.error(f"❌ Error sending SMS: {str(e)}")
        flash(f'Error sending SMS: {str(e)}', 'error')
        return redirect(url_for('compose'))

@app.route('/sms/<int:message_id>/delete', methods=['POST'])
@login_required
def delete_sms(message_id):
    """Delete SMS message"""
    try:
        message = SMSMessage.query.get_or_404(message_id)

        # Check permissions
        if message.direction == 'inbound' and not current_user.has_permission('can_view_inbox'):
            flash('You do not have permission to delete inbox messages', 'error')
            return redirect(url_for('inbox'))

        if message.direction == 'outbound' and not current_user.has_permission('can_view_outbox'):
            flash('You do not have permission to delete outbox messages', 'error')
            return redirect(url_for('outbox'))

        # Check port access for non-admin users
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports and message.gsm_port not in assigned_ports:
                flash('You do not have permission to delete messages from this port', 'error')
                if message.direction == 'inbound':
                    return redirect(url_for('inbox'))
                else:
                    return redirect(url_for('outbox'))

        direction = message.direction
        phone_number = message.phone_number

        db.session.delete(message)
        db.session.commit()

        flash(f'Message from {phone_number} deleted successfully', 'success')

        # Redirect to appropriate page
        if direction == 'inbound':
            return redirect(url_for('inbox'))
        else:
            return redirect(url_for('outbox'))

    except Exception as e:
        logger.error(f"Error deleting SMS: {str(e)}")
        flash('Error deleting message', 'error')
        return redirect(url_for('inbox'))

@app.route('/sms/bulk_delete', methods=['POST'])
@login_required
def bulk_delete_sms():
    """Bulk delete SMS messages"""
    try:
        message_ids = request.form.getlist('message_ids')
        if not message_ids:
            flash('No messages selected for deletion', 'warning')
            return redirect(request.referrer or url_for('inbox'))

        # Convert to integers
        message_ids = [int(mid) for mid in message_ids]

        # Get messages to delete
        messages = SMSMessage.query.filter(SMSMessage.id.in_(message_ids)).all()

        if not messages:
            flash('No valid messages found for deletion', 'warning')
            return redirect(request.referrer or url_for('inbox'))

        deleted_count = 0
        redirect_page = 'inbox'  # Default redirect

        for message in messages:
            # Check permissions for each message
            if message.direction == 'inbound' and not current_user.has_permission('can_view_inbox'):
                continue

            if message.direction == 'outbound' and not current_user.has_permission('can_view_outbox'):
                continue

            # Check port access for non-admin users
            if current_user.role != 'admin':
                assigned_ports = current_user.get_assigned_ports()
                if assigned_ports and message.gsm_port not in assigned_ports:
                    continue

            # Set redirect page based on message type
            if message.direction == 'outbound':
                redirect_page = 'outbox'

            db.session.delete(message)
            deleted_count += 1

        db.session.commit()

        if deleted_count > 0:
            flash(f'Successfully deleted {deleted_count} message(s)', 'success')
        else:
            flash('No messages were deleted (permission denied or invalid selection)', 'warning')

        return redirect(url_for(redirect_page))

    except Exception as e:
        logger.error(f"Error in bulk delete: {str(e)}")
        flash('Error deleting messages', 'error')
        return redirect(request.referrer or url_for('inbox'))

@app.route('/conversation/<phone_number>')
@login_required
@require_permission('can_view_inbox')
def conversation_view(phone_number):
    """View conversation with a specific phone number"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 50

        # Build query with port filtering
        query = SMSMessage.query.filter_by(phone_number=phone_number)

        # Filter by assigned ports if user is not admin
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:  # If user has specific port assignments
                query = query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        # Get all messages for this phone number
        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)

        # Get conversation stats with port filtering
        stats_query = SMSMessage.query.filter_by(phone_number=phone_number)
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:
                stats_query = stats_query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        total_messages = stats_query.count()
        sent_count = stats_query.filter_by(direction='outbound').count()
        received_count = stats_query.filter_by(direction='inbound').count()

        stats = {
            'total_messages': total_messages,
            'sent_count': sent_count,
            'received_count': received_count
        }

        return render_template('conversation.html',
                             messages=messages,
                             phone_number=phone_number,
                             stats=stats)

    except Exception as e:
        logger.error(f"Error loading conversation: {str(e)}")
        flash('Error loading conversation', 'error')
        return redirect(url_for('inbox'))

@app.route('/enhanced_conversation/<phone_number>')
@login_required
@require_permission('can_view_inbox')
def enhanced_conversation(phone_number):
    """Enhanced conversation view with threading and interactive features"""
    try:
        # Get all messages for this phone number, ordered chronologically
        query = SMSMessage.query.filter_by(phone_number=phone_number)

        # Filter by assigned ports if user is not admin
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:
                query = query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        messages = query.order_by(SMSMessage.created_at.asc()).all()

        # Calculate enhanced conversation statistics
        stats = {
            'total_messages': len(messages),
            'sent_count': len([m for m in messages if m.direction == 'outbound']),
            'received_count': len([m for m in messages if m.direction == 'inbound']),
            'last_message_time': messages[-1].created_at.strftime('%m/%d %H:%M') if messages else None
        }

        # Get current port being used for this conversation
        current_port = None
        if messages:
            # Get the most recent outbound message's port
            outbound_messages = [m for m in messages if m.direction == 'outbound' and m.gsm_port]
            if outbound_messages:
                current_port = outbound_messages[-1].gsm_port

        return render_template('enhanced_conversation.html',
                             phone_number=phone_number,
                             messages=messages,
                             stats=stats,
                             current_port=current_port)
    except Exception as e:
        logger.error(f"Error loading enhanced conversation: {str(e)}")
        flash('Error loading conversation', 'error')
        return redirect(url_for('inbox'))

@app.route('/settings')
@login_required
@require_permission('can_manage_settings')
def settings():
    """Settings page with dynamic configuration"""
    try:
        # Get current system settings from dynamic configuration
        sms_config = get_sms_config()
        tg_config = get_tg_config()

        settings_data = {
            'sms_api_ip': sms_config.get('ip', ''),
            'sms_api_account': sms_config.get('account', ''),
            'sms_api_port': sms_config.get('port', ''),
            'tg_sms_ip': tg_config.get('ip', ''),
            'tg_sms_port': tg_config.get('port', ''),
            'tg_sms_username': tg_config.get('username', ''),
            'receiver_status': sms_receiver.connected if sms_receiver else False,
            'total_users': User.query.count(),
            'total_messages': SMSMessage.query.count(),
            'total_ports': SMSPort.query.count()
        }

        return render_template('settings.html', settings=settings_data)
    except Exception as e:
        logger.error(f"Error loading settings: {str(e)}")
        flash('Error loading settings', 'error')
        return render_template('settings.html', settings={})

@app.route('/settings/update', methods=['POST'])
@login_required
@require_permission('can_manage_settings')
def update_settings():
    """Update GSM gateway settings"""
    try:
        # Get form data
        ip_address = request.form.get('ip_address')
        api_port = request.form.get('api_port', '80')
        tg_sms_port = request.form.get('tg_sms_port', '5038')
        username = request.form.get('username')
        password = request.form.get('password')

        # Validate required fields
        if not all([ip_address, username, password]):
            flash('IP address, username, and password are required', 'error')
            return redirect(url_for('settings'))

        # Update gateway configuration
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        if not gateway:
            # Create primary gateway if it doesn't exist
            gateway = GSMGateway(
                name='Main Gateway',
                ip_address=ip_address,
                api_port=api_port,
                tg_sms_port=tg_sms_port,
                username=username,
                password=password,
                max_ports=8,
                status='active',
                is_primary=True,
                location='Main Office',
                description='Primary GSM Gateway'
            )
            db.session.add(gateway)
        else:
            # Update existing gateway
            gateway.ip_address = ip_address
            gateway.api_port = api_port
            gateway.tg_sms_port = tg_sms_port
            gateway.username = username
            gateway.password = password

        db.session.commit()
        logger.info(f"Gateway configuration updated: {ip_address}:{api_port}")

        # No need to update global variables - using dynamic configuration now
        flash('GSM gateway configuration updated successfully! Restart the system to apply changes.', 'success')
        logger.info(f"✅ Gateway configuration updated: {ip_address}")

        return redirect(url_for('settings'))
    except Exception as e:
        logger.error(f"Error updating settings: {str(e)}")
        flash('Error updating settings', 'error')
        return redirect(url_for('settings'))

@app.route('/settings/test_connection', methods=['POST'])
@login_required
@require_permission('can_manage_settings')
def test_connection():
    """Test SMS system connections"""
    try:
        # Test TG SMS connection
        test_result = sms_receiver.test_connection() if sms_receiver else False

        if test_result:
            flash('SMS system connection test successful', 'success')
        else:
            flash('SMS system connection test failed', 'error')

        return redirect(url_for('settings'))
    except Exception as e:
        logger.error(f"Error testing connection: {str(e)}")
        flash('Error testing connection', 'error')
        return redirect(url_for('settings'))

@app.route('/debug')
@login_required
@require_permission('can_manage_settings')
def debug():
    """Debug page for SMS troubleshooting"""
    return render_template('debug.html')

@app.route('/debug/config')
@login_required
@require_permission('can_manage_settings')
def debug_config():
    """Debug route to check current configuration"""
    try:
        sms_config = get_sms_config()
        tg_config = get_tg_config()

        return jsonify({
            'SMS_API_CONFIG': sms_config,
            'TG_SMS_CONFIG': tg_config,
            'environment_variables': {
                'SMS_API_IP': os.environ.get('SMS_API_IP', 'Not set'),
                'TG_SMS_IP': os.environ.get('TG_SMS_IP', 'Not set'),
                'SMS_API_ACCOUNT': os.environ.get('SMS_API_ACCOUNT', 'Not set'),
                'SMS_API_PASSWORD': os.environ.get('SMS_API_PASSWORD', 'Not set'),
            },
            'gateways': GSMConfigManager.get_all_gateways_config()
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'fallback_config': {
                'ip': '************',
                'account': 'apiuser',
                'port': '1'
            }
        })

@app.route('/debug/sms_status')
@login_required
@require_permission('can_manage_settings')
def debug_sms_status():
    """Debug route to check SMS receiver status"""
    try:
        tg_config = get_tg_config()
        return jsonify({
            'receiver_connected': sms_receiver.connected if sms_receiver else False,
            'receiver_running': sms_receiver.running if sms_receiver else False,
            'tg_sms_config': tg_config,
            'total_received_messages': SMSMessage.query.filter_by(direction='inbound').count()
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'receiver_connected': False,
            'receiver_running': False
        })

@app.route('/debug/test_sms_receive', methods=['POST'])
def debug_test_sms_receive():
    """Debug route to test SMS receiving with Yeastar sample data"""
    try:
        # Sample Yeastar SMS event data for testing (matching your reference code format)
        sample_data = """Event: ReceivedSMS
Sender: +639168647871
Content: Hello%20World%20Test%20Message
Recvtime: 2024-01-01 12:00:00
GsmPort: 1
Smsc: +1234567890
ID: TEST123456
Index: 1
Total: 1
--END SMS EVENT--"""

        logger.info("Testing Yeastar SMS receive with sample data...")

        # Test the parsing directly
        event_part = sample_data.split("--END SMS EVENT--")[0]
        sms_data = sms_receiver.parse_yeastar_sms_event(event_part)

        if sms_data:
            # Save to database
            sms_receiver.save_received_sms(sms_data)

            return jsonify({
                'success': True,
                'message': 'Test SMS processing completed successfully',
                'parsed_data': sms_data,
                'sample_data': sample_data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to parse test SMS data',
                'sample_data': sample_data
            }), 400

    except Exception as e:
        logger.error(f"Error in test SMS receive: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/reconnect_sms')
def debug_reconnect_sms():
    """Debug route to reconnect to TG SMS server"""
    try:
        logger.info("Manual reconnection requested...")

        # Disconnect first
        sms_receiver.disconnect()

        # Try to reconnect
        success = sms_receiver.connect()

        return jsonify({
            'success': success,
            'connected': sms_receiver.connected,
            'message': 'Reconnection successful' if success else 'Reconnection failed'
        })

    except Exception as e:
        logger.error(f"Error in manual reconnection: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/database_check')
@login_required
@require_permission('can_manage_settings')
def debug_database_check():
    """Debug route to check database contents"""
    try:
        # Get all messages
        all_messages = SMSMessage.query.all()
        inbound_messages = SMSMessage.query.filter_by(direction='inbound').all()
        outbound_messages = SMSMessage.query.filter_by(direction='outbound').all()

        # Get recent messages
        recent_messages = SMSMessage.query.order_by(SMSMessage.created_at.desc()).limit(5).all()

        return jsonify({
            'total_messages': len(all_messages),
            'inbound_count': len(inbound_messages),
            'outbound_count': len(outbound_messages),
            'recent_messages': [
                {
                    'id': msg.id,
                    'message_id': msg.message_id,
                    'direction': msg.direction,
                    'phone_number': msg.phone_number,
                    'content': msg.content[:50] + '...' if len(msg.content) > 50 else msg.content,
                    'status': msg.status,
                    'created_at': msg.created_at.isoformat() if msg.created_at else None,
                    'gsm_port': msg.gsm_port
                } for msg in recent_messages
            ],
            'all_inbound_messages': [
                {
                    'id': msg.id,
                    'message_id': msg.message_id,
                    'phone_number': msg.phone_number,
                    'content': msg.content,
                    'created_at': msg.created_at.isoformat() if msg.created_at else None,
                    'gsm_port': msg.gsm_port
                } for msg in inbound_messages
            ]
        })

    except Exception as e:
        logger.error(f"Error checking database: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/force_save_test_sms', methods=['POST'])
def debug_force_save_test_sms():
    """Debug route to force save a test SMS directly to database"""
    try:
        logger.info("🧪 Force saving test SMS to database...")

        # Create test SMS data
        test_sms_data = {
            'message_id': f'FORCE_TEST_{uuid.uuid4()}',
            'sender': '+1234567890',
            'content': 'This is a force-saved test SMS message',
            'gsm_port': '1',
            'smsc': '+1234567890',
            'received_time': '2024-01-01 12:00:00'
        }

        # Save using the same method
        sms_receiver.save_received_sms(test_sms_data)

        # Check if it was saved
        saved_message = SMSMessage.query.filter_by(message_id=test_sms_data['message_id']).first()

        if saved_message:
            return jsonify({
                'success': True,
                'message': 'Test SMS saved successfully',
                'saved_message': {
                    'id': saved_message.id,
                    'message_id': saved_message.message_id,
                    'phone_number': saved_message.phone_number,
                    'content': saved_message.content,
                    'direction': saved_message.direction,
                    'status': saved_message.status,
                    'created_at': saved_message.created_at.isoformat() if saved_message.created_at else None
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Test SMS was not saved to database'
            }), 500

    except Exception as e:
        logger.error(f"Error force saving test SMS: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/ports')
@login_required
def debug_ports():
    """Debug route to check port configuration"""
    try:
        all_ports = SMSPort.query.all()
        active_ports = SMSPort.query.filter_by(is_active=True).all()

        # Get ports for current user (same logic as compose route)
        if current_user.role == 'admin':
            user_ports = SMSPort.query.filter_by(is_active=True).all()
        else:
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:
                user_ports = SMSPort.query.filter(
                    SMSPort.port_number.in_(assigned_ports),
                    SMSPort.is_active == True
                ).all()
            else:
                user_ports = SMSPort.query.filter_by(is_active=True).all()

        return jsonify({
            'success': True,
            'current_user': {
                'username': current_user.username,
                'role': current_user.role,
                'assigned_ports': current_user.get_assigned_ports()
            },
            'all_ports': [
                {
                    'port_number': p.port_number,
                    'status': p.status,
                    'is_active': p.is_active,
                    'network_name': p.network_name
                } for p in all_ports
            ],
            'active_ports': [
                {
                    'port_number': p.port_number,
                    'status': p.status,
                    'is_active': p.is_active,
                    'network_name': p.network_name
                } for p in active_ports
            ],
            'user_visible_ports': [
                {
                    'port_number': p.port_number,
                    'status': p.status,
                    'is_active': p.is_active,
                    'network_name': p.network_name
                } for p in user_ports
            ],
            'counts': {
                'total_ports': len(all_ports),
                'active_ports': len(active_ports),
                'user_visible_ports': len(user_ports)
            }
        })

    except Exception as e:
        logger.error(f"Error in debug ports: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/user_permissions')
@login_required
def debug_user_permissions():
    """Debug route to check current user permissions"""
    try:
        permissions_to_check = [
            'can_send_sms',
            'can_view_inbox',
            'can_view_outbox',
            'can_export',
            'can_manage_users',
            'can_view_reports',
            'can_manage_settings',
            'can_manage_ports'
        ]

        user_info = {
            'username': current_user.username,
            'role': current_user.role,
            'is_active': current_user.is_active,
            'assigned_ports': current_user.get_assigned_ports(),
            'permissions': {}
        }

        for perm in permissions_to_check:
            user_info['permissions'][perm] = {
                'has_permission': current_user.has_permission(perm),
                'direct_attribute': getattr(current_user, perm, None)
            }

        return jsonify({
            'success': True,
            'user_info': user_info
        })

    except Exception as e:
        logger.error(f"Error checking user permissions: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/code_reload_test')
def debug_code_reload_test():
    """Test route to verify code reloading"""
    return jsonify({
        'message': f'NEW CODE IS LOADED - CACHE_BUSTER: {CACHE_BUSTER}',
        'timestamp': time.time(),
        'database_check': {
            'gateways': GSMGateway.query.count(),
            'ports': SMSPort.query.count(),
            'gateway_max_ports': GSMGateway.query.first().max_ports if GSMGateway.query.first() else 'No gateway'
        }
    })

@app.route('/force_restart')
def force_restart():
    """Force restart the Flask app"""
    import sys
    import os

    # This will cause the Flask app to restart in debug mode
    os._exit(0)

@app.route('/gsm_gateways_fixed')
@login_required
@require_permission('can_manage_settings')
def gsm_gateways_fixed():
    """FIXED GSM Gateway Management page - bypasses all caching"""
    try:
        logger.info("🔧 [FIXED ROUTE] Loading GSM Gateway management page...")

        # Force ensure we have the correct gateway setup
        gateways = GSMGateway.query.all()
        logger.info(f"📋 [FIXED ROUTE] Found {len(gateways)} gateways in database")

        # Force update or create gateway with 8 ports
        if not gateways:
            logger.info("🔧 [FIXED ROUTE] Creating main gateway with 8 ports...")
            sms_config = get_sms_config()
            tg_config = get_tg_config()

            main_gateway = GSMGateway(
                name='Main Gateway',
                ip_address=sms_config['ip'],
                api_port='80',
                tg_sms_port=str(tg_config['port']),
                username=sms_config['account'],
                password=sms_config['password'],
                max_ports=8,  # FORCE 8 ports
                status='active',
                location='Main Office',
                description='Primary GSM gateway with 8 ports',
                is_primary=True
            )
            db.session.add(main_gateway)
            db.session.commit()
            gateways = [main_gateway]
        else:
            # Force update existing gateway to have 8 ports
            gateway = gateways[0]
            if gateway.max_ports != 8:
                logger.info(f"🔧 [FIXED ROUTE] Updating gateway max_ports from {gateway.max_ports} to 8")
                gateway.max_ports = 8
                db.session.commit()

        # Ensure we have 8 active ports
        for i in range(1, 9):
            port = SMSPort.query.filter_by(port_number=str(i)).first()
            if not port:
                port = SMSPort(
                    port_number=str(i),
                    status='detected',
                    is_active=True,
                    gateway_id=gateways[0].id
                )
                db.session.add(port)
        db.session.commit()

        # Convert to dict format for template - FORCE CORRECT DATA
        gateway_list = []
        for gateway in gateways:
            gateway_dict = gateway.to_dict()
            gateway_dict['is_connected'] = True

            # FORCE OVERRIDE: Ensure max_ports is 8 in the dict
            gateway_dict['max_ports'] = 8

            logger.info(f"📊 [FIXED ROUTE] Gateway {gateway.name}: max_ports = {gateway_dict['max_ports']}")

            # Add port status method
            def get_port_status(port_num, gw_id=gateway.id):
                port = SMSPort.query.filter_by(gateway_id=gw_id, port_number=str(port_num)).first()
                if port and port.is_active:
                    return 'active' if port.status in ['active', 'detected'] else 'inactive'
                return 'inactive'

            gateway_dict['get_port_status'] = get_port_status
            gateway_list.append(gateway_dict)

        # Calculate statistics
        active_gateways = len([g for g in gateway_list if g['status'] == 'active'])
        total_ports = sum(g['max_ports'] for g in gateway_list)
        active_ports = SMSPort.query.filter_by(is_active=True).count()

        logger.info(f"📈 [FIXED ROUTE] Statistics: {active_gateways} active gateways, {total_ports} total ports, {active_ports} active ports")

        # Create response with cache-busting headers
        response = make_response(render_template('gsm_gateways.html',
                                               gateways=gateway_list,
                                               active_gateways=active_gateways,
                                               total_ports=total_ports,
                                               active_ports=active_ports))

        # Add cache-busting headers
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        return response

    except Exception as e:
        logger.error(f"Error loading GSM gateways: {str(e)}")
        flash('Error loading GSM gateways', 'error')
        return redirect(url_for('index'))

@app.route('/debug/test_login/<username>')
def debug_test_login(username):
    """Debug route to test login for a specific user (DEVELOPMENT ONLY)"""
    try:
        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({
                'success': False,
                'error': f'User {username} not found'
            }), 404

        if not user.is_active:
            return jsonify({
                'success': False,
                'error': f'User {username} is not active'
            }), 403

        # Login the user
        login_user(user)

        return jsonify({
            'success': True,
            'message': f'Successfully logged in as {username}',
            'user_info': {
                'username': user.username,
                'role': user.role,
                'assigned_ports': user.get_assigned_ports(),
                'permissions': {
                    'can_send_sms': user.can_send_sms,
                    'can_view_inbox': user.can_view_inbox,
                    'can_view_outbox': user.can_view_outbox,
                    'can_export': user.can_export,
                    'can_manage_settings': user.can_manage_settings
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in debug test login: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/test_inbox_query')
def debug_test_inbox_query():
    """Debug route to test inbox query without authentication"""
    try:
        logger.info("🔍 Testing inbox query directly...")

        # Test the exact same query as the inbox route
        page = 1
        per_page = 20

        messages = SMSMessage.query.filter_by(direction='inbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        logger.info(f"📊 Inbox query results:")
        logger.info(f"   Total messages: {messages.total}")
        logger.info(f"   Current page: {messages.page}")
        logger.info(f"   Messages on this page: {len(messages.items)}")

        # Convert messages to dict for JSON response
        message_list = []
        for msg in messages.items:
            message_list.append({
                'id': msg.id,
                'message_id': msg.message_id,
                'direction': msg.direction,
                'phone_number': msg.phone_number,
                'content': msg.content,
                'status': msg.status,
                'created_at': msg.created_at.isoformat() if msg.created_at else None,
                'gsm_port': msg.gsm_port
            })

        return jsonify({
            'success': True,
            'query_results': {
                'total': messages.total,
                'page': messages.page,
                'pages': messages.pages,
                'per_page': messages.per_page,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev,
                'messages': message_list
            },
            'raw_inbound_count': SMSMessage.query.filter_by(direction='inbound').count(),
            'all_messages_count': SMSMessage.query.count()
        })

    except Exception as e:
        logger.error(f"Error testing inbox query: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/debug/test_inbox_page')
def debug_test_inbox_page():
    """Debug route to test inbox page rendering without authentication"""
    try:
        logger.info("🔍 Testing inbox page rendering directly...")

        # Test the exact same logic as the inbox route
        page = 1
        per_page = 20

        messages = SMSMessage.query.filter_by(direction='inbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        logger.info(f"📊 Inbox page test results:")
        logger.info(f"   Messages object: {messages}")
        logger.info(f"   Messages.items: {messages.items}")
        logger.info(f"   Total messages: {messages.total}")
        logger.info(f"   Messages on this page: {len(messages.items)}")

        # Render the inbox template directly
        return render_template('inbox.html', messages=messages)

    except Exception as e:
        logger.error(f"Error testing inbox page: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return f"<h1>Error testing inbox page</h1><pre>{str(e)}\n\n{traceback.format_exc()}</pre>"

@app.route('/debug/check_user_permissions')
def debug_check_user_permissions():
    """Debug route to check current user permissions"""
    try:
        if current_user.is_authenticated:
            user_info = {
                'authenticated': True,
                'username': current_user.username,
                'email': current_user.email,
                'role': current_user.role,
                'is_active': current_user.is_active,
                'permissions': {
                    'can_send_sms': current_user.can_send_sms,
                    'can_view_inbox': current_user.can_view_inbox,
                    'can_view_outbox': current_user.can_view_outbox,
                    'can_export': current_user.can_export,
                    'can_manage_users': current_user.can_manage_users,
                    'can_view_reports': current_user.can_view_reports,
                    'can_manage_settings': current_user.can_manage_settings,
                    'can_manage_ports': current_user.can_manage_ports,
                },
                'has_view_inbox_permission': current_user.has_permission('view_inbox')
            }
        else:
            user_info = {
                'authenticated': False,
                'message': 'User is not logged in'
            }

        return jsonify({
            'success': True,
            'user_info': user_info
        })

    except Exception as e:
        logger.error(f"Error checking user permissions: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/check_all_messages')
def debug_check_all_messages():
    """Debug route to check ALL messages in database regardless of direction"""
    try:
        # Get ALL messages
        all_messages = SMSMessage.query.order_by(SMSMessage.created_at.desc()).all()

        # Group by direction
        inbound_messages = [msg for msg in all_messages if msg.direction == 'inbound']
        outbound_messages = [msg for msg in all_messages if msg.direction == 'outbound']
        other_messages = [msg for msg in all_messages if msg.direction not in ['inbound', 'outbound']]

        # Convert to dict for JSON response
        def message_to_dict(msg):
            return {
                'id': msg.id,
                'message_id': msg.message_id,
                'direction': msg.direction,
                'phone_number': msg.phone_number,
                'content': msg.content[:100] + '...' if len(msg.content) > 100 else msg.content,
                'status': msg.status,
                'created_at': msg.created_at.isoformat() if msg.created_at else None,
                'gsm_port': msg.gsm_port,
                'smsc': msg.smsc
            }

        return jsonify({
            'success': True,
            'summary': {
                'total_messages': len(all_messages),
                'inbound_count': len(inbound_messages),
                'outbound_count': len(outbound_messages),
                'other_direction_count': len(other_messages)
            },
            'all_messages': [message_to_dict(msg) for msg in all_messages],
            'inbound_messages': [message_to_dict(msg) for msg in inbound_messages],
            'outbound_messages': [message_to_dict(msg) for msg in outbound_messages],
            'other_messages': [message_to_dict(msg) for msg in other_messages] if other_messages else []
        })

    except Exception as e:
        logger.error(f"Error checking all messages: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/debug/test_tg_connection')
def debug_test_tg_connection():
    """Debug route to test TG SMS server connectivity"""
    try:
        import socket

        tg_config = get_tg_config()
        host = tg_config['ip']
        port = tg_config['port']

        logger.info(f"🔍 Testing connection to TG SMS server {host}:{port}")

        # Test basic TCP connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5 second timeout

        try:
            result = sock.connect_ex((host, port))
            sock.close()

            if result == 0:
                connection_status = "✅ TCP connection successful"
                can_connect = True
            else:
                connection_status = f"❌ TCP connection failed (error code: {result})"
                can_connect = False

        except Exception as e:
            connection_status = f"❌ TCP connection error: {str(e)}"
            can_connect = False

        # Check current receiver status
        receiver_status = {
            'connected': sms_receiver.connected,
            'running': sms_receiver.running,
            'socket_exists': sms_receiver.socket is not None
        }

        return jsonify({
            'success': True,
            'tg_config': tg_config,
            'connection_test': {
                'host': host,
                'port': port,
                'status': connection_status,
                'can_connect': can_connect
            },
            'receiver_status': receiver_status,
            'recommendations': [
                "Check if Yeastar TG SMS server is running" if not can_connect else "TG SMS server is reachable",
                "Check firewall settings for port 5038" if not can_connect else "Port 5038 is accessible",
                "Verify TG SMS server configuration" if not can_connect else "Network connectivity is OK",
                "Try manual reconnection" if can_connect and not receiver_status['connected'] else "Connection status looks good"
            ]
        })

    except Exception as e:
        logger.error(f"Error testing TG connection: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

# API Routes for AJAX calls
@app.route('/api/sms/inbox', methods=['GET'])
def get_inbox():
    """Get inbox messages (received SMS)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        messages = SMSMessage.query.filter_by(direction='inbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'messages': [msg.to_dict() for msg in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page
        })

    except Exception as e:
        logger.error(f"Error in get_inbox: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/outbox', methods=['GET'])
def get_outbox():
    """Get outbox messages (sent SMS)"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        messages = SMSMessage.query.filter_by(direction='outbound')\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'messages': [msg.to_dict() for msg in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page
        })

    except Exception as e:
        logger.error(f"Error in get_outbox: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/message/<message_id>', methods=['GET'])
def get_message(message_id):
    """Get specific message details"""
    try:
        message = SMSMessage.query.filter_by(message_id=message_id).first()

        if not message:
            return jsonify({'error': 'Message not found'}), 404

        return jsonify(message.to_dict())

    except Exception as e:
        logger.error(f"Error in get_message: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/ports', methods=['GET'])
def get_ports():
    """Get SMS port status"""
    try:
        ports = SMSPort.query.all()
        return jsonify([port.to_dict() for port in ports])

    except Exception as e:
        logger.error(f"Error in get_ports: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/stats', methods=['GET'])
def get_stats():
    """Get SMS statistics"""
    try:
        total_sent = SMSMessage.query.filter_by(direction='outbound').count()
        total_received = SMSMessage.query.filter_by(direction='inbound').count()
        pending_messages = SMSMessage.query.filter_by(status='pending').count()
        failed_messages = SMSMessage.query.filter_by(status='failed').count()

        return jsonify({
            'total_sent': total_sent,
            'total_received': total_received,
            'pending_messages': pending_messages,
            'failed_messages': failed_messages,
            'total_messages': total_sent + total_received
        })

    except Exception as e:
        logger.error(f"Error in get_stats: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sms/conversation/<phone_number>')
def get_conversation(phone_number):
    """Get conversation history with a specific phone number"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 50

        messages = SMSMessage.query.filter_by(phone_number=phone_number)\
                                 .order_by(SMSMessage.created_at.desc())\
                                 .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'success': True,
            'phone_number': phone_number,
            'messages': [msg.to_dict() for msg in messages.items],
            'pagination': {
                'page': messages.page,
                'pages': messages.pages,
                'per_page': messages.per_page,
                'total': messages.total,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev
            }
        })
    except Exception as e:
        logger.error(f"Error getting conversation: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sms/stats')
@login_required
def get_sms_stats():
    """Get SMS statistics for export page"""
    try:
        total_sent = SMSMessage.query.filter_by(direction='outbound').count()
        total_received = SMSMessage.query.filter_by(direction='inbound').count()
        total_messages = total_sent + total_received

        return jsonify({
            'success': True,
            'total_messages': total_messages,
            'total_sent': total_sent,
            'total_received': total_received
        })
    except Exception as e:
        logger.error(f"Error getting SMS stats: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500



# Export and Reporting Routes
@app.route('/export')
@login_required
@require_permission('can_export')
def export_page():
    """Export page"""
    return render_template('export.html')

@app.route('/export/messages')
@login_required
@require_permission('can_export')
def export_messages():
    """Export messages to CSV"""
    try:
        # Get filter parameters
        direction = request.args.get('direction', 'all')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        phone_number = request.args.get('phone_number')

        # Build query with port filtering
        query = SMSMessage.query

        # Filter by assigned ports if user is not admin
        if current_user.role != 'admin':
            assigned_ports = current_user.get_assigned_ports()
            if assigned_ports:  # If user has specific port assignments
                query = query.filter(SMSMessage.gsm_port.in_(assigned_ports))

        if direction != 'all':
            query = query.filter_by(direction=direction)

        if start_date:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(SMSMessage.created_at >= start_dt)

        if end_date:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            end_dt = end_dt.replace(hour=23, minute=59, second=59)
            query = query.filter(SMSMessage.created_at <= end_dt)

        if phone_number:
            query = query.filter(SMSMessage.phone_number.contains(phone_number))

        messages = query.order_by(SMSMessage.created_at.desc()).all()

        # Create CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'Message ID', 'Direction', 'Phone Number', 'Content',
            'Status', 'Created At', 'GSM Port', 'SMSC', 'Reply To'
        ])

        # Write data
        for msg in messages:
            writer.writerow([
                msg.message_id,
                msg.direction,
                msg.phone_number,
                msg.content,
                msg.status,
                msg.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                msg.gsm_port or '',
                msg.smsc or '',
                msg.reply_to_id or ''
            ])

        # Create response
        output.seek(0)
        filename = f"sms_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        return app.response_class(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )

    except Exception as e:
        logger.error(f"Error exporting messages: {str(e)}")
        flash('Error exporting messages', 'error')
        return redirect(url_for('export_page'))

@app.route('/reports')
@login_required
@require_permission('view_reports')
def reports():
    """Reports page"""
    try:
        # Get date range (default to last 30 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        # Get statistics
        total_sent = SMSMessage.query.filter_by(direction='outbound').count()
        total_received = SMSMessage.query.filter_by(direction='inbound').count()

        # Messages by day (last 30 days)
        daily_stats = []
        for i in range(30):
            day = start_date + timedelta(days=i)
            day_start = day.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day.replace(hour=23, minute=59, second=59, microsecond=999999)

            sent_count = SMSMessage.query.filter(
                SMSMessage.direction == 'outbound',
                SMSMessage.created_at >= day_start,
                SMSMessage.created_at <= day_end
            ).count()

            received_count = SMSMessage.query.filter(
                SMSMessage.direction == 'inbound',
                SMSMessage.created_at >= day_start,
                SMSMessage.created_at <= day_end
            ).count()

            daily_stats.append({
                'date': day.strftime('%Y-%m-%d'),
                'sent': sent_count,
                'received': received_count
            })

        # Top contacts
        from sqlalchemy import func
        top_contacts = db.session.query(
            SMSMessage.phone_number,
            func.count(SMSMessage.id).label('message_count')
        ).group_by(SMSMessage.phone_number)\
         .order_by(func.count(SMSMessage.id).desc())\
         .limit(10).all()

        # Port usage
        port_usage = db.session.query(
            SMSMessage.gsm_port,
            func.count(SMSMessage.id).label('usage_count')
        ).filter(SMSMessage.gsm_port.isnot(None))\
         .group_by(SMSMessage.gsm_port)\
         .order_by(func.count(SMSMessage.id).desc()).all()

        return render_template('reports.html',
                             total_sent=total_sent,
                             total_received=total_received,
                             daily_stats=daily_stats,
                             top_contacts=top_contacts,
                             port_usage=port_usage)

    except Exception as e:
        logger.error(f"Error loading reports: {str(e)}")
        return render_template('reports.html',
                             total_sent=0,
                             total_received=0,
                             daily_stats=[],
                             top_contacts=[],
                             port_usage=[])

@app.route('/users')
@login_required
@require_permission('manage_users')
def users():
    """User management page"""
    try:
        users = User.query.all()
        return render_template('users.html', users=users)
    except Exception as e:
        logger.error(f"Error loading users: {str(e)}")
        return render_template('users.html', users=[])

@app.route('/users/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_user():
    """Create new user"""
    if request.method == 'POST':
        try:
            username = request.form.get('username')
            email = request.form.get('email')
            password = request.form.get('password')
            role = request.form.get('role', 'user')

            # Get permissions
            permissions = {
                'can_send_sms': 'can_send_sms' in request.form,
                'can_view_inbox': 'can_view_inbox' in request.form,
                'can_view_outbox': 'can_view_outbox' in request.form,
                'can_export': 'can_export' in request.form,
                'can_manage_users': 'can_manage_users' in request.form,
                'can_view_reports': 'can_view_reports' in request.form,
                'can_manage_settings': 'can_manage_settings' in request.form,
                'can_manage_ports': 'can_manage_ports' in request.form,
            }

            # Get assigned ports
            assigned_ports = request.form.getlist('assigned_ports')

            # Validate
            if not username or not email or not password:
                flash('Username, email, and password are required', 'error')
                return render_template('create_user.html')

            # Check if user exists
            if User.query.filter_by(username=username).first():
                flash('Username already exists', 'error')
                return render_template('create_user.html')

            if User.query.filter_by(email=email).first():
                flash('Email already exists', 'error')
                return render_template('create_user.html')

            # Create user
            user = User(
                username=username,
                email=email,
                role=role,
                **permissions
            )
            user.set_password(password)
            user.set_assigned_ports(assigned_ports)

            db.session.add(user)
            db.session.commit()

            flash(f'User {username} created successfully', 'success')
            return redirect(url_for('users'))

        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            flash('Error creating user', 'error')

    # Get ONLY the original 4 ports for assignment
    try:
        logger.info("🔍 Loading ONLY original 4 ports for user creation...")

        # Get ONLY the main gateway (ID=1)
        main_gateway = GSMGateway.query.filter_by(id=1).first()
        if not main_gateway:
            logger.error("❌ Main gateway not found!")
            flash('Error: Main gateway not found', 'error')
            return render_template('create_user.html', ports=[], gateway_info=None, gateways=[])

        # Get ALL 8 ports from the main gateway
        all_ports = SMSPort.query.filter(
            SMSPort.gateway_id == 1,
            SMSPort.port_number.in_(['1', '2', '3', '4', '5', '6', '7', '8'])
        ).order_by(SMSPort.port_number.asc()).all()

        logger.info(f"📱 Found {len(all_ports)} ports: {[p.port_number for p in all_ports]}")

        # Build gateway data with ALL 8 ports
        gateway_info = {
            'id': main_gateway.id,
            'name': main_gateway.name,
            'ip_address': main_gateway.ip_address,
            'total_ports': 8,  # Show all 8 ports
            'active_ports': len([p for p in all_ports if p.status in ['active', 'detected']]),
            'ports': all_ports
        }

        logger.info(f"✅ Loaded main gateway with {len(all_ports)} ports (1-8)")

    except Exception as e:
        logger.error(f"Error querying SMS ports: {str(e)}")
        gateway_info = None
        flash('Warning: Could not load port information', 'warning')

    return render_template('create_user.html',
                         ports=[],  # Don't pass individual ports
                         gateway_info=gateway_info,
                         gateways=[gateway_info] if gateway_info else [])

@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    """Edit user"""
    user = User.query.get_or_404(user_id)

    if request.method == 'POST':
        try:
            user.email = request.form.get('email')
            user.role = request.form.get('role', 'user')
            user.is_active = request.form.get('is_active') == 'true'

            # Update permissions
            user.can_send_sms = 'can_send_sms' in request.form
            user.can_view_inbox = 'can_view_inbox' in request.form
            user.can_view_outbox = 'can_view_outbox' in request.form
            user.can_export = 'can_export' in request.form
            user.can_manage_users = 'can_manage_users' in request.form
            user.can_view_reports = 'can_view_reports' in request.form
            user.can_manage_settings = 'can_manage_settings' in request.form
            user.can_manage_ports = 'can_manage_ports' in request.form

            # Update assigned ports
            assigned_ports = request.form.getlist('assigned_ports')
            user.set_assigned_ports(assigned_ports)

            db.session.commit()
            flash(f'User {user.username} updated successfully', 'success')
            return redirect(url_for('users'))

        except Exception as e:
            logger.error(f"Error updating user: {str(e)}")
            flash('Error updating user', 'error')

    try:
        logger.info("🔍 Loading ONLY original 4 ports for user editing...")

        # Get ONLY the main gateway (ID=1)
        main_gateway = GSMGateway.query.filter_by(id=1).first()
        if not main_gateway:
            logger.error("❌ Main gateway not found!")
            flash('Error: Main gateway not found', 'error')
            return render_template('edit_user.html', user=user, ports=[], gateway_info=None, gateways=[])

        # Get ALL 8 ports from the main gateway
        all_ports = SMSPort.query.filter(
            SMSPort.gateway_id == 1,
            SMSPort.port_number.in_(['1', '2', '3', '4', '5', '6', '7', '8'])
        ).order_by(SMSPort.port_number.asc()).all()

        logger.info(f"📱 Found {len(all_ports)} ports: {[p.port_number for p in all_ports]}")

        # Build gateway data with ALL 8 ports
        gateway_info = {
            'id': main_gateway.id,
            'name': main_gateway.name,
            'ip_address': main_gateway.ip_address,
            'total_ports': 8,  # Show all 8 ports
            'active_ports': len([p for p in all_ports if p.status in ['active', 'detected']]),
            'ports': all_ports
        }

        logger.info(f"✅ Loaded main gateway with {len(all_ports)} ports (1-8)")

    except Exception as e:
        logger.error(f"Error querying SMS ports: {str(e)}")
        gateway_info = None
        flash('Warning: Could not load port information', 'warning')

    return render_template('edit_user.html',
                         user=user,
                         ports=[],  # Don't pass individual ports
                         gateway_info=gateway_info,
                         gateways=[gateway_info] if gateway_info else [])

@app.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    """Delete user"""
    try:
        user = User.query.get_or_404(user_id)

        # Prevent deleting admin user
        if user.username == 'admin':
            flash('Cannot delete the admin user', 'error')
            return redirect(url_for('users'))

        # Prevent deleting current user
        if user.id == current_user.id:
            flash('Cannot delete your own account', 'error')
            return redirect(url_for('users'))

        username = user.username
        db.session.delete(user)
        db.session.commit()

        flash(f'User {username} deleted successfully', 'success')

    except Exception as e:
        logger.error(f"Error deleting user: {str(e)}")
        flash('Error deleting user', 'error')

    return redirect(url_for('users'))

@app.route('/users/<int:user_id>/toggle_status', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """Toggle user active status"""
    try:
        user = User.query.get_or_404(user_id)

        # Prevent deactivating admin user
        if user.username == 'admin':
            flash('Cannot deactivate the admin user', 'error')
            return redirect(url_for('users'))

        user.is_active = not user.is_active
        db.session.commit()

        status = 'activated' if user.is_active else 'deactivated'
        flash(f'User {user.username} {status} successfully', 'success')

    except Exception as e:
        logger.error(f"Error toggling user status: {str(e)}")
        flash('Error updating user status', 'error')

    return redirect(url_for('users'))

@app.route('/ports')
@login_required
@require_permission('can_manage_ports')
def ports():
    """Port management page"""
    try:
        # Get all ports with their status
        ports = SMSPort.query.order_by(SMSPort.port_number.asc()).all()

        # Get port statistics
        port_stats = {}
        for port in ports:
            port_stats[port.port_number] = {
                'total_messages': SMSMessage.query.filter_by(gsm_port=port.port_number).count(),
                'inbound_messages': SMSMessage.query.filter_by(gsm_port=port.port_number, direction='inbound').count(),
                'outbound_messages': SMSMessage.query.filter_by(gsm_port=port.port_number, direction='outbound').count(),
                'assigned_users': User.query.filter(User.assigned_ports.like(f'%{port.port_number}%')).count()
            }

        # Get available port range
        sms_config = get_sms_config()
        max_ports = sms_config.get('max_ports', 8)

        return render_template('ports.html',
                             ports=ports,
                             port_stats=port_stats,
                             max_ports=max_ports)
    except Exception as e:
        logger.error(f"Error loading ports: {str(e)}")
        return render_template('ports.html', ports=[], port_stats={}, max_ports=8)

@app.route('/ports/detect', methods=['POST'])
@login_required
@require_permission('can_manage_ports')
def detect_ports():
    """Detect and initialize GSM ports"""
    try:
        detected_ports = GSMPortManager.initialize_ports()
        flash(f'Successfully detected and initialized {len(detected_ports)} GSM ports: {", ".join(map(str, detected_ports))}', 'success')
    except Exception as e:
        logger.error(f"Error detecting ports: {str(e)}")
        flash('Error detecting GSM ports', 'error')

    return redirect(url_for('ports'))

@app.route('/ports/configure', methods=['POST'])
@login_required
@require_permission('can_manage_ports')
def configure_ports():
    """Configure maximum number of ports"""
    try:
        max_ports = int(request.form.get('max_ports', 8))

        # Validate range
        if max_ports < 1 or max_ports > 32:
            flash('Maximum ports must be between 1 and 32', 'error')
            return redirect(url_for('ports'))

        # Update configuration - now handled by gateway configuration
        # No need to update global config as we use dynamic configuration

        # Re-initialize ports with new configuration
        detected_ports = GSMPortManager.initialize_ports()

        flash(f'Port configuration updated: {max_ports} maximum ports. Detected {len(detected_ports)} active ports.', 'success')

    except ValueError:
        flash('Invalid port number', 'error')
    except Exception as e:
        logger.error(f"Error configuring ports: {str(e)}")
        flash('Error configuring ports', 'error')

    return redirect(url_for('ports'))

@app.route('/ports/<port_number>/toggle', methods=['POST'])
@login_required
@require_permission('can_manage_ports')
def toggle_port_status(port_number):
    """Toggle port active status"""
    try:
        port = SMSPort.query.filter_by(port_number=port_number).first_or_404()
        port.is_active = not port.is_active
        db.session.commit()

        status = 'activated' if port.is_active else 'deactivated'
        flash(f'Port {port_number} {status} successfully', 'success')

    except Exception as e:
        logger.error(f"Error toggling port status: {str(e)}")
        flash('Error updating port status', 'error')

    return redirect(url_for('ports'))

# Enhanced GSM Gateway Management Route
@app.route('/gsm_gateways')
@login_required
@require_permission('can_manage_settings')
def gsm_gateways():
    """Enhanced GSM Gateway management page with real-time monitoring"""
    try:
        logger.info("🔍 Enhanced GSM Gateway management route called")

        # Get all gateways
        gateways = GSMGateway.query.all()

        # Calculate statistics
        active_gateways = len([gw for gw in gateways if gw.status == 'active'])
        total_ports = sum(gw.max_ports for gw in gateways)

        # Get active ports count
        active_ports = 0
        for gateway in gateways:
            ports = SMSPort.query.filter_by(gateway_id=gateway.id, status='active').count()
            active_ports += ports

        logger.info(f"📊 Gateway stats: {len(gateways)} total, {active_gateways} active, {total_ports} total ports, {active_ports} active ports")

        return render_template('gsm_management.html',
                             gateways=gateways,
                             active_gateways=active_gateways,
                             total_ports=total_ports,
                             active_ports=active_ports)

    except Exception as e:
        logger.error(f"Error loading GSM gateway: {str(e)}")
        flash('Error loading GSM gateway', 'error')
        return render_template('gsm_management.html',
                             gateways=[],
                             active_gateways=0,
                             total_ports=0,
                             active_ports=0)

@app.route('/api/gateway/<int:gateway_id>/ports')
@login_required
@require_permission('can_manage_settings')
def get_gateway_ports(gateway_id):
    """API endpoint to get real-time port status for a gateway"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)

        # Perform real port detection
        detected_ports = GSMPortManager.detect_available_ports(gateway_id)

        return jsonify({
            'success': True,
            'gateway_id': gateway_id,
            'gateway_name': gateway.name,
            'ports': detected_ports
        })
    except Exception as e:
        logger.error(f"Error getting ports for gateway {gateway_id}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/gsm_gateways/add', methods=['GET', 'POST'])
@login_required
@require_permission('can_manage_settings')
def add_gsm_gateway():
    """Add new GSM gateway"""
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name')
            ip_address = request.form.get('ip_address')
            api_port = request.form.get('api_port', '80')
            tg_sms_port = request.form.get('tg_sms_port', '5038')
            username = request.form.get('username')
            password = request.form.get('password')
            max_ports = int(request.form.get('max_ports', 8))
            location = request.form.get('location', '')
            description = request.form.get('description', '')
            status = request.form.get('status', 'active')
            is_primary = 'is_primary' in request.form
            test_connection = 'testConnection' in request.form

            # Validate required fields
            if not all([name, ip_address, username, password]):
                error_msg = 'Name, IP address, username, and password are required'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.is_json:
                    return jsonify({'success': False, 'error': error_msg})
                else:
                    flash(error_msg, 'error')
                    return redirect(url_for('gsm_gateways'))

            # Check if gateway with same IP already exists
            existing_gateway = GSMGateway.query.filter_by(ip_address=ip_address).first()
            if existing_gateway:
                error_msg = f'Gateway with IP address {ip_address} already exists'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.is_json:
                    return jsonify({'success': False, 'error': error_msg})
                else:
                    flash(error_msg, 'error')
                    return redirect(url_for('gsm_gateways'))

            # Create new gateway
            new_gateway = GSMGateway(
                name=name,
                ip_address=ip_address,
                api_port=api_port,
                tg_sms_port=tg_sms_port,
                username=username,
                password=password,
                max_ports=max_ports,
                location=location,
                description=description,
                status=status,
                is_primary=is_primary
            )

            # If this is set as primary, unset other primary gateways
            if is_primary:
                GSMGateway.query.filter_by(is_primary=True).update({'is_primary': False})

            db.session.add(new_gateway)
            db.session.flush()  # Get the gateway ID

            # Create ports for this gateway
            for port_num in range(1, max_ports + 1):
                new_port = SMSPort(
                    port_number=str(port_num),  # Simple port number (1, 2, 3, etc.)
                    status='detected',
                    network_name='Unknown',
                    signal_quality='Unknown',
                    is_active=True,
                    gateway_id=new_gateway.id
                )
                db.session.add(new_port)

            db.session.commit()

            logger.info(f'Gateway "{name}" added successfully with {max_ports} ports')

            # If test connection is requested
            if test_connection:
                logger.info(f'Connection test for {ip_address} would be performed')

            # Return JSON response for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.is_json:
                return jsonify({
                    'success': True,
                    'message': f'Gateway "{name}" added successfully with {max_ports} ports',
                    'gateway_id': new_gateway.id
                })
            else:
                flash(f'Gateway "{name}" added successfully with {max_ports} ports', 'success')
                return redirect(url_for('gsm_gateways'))

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error adding GSM gateway: {str(e)}")
            error_msg = f'Error adding GSM gateway: {str(e)}'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.is_json:
                return jsonify({'success': False, 'error': error_msg})
            else:
                flash('Error adding GSM gateway', 'error')
                return redirect(url_for('gsm_gateways'))

    return redirect(url_for('gsm_gateways'))

@app.route('/gsm_gateways/<int:gateway_id>/delete', methods=['POST'])
@login_required
@require_permission('can_manage_settings')
def delete_gsm_gateway(gateway_id):
    """Delete GSM gateway"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)

        # Check if this is the primary gateway
        if gateway.is_primary:
            error_msg = 'Cannot delete primary gateway. Set another gateway as primary first.'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': error_msg})
            else:
                flash(error_msg, 'error')
                return redirect(url_for('gsm_gateways'))

        # Delete associated ports first
        SMSPort.query.filter_by(gateway_id=gateway_id).delete()

        # Delete the gateway
        gateway_name = gateway.name
        db.session.delete(gateway)
        db.session.commit()

        logger.info(f'Gateway "{gateway_name}" deleted successfully')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success': True,
                'message': f'Gateway "{gateway_name}" deleted successfully'
            })
        else:
            flash(f'Gateway "{gateway_name}" deleted successfully', 'success')
            return redirect(url_for('gsm_gateways'))

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting gateway {gateway_id}: {str(e)}")
        error_msg = f'Error deleting gateway: {str(e)}'
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'error': error_msg})
        else:
            flash('Error deleting gateway', 'error')
            return redirect(url_for('gsm_gateways'))

@app.route('/gsm_gateways/<int:gateway_id>/configure')
@login_required
@require_permission('can_manage_settings')
def configure_gsm_gateway(gateway_id):
    """Configure GSM gateway"""
    try:
        # Get actual detected ports from the database
        detected_ports = SMSPort.query.filter_by(is_active=True).all()

        # Generate port configuration cards dynamically
        port_cards = ""
        for port in detected_ports:
            port_num = port.port_number
            status_class = "success" if port.status in ['active', 'detected'] else "warning"
            status_badge = "success" if port.status in ['active', 'detected'] else "warning"
            status_text = "Active" if port.status in ['active', 'detected'] else "Inactive"
            network = port.network_name if port.network_name and port.network_name != 'Unknown' else "-"
            signal = port.signal_quality if port.signal_quality and port.signal_quality != 'Unknown' else "-"

            # Use 3 columns per row for better layout with 8 ports
            col_class = "col-4 mb-3"

            port_cards += f'''
                    <div class="{col_class}">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="text-{status_class}">Port {port_num}</h5>
                                <p class="mb-1">Status: <span class="badge bg-{status_badge}">{status_text}</span></p>
                                <p class="mb-1">Network: {network}</p>
                                <p class="mb-0">Signal: {signal}</p>
                                <button class="btn btn-sm btn-outline-primary mt-2">Configure</button>
                            </div>
                        </div>
                    </div>'''

        # Return configuration interface HTML with actual port data
        config_html = f"""
        <div class="row">
            <div class="col-md-6">
                <h6><i class="bi bi-gear"></i> Gateway Settings</h6>
                <form id="gatewayConfigForm">
                    <div class="mb-3">
                        <label class="form-label">Gateway Name</label>
                        <input type="text" class="form-control" value="Main Gateway" name="name">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">IP Address</label>
                        <input type="text" class="form-control" value="{get_sms_config()['ip']}" name="ip_address">
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label">API Port</label>
                                <input type="number" class="form-control" value="80" name="api_port">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label">TG SMS Port</label>
                                <input type="number" class="form-control" value="{get_tg_config()['port']}" name="tg_sms_port">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status">
                            <option value="active" selected>Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="maintenance">Maintenance</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check"></i> Save Configuration
                    </button>
                </form>
            </div>
            <div class="col-md-6">
                <h6><i class="bi bi-hdd-stack"></i> Port Configuration ({len(detected_ports)} ports detected)</h6>
                <div class="row">
                    {port_cards}
                </div>
            </div>
        </div>

        <script>
        document.getElementById('gatewayConfigForm').addEventListener('submit', function(e) {{
            e.preventDefault();
            alert('Configuration saved successfully!');
            bootstrap.Modal.getInstance(document.getElementById('configureGatewayModal')).hide();
        }});
        </script>
        """

        return config_html

    except Exception as e:
        logger.error(f"Error configuring gateway {gateway_id}: {str(e)}")
        return f'<div class="alert alert-danger">Error loading configuration: {str(e)}</div>'

@app.route('/gsm_gateways/<int:gateway_id>/test_connection', methods=['POST'])
@login_required
@require_permission('can_manage_settings')
def test_gsm_gateway_connection(gateway_id):
    """Test GSM gateway connection"""
    try:
        # Simulate connection test
        import time
        import random

        start_time = time.time()

        # Simulate some delay
        time.sleep(random.uniform(0.5, 2.0))

        response_time = int((time.time() - start_time) * 1000)

        # Simulate success/failure
        success = random.choice([True, True, True, False])  # 75% success rate

        if success:
            return jsonify({
                'success': True,
                'http_status': 'Connected',
                'tg_sms_status': 'Connected',
                'response_time': response_time
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Connection timeout or gateway unreachable'
            })

    except Exception as e:
        logger.error(f"Error testing gateway {gateway_id} connection: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/gsm_gateways/<int:gateway_id>/refresh', methods=['POST'])
@login_required
@require_permission('can_manage_settings')
def refresh_gsm_gateway(gateway_id):
    """Refresh GSM gateway status"""
    try:
        # Simulate refresh operation
        logger.info(f"Refreshing gateway {gateway_id} status")

        return jsonify({
            'success': True,
            'message': f'Gateway {gateway_id} refreshed successfully'
        })

    except Exception as e:
        logger.error(f"Error refreshing gateway {gateway_id}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/gsm_gateways/refresh_all', methods=['POST'])
@login_required
@require_permission('can_manage_settings')
def refresh_all_gsm_gateways():
    """Refresh all GSM gateways"""
    try:
        logger.info("Refreshing all gateways")

        return jsonify({
            'success': True,
            'message': 'All gateways refreshed successfully'
        })

    except Exception as e:
        logger.error(f"Error refreshing all gateways: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/gsm_gateways/<int:gateway_id>/delete', methods=['POST'])
@login_required
@require_permission('can_manage_settings')
def delete_gsm_gateway(gateway_id):
    """Delete GSM gateway"""
    try:
        logger.info(f"Deleting gateway {gateway_id}")

        return jsonify({
            'success': True,
            'message': f'Gateway {gateway_id} deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error deleting gateway {gateway_id}: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Initialize database
def init_db():
    """Initialize database tables"""
    with app.app_context():
        db.create_all()

        # Create default admin user if no users exist
        if User.query.count() == 0:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin',
                can_send_sms=True,
                can_view_inbox=True,
                can_view_outbox=True,
                can_export=True,
                can_manage_users=True,
                can_view_reports=True,
                can_manage_settings=True,
                can_manage_ports=True
            )
            admin_user.set_password('admin123')  # Change this in production!

            # Create test user with limited permissions
            test_user = User(
                username='testuser',
                email='<EMAIL>',
                role='user',
                can_send_sms=True,
                can_view_inbox=True,
                can_view_outbox=True,
                can_export=False,
                can_manage_users=False,
                can_view_reports=False,
                can_manage_settings=False,
                can_manage_ports=False
            )
            test_user.set_password('test123')
            test_user.set_assigned_ports(['1', '2'])  # Assign ports 1 and 2

            db.session.add(admin_user)
            db.session.add(test_user)
            db.session.commit()

            logger.info("Created default admin user: admin/admin123")
            logger.info("Created test user: testuser/test123 (assigned to ports 1,2)")

        # Original single GSM gateway system - database already set up by removal script
        logger.info("✅ Original single GSM gateway system ready")

        # Verify the original setup exists
        gateway = GSMGateway.query.filter_by(name='Main Gateway').first()
        ports = SMSPort.query.filter_by(gateway_id=gateway.id if gateway else None).all()

        logger.info(f"✅ Verified: {len([gateway] if gateway else [])} gateway, {len(ports)} ports")

        logger.info("Database initialized")

# Start SMS receiver in background thread
def start_sms_receiver():
    """Start SMS receiver in background thread"""
    def receiver_thread():
        try:
            sms_receiver.start_listening()
        except Exception as e:
            logger.error(f"SMS receiver thread error: {str(e)}")

    thread = threading.Thread(target=receiver_thread, daemon=True)
    thread.start()
    logger.info("SMS receiver thread started")

if __name__ == '__main__':
    # Initialize database first
    init_db()

    # Log current configuration using dynamic config
    logger.info("=== SMS Management System Starting ===")
    try:
        sms_config = get_sms_config()
        tg_config = get_tg_config()
        logger.info(f"SMS API IP: {sms_config['ip']}")
        logger.info(f"SMS API Account: {sms_config['account']}")
        logger.info(f"SMS API Port: {sms_config['port']}")
        logger.info(f"SMS Max Ports: {sms_config['max_ports']}")
        logger.info(f"TG SMS IP: {tg_config['ip']}")
        logger.info(f"TG SMS Port: {tg_config['port']}")
        logger.info(f"TG SMS Username: {tg_config['username']}")
    except Exception as e:
        logger.warning(f"Could not load dynamic config: {str(e)}")
        logger.info("Using fallback configuration")
    logger.info("=====================================")

    # RESTORE ORIGINAL TG SMS RECEIVER - ENABLE INBOX FUNCTIONALITY
    logger.info("✅ Starting TG SMS receiver - restoring inbox functionality")
    try:
        start_sms_receiver()
        logger.info("✅ TG SMS receiver started successfully")
    except Exception as e:
        logger.error(f"❌ Error starting TG SMS receiver: {str(e)}")

    logger.info("🎯 Dynamic GSM configuration system enabled with inbox functionality")
    app.run(
        host='127.0.0.1',  # Use localhost to avoid permission issues
        port=5555,  # Use port 5555 to avoid conflicts
        debug=True,  # Enable debug mode to see detailed logs
        use_reloader=False  # Disable reloader to avoid child process issues
    )
