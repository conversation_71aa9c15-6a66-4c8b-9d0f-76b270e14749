#!/usr/bin/env python3
"""
Update database schema to add is_active column to SMS ports
"""

import sys
import os
sys.path.append('backend')

import sqlite3

def update_database_schema():
    print("🔧 Updating Database Schema for Dynamic Port Management")
    print("=" * 60)
    
    # Connect to the database
    db_path = 'backend/sms_system.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if is_active column exists
        cursor.execute("PRAGMA table_info(sms_ports)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"Current columns in sms_ports: {columns}")
        
        if 'is_active' not in columns:
            print("Adding is_active column to sms_ports table...")
            
            # Add the is_active column
            cursor.execute("ALTER TABLE sms_ports ADD COLUMN is_active BOOLEAN DEFAULT 1")
            
            # Update all existing ports to be active
            cursor.execute("UPDATE sms_ports SET is_active = 1")
            
            conn.commit()
            print("✅ Successfully added is_active column")
        else:
            print("✅ is_active column already exists")
        
        # Verify the update
        cursor.execute("PRAGMA table_info(sms_ports)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"Updated columns in sms_ports: {columns}")
        
        # Show current port data
        cursor.execute("SELECT port_number, status, is_active FROM sms_ports ORDER BY CAST(port_number AS INTEGER)")
        ports = cursor.fetchall()
        
        print(f"\nCurrent SMS Ports:")
        for port_number, status, is_active in ports:
            print(f"  Port {port_number}: {status} (Active: {bool(is_active)})")
        
        conn.close()
        print(f"\n🎉 Database schema update complete!")
        
    except Exception as e:
        print(f"❌ Error updating database schema: {str(e)}")

if __name__ == "__main__":
    update_database_schema()
