@echo off
echo Starting SMS Management System for MyGoautodial...
echo.

echo Setting SMS Configuration to use GSM IP *************...
set SMS_API_IP=*************
set TG_SMS_IP=*************
echo SMS API IP: %SMS_API_IP%
echo TG SMS IP: %TG_SMS_IP%
echo.

echo Installing Python dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo Failed to install Python dependencies
    echo.
    echo Please make sure Python is installed and added to PATH
    echo Download Python from: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo.
echo Starting the SMS Management System...
echo.
echo The application will be available at:
echo http://localhost:5000
echo.
echo Configuration debug URL:
echo http://localhost:5000/debug/config
echo.
echo Press Ctrl+C to stop the server
echo.

cd backend
python app.py

pause
