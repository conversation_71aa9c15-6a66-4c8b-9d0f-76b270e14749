{% extends "base.html" %}

{% block title %}Dashboard - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Dashboard</h1>
        <p class="text-muted">Welcome back, {{ current_user.full_name }}!</p>
    </div>
    <div>
        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <h3 id="totalMessages">{{ stats.total_messages or 0 }}</h3>
            <p><i class="bi bi-chat-dots"></i> Total Messages</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #059669 0%, #10b981 100%);">
            <h3 id="todaySent">{{ stats.today_sent or 0 }}</h3>
            <p><i class="bi bi-send"></i> Sent Today</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);">
            <h3 id="todayReceived">{{ stats.today_received or 0 }}</h3>
            <p><i class="bi bi-inbox"></i> Received Today</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);">
            <h3 id="failedMessages">{{ stats.failed_messages or 0 }}</h3>
            <p><i class="bi bi-exclamation-triangle"></i> Failed</p>
        </div>
    </div>
</div>

<!-- Gateway Status and Quick Actions -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-hdd-network"></i> Gateway Status</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshGateways()">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
            <div class="card-body">
                {% if gateways %}
                    <div class="row">
                        {% for gateway in gateways %}
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="me-3">
                                    <div class="rounded-circle {{ 'bg-success' if gateway.status == 'active' else 'bg-danger' }}" 
                                         style="width: 12px; height: 12px;"></div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ gateway.name }}</h6>
                                    <small class="text-muted">{{ gateway.ip_address }}:{{ gateway.api_port }}</small>
                                    {% if gateway.is_primary %}
                                        <span class="badge bg-primary ms-2">Primary</span>
                                    {% endif %}
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{{ gateway.max_ports }} ports</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-hdd-network text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-2">No gateways configured</h6>
                        {% if current_user.can_manage_gateways %}
                        <a href="{{ url_for('gateways.add') }}" class="btn btn-primary btn-sm mt-2">
                            <i class="bi bi-plus"></i> Add Gateway
                        </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if current_user.can_send_sms %}
                    <a href="{{ url_for('sms.compose') }}" class="btn btn-primary">
                        <i class="bi bi-pencil-square"></i> Compose SMS
                    </a>
                    {% endif %}
                    
                    {% if current_user.can_view_inbox %}
                    <a href="{{ url_for('sms.inbox') }}" class="btn btn-outline-primary">
                        <i class="bi bi-inbox"></i> View Inbox
                    </a>
                    {% endif %}
                    
                    {% if current_user.can_manage_gateways %}
                    <a href="{{ url_for('gateways.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-hdd-network"></i> Manage Gateways
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-info" onclick="refreshAllPorts()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh Ports
                    </button>

                    <button class="btn btn-outline-warning" onclick="showTestPanel()">
                        <i class="bi bi-tools"></i> Test SMS (Dev)
                    </button>

                    <button class="btn btn-outline-info" onclick="testReceiver()">
                        <i class="bi bi-reception-4"></i> Test Receiver
                    </button>

                    <button class="btn btn-outline-danger" onclick="restartReceiver()">
                        <i class="bi bi-arrow-repeat"></i> Restart Receiver
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Messages -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-clock-history"></i> Recent Messages</h5>
                <a href="{{ url_for('sms.inbox') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_messages %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Direction</th>
                                    <th>Phone Number</th>
                                    <th>Message</th>
                                    <th>Status</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for message in recent_messages %}
                                <tr>
                                    <td>
                                        {% if message.direction == 'inbound' %}
                                            <span class="badge bg-info">
                                                <i class="bi bi-arrow-down"></i> In
                                            </span>
                                        {% else %}
                                            <span class="badge bg-primary">
                                                <i class="bi bi-arrow-up"></i> Out
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('sms.conversation', phone_number=message.phone_number) }}" 
                                           class="text-decoration-none">
                                            {{ message.phone_number }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 300px;">
                                            {{ message.content }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if message.status == 'sent' %}
                                            <span class="badge bg-success">Sent</span>
                                        {% elif message.status == 'delivered' %}
                                            <span class="badge bg-success">Delivered</span>
                                        {% elif message.status == 'failed' %}
                                            <span class="badge bg-danger">Failed</span>
                                        {% elif message.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ message.status.title() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ message.created_at.strftime('%m/%d %H:%M') }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-2">No recent messages</h6>
                        <p class="text-muted">Messages will appear here once you start sending or receiving SMS.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Test SMS Modal -->
<div class="modal fade" id="testSMSModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-tools"></i> SMS Testing Panel (Development Mode)
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-send"></i> Test Send SMS</h6>
                            </div>
                            <div class="card-body">
                                <form id="testSendForm">
                                    <div class="mb-3">
                                        <label class="form-label">Phone Number</label>
                                        <input type="text" class="form-control" id="testSendPhone" value="+1234567890">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Message</label>
                                        <textarea class="form-control" id="testSendMessage" rows="3">Test message from Modern SMS Manager</textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Port</label>
                                        <select class="form-select" id="testSendPort">
                                            <option value="">Loading ports...</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-send"></i> Send Test SMS
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-inbox"></i> Simulate Receive SMS</h6>
                            </div>
                            <div class="card-body">
                                <form id="testReceiveForm">
                                    <div class="mb-3">
                                        <label class="form-label">From Phone Number</label>
                                        <input type="text" class="form-control" id="testReceivePhone" value="+0987654321">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Message</label>
                                        <textarea class="form-control" id="testReceiveMessage" rows="3">Hello! This is a test received message.</textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Port</label>
                                        <select class="form-select" id="testReceivePort">
                                            <option value="">Loading ports...</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-success">
                                        <i class="bi bi-inbox"></i> Simulate Receive
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <h6><i class="bi bi-info-circle"></i> Development Mode Testing</h6>
                    <ul class="mb-0">
                        <li><strong>Send SMS:</strong> Simulates sending SMS without actual gateway connection</li>
                        <li><strong>Receive SMS:</strong> Creates a fake received message for testing inbox functionality</li>
                        <li><strong>Note:</strong> These are development tools and won't work with real hardware</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 30 seconds
    setInterval(refreshDashboard, 30000);

    function refreshDashboard() {
        fetch('/api/dashboard/stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStats(data.stats);
                    showToast('Dashboard refreshed', 'success');
                }
            })
            .catch(error => {
                console.error('Error refreshing dashboard:', error);
            });
    }

    function updateStats(stats) {
        document.getElementById('totalMessages').textContent = stats.total_messages || 0;
        document.getElementById('todaySent').textContent = stats.today_sent || 0;
        document.getElementById('todayReceived').textContent = stats.today_received || 0;
        document.getElementById('failedMessages').textContent = stats.failed_messages || 0;
    }

    function refreshGateways() {
        // Reload the page to refresh gateway status
        location.reload();
    }

    function refreshAllPorts() {
        fetch('/api/ports/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`Refreshed ${data.updated_count} ports`, 'success');
            } else {
                showToast('Error refreshing ports: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error refreshing ports:', error);
            showToast('Error refreshing ports', 'error');
        });
    }

    // Show test panel
    function showTestPanel() {
        const modal = new bootstrap.Modal(document.getElementById('testSMSModal'));
        modal.show();
        loadTestPorts();
    }

    // Load ports for testing
    function loadTestPorts() {
        fetch('/api/gateways')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.gateways) {
                    populateTestPorts(data.gateways);
                }
            })
            .catch(error => {
                console.error('Error loading test ports:', error);
            });
    }

    function populateTestPorts(gateways) {
        const sendPortSelect = document.getElementById('testSendPort');
        const receivePortSelect = document.getElementById('testReceivePort');

        let options = '<option value="">Select a port...</option>';

        gateways.forEach(gateway => {
            for (let i = 1; i <= gateway.max_ports; i++) {
                options += `<option value="${i}">Gateway ${gateway.id} - Port ${i}</option>`;
            }
        });

        sendPortSelect.innerHTML = options;
        receivePortSelect.innerHTML = options;
    }

    // Handle test send form
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('testSendForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const phone = document.getElementById('testSendPhone').value;
            const message = document.getElementById('testSendMessage').value;
            const port = document.getElementById('testSendPort').value;

            if (!phone || !message || !port) {
                showToast('Please fill all fields', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('phone_number', phone);
            formData.append('message', message);
            formData.append('port_id', port);

            fetch('/sms/send', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Test SMS sent successfully!', 'success');
                } else {
                    showToast('Error: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('Error sending test SMS: ' + error.message, 'error');
            });
        });

        // Handle test receive form
        document.getElementById('testReceiveForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const phone = document.getElementById('testReceivePhone').value;
            const message = document.getElementById('testReceiveMessage').value;
            const port = document.getElementById('testReceivePort').value;

            if (!phone || !message || !port) {
                showToast('Please fill all fields', 'error');
                return;
            }

            fetch('/api/simulate/receive-sms', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    phone_number: phone,
                    message: message,
                    port_id: parseInt(port)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Test SMS received successfully!', 'success');
                } else {
                    showToast('Error: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('Error simulating received SMS: ' + error.message, 'error');
            });
        });
    });

    // Test SMS receiver
    function testReceiver() {
        fetch('/api/test-receiver', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let statusMsg = `SMS Receiver Status:\n`;
                statusMsg += `Running: ${data.receiver_status.running}\n`;
                statusMsg += `Connected: ${data.receiver_status.connected}\n`;
                statusMsg += `Thread Alive: ${data.receiver_status.thread_alive}\n`;

                if (data.gateway_info) {
                    statusMsg += `\nGateway: ${data.gateway_info.name}\n`;
                    statusMsg += `IP: ${data.gateway_info.ip}\n`;
                    statusMsg += `TG Port: ${data.gateway_info.tg_port}`;
                } else {
                    statusMsg += `\nNo primary gateway configured`;
                }

                alert(statusMsg);
                showToast('Receiver status checked', 'info');
            } else {
                showToast('Error: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Failed to check receiver: ' + error.message, 'error');
        });
    }

    // Restart SMS receiver
    function restartReceiver() {
        if (!confirm('Are you sure you want to restart the SMS receiver? This will briefly interrupt SMS receiving.')) {
            return;
        }

        showToast('Restarting SMS receiver...', 'info');

        fetch('/api/restart-receiver', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let message = `SMS Receiver Restarted Successfully!\n`;
                if (data.gateway) {
                    message += `Gateway: ${data.gateway.name}\n`;
                    message += `IP: ${data.gateway.ip_address}:${data.gateway.tg_sms_port}`;
                }
                showToast(message, 'success');

                // Refresh dashboard stats after restart
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                showToast('Error restarting receiver: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Error restarting receiver: ' + error.message, 'error');
        });
    }

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        // Add any initialization code here
        console.log('Dashboard loaded');
    });
</script>
{% endblock %}
