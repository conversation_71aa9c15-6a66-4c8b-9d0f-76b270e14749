{% extends "base.html" %}

{% block title %}Dashboard - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Dashboard</h1>
        <p class="text-muted">Welcome back, {{ current_user.full_name }}!</p>
    </div>
    <div>
        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <h3 id="totalMessages">{{ stats.total_messages or 0 }}</h3>
            <p><i class="bi bi-chat-dots"></i> Total Messages</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #059669 0%, #10b981 100%);">
            <h3 id="todaySent">{{ stats.today_sent or 0 }}</h3>
            <p><i class="bi bi-send"></i> Sent Today</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);">
            <h3 id="todayReceived">{{ stats.today_received or 0 }}</h3>
            <p><i class="bi bi-inbox"></i> Received Today</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);">
            <h3 id="failedMessages">{{ stats.failed_messages or 0 }}</h3>
            <p><i class="bi bi-exclamation-triangle"></i> Failed</p>
        </div>
    </div>
</div>

<!-- Gateway Status and Quick Actions -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-hdd-network"></i> Gateway Status</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshGateways()">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
            <div class="card-body">
                {% if gateways %}
                    <div class="row">
                        {% for gateway in gateways %}
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="me-3">
                                    <div class="rounded-circle {{ 'bg-success' if gateway.status == 'active' else 'bg-danger' }}" 
                                         style="width: 12px; height: 12px;"></div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ gateway.name }}</h6>
                                    <small class="text-muted">{{ gateway.ip_address }}:{{ gateway.api_port }}</small>
                                    {% if gateway.is_primary %}
                                        <span class="badge bg-primary ms-2">Primary</span>
                                    {% endif %}
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{{ gateway.max_ports }} ports</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-hdd-network text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-2">No gateways configured</h6>
                        {% if current_user.can_manage_gateways %}
                        <a href="{{ url_for('gateways.add') }}" class="btn btn-primary btn-sm mt-2">
                            <i class="bi bi-plus"></i> Add Gateway
                        </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if current_user.can_send_sms %}
                    <a href="{{ url_for('sms.compose') }}" class="btn btn-primary">
                        <i class="bi bi-pencil-square"></i> Compose SMS
                    </a>
                    {% endif %}
                    
                    {% if current_user.can_view_inbox %}
                    <a href="{{ url_for('sms.inbox') }}" class="btn btn-outline-primary">
                        <i class="bi bi-inbox"></i> View Inbox
                    </a>
                    {% endif %}
                    
                    {% if current_user.can_manage_gateways %}
                    <a href="{{ url_for('gateways.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-hdd-network"></i> Manage Gateways
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-info" onclick="refreshAllPorts()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh Ports
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Messages -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-clock-history"></i> Recent Messages</h5>
                <a href="{{ url_for('sms.inbox') }}" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_messages %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Direction</th>
                                    <th>Phone Number</th>
                                    <th>Message</th>
                                    <th>Status</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for message in recent_messages %}
                                <tr>
                                    <td>
                                        {% if message.direction == 'inbound' %}
                                            <span class="badge bg-info">
                                                <i class="bi bi-arrow-down"></i> In
                                            </span>
                                        {% else %}
                                            <span class="badge bg-primary">
                                                <i class="bi bi-arrow-up"></i> Out
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('sms.conversation', phone_number=message.phone_number) }}" 
                                           class="text-decoration-none">
                                            {{ message.phone_number }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 300px;">
                                            {{ message.content }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if message.status == 'sent' %}
                                            <span class="badge bg-success">Sent</span>
                                        {% elif message.status == 'delivered' %}
                                            <span class="badge bg-success">Delivered</span>
                                        {% elif message.status == 'failed' %}
                                            <span class="badge bg-danger">Failed</span>
                                        {% elif message.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ message.status.title() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ message.created_at.strftime('%m/%d %H:%M') }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-2">No recent messages</h6>
                        <p class="text-muted">Messages will appear here once you start sending or receiving SMS.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 30 seconds
    setInterval(refreshDashboard, 30000);

    function refreshDashboard() {
        fetch('/api/dashboard/stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStats(data.stats);
                    showToast('Dashboard refreshed', 'success');
                }
            })
            .catch(error => {
                console.error('Error refreshing dashboard:', error);
            });
    }

    function updateStats(stats) {
        document.getElementById('totalMessages').textContent = stats.total_messages || 0;
        document.getElementById('todaySent').textContent = stats.today_sent || 0;
        document.getElementById('todayReceived').textContent = stats.today_received || 0;
        document.getElementById('failedMessages').textContent = stats.failed_messages || 0;
    }

    function refreshGateways() {
        // Reload the page to refresh gateway status
        location.reload();
    }

    function refreshAllPorts() {
        fetch('/api/ports/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`Refreshed ${data.updated_count} ports`, 'success');
            } else {
                showToast('Error refreshing ports: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error refreshing ports:', error);
            showToast('Error refreshing ports', 'error');
        });
    }

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        // Add any initialization code here
        console.log('Dashboard loaded');
    });
</script>
{% endblock %}
