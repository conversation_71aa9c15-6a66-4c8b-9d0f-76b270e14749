import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  Reply, 
  Phone, 
  Clock, 
  MessageSquare,
  Send,
  Inbox,
  Copy,
  ExternalLink
} from 'lucide-react';
import toast from 'react-hot-toast';
import { smsApi, formatDateTime, getStatusColor, getStatusIcon } from '../services/api';

const MessageView = ({ onStatsUpdate }) => {
  const { messageId } = useParams();
  const navigate = useNavigate();
  
  const [message, setMessage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (messageId) {
      loadMessage();
    }
  }, [messageId]);

  const loadMessage = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await smsApi.getMessage(messageId);
      setMessage(response.data);
    } catch (error) {
      console.error('Error loading message:', error);
      setError(error.message || 'Failed to load message');
      toast.error('Failed to load message details');
    } finally {
      setLoading(false);
    }
  };

  const handleReply = () => {
    if (message && message.direction === 'inbound') {
      navigate(`/compose?to=${encodeURIComponent(message.phone_number)}&reply_to=${message.message_id}`);
    }
  };

  const handleCopyContent = () => {
    if (message) {
      navigator.clipboard.writeText(message.content);
      toast.success('Message content copied to clipboard');
    }
  };

  const handleCopyPhoneNumber = () => {
    if (message) {
      navigator.clipboard.writeText(message.phone_number);
      toast.success('Phone number copied to clipboard');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-20 bg-gray-200 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !message) {
    return (
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center space-x-3 mb-6">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back</span>
            </button>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Message Not Found</h3>
            <p className="text-gray-500 mb-4">
              {error || 'The message you\'re looking for doesn\'t exist or has been deleted.'}
            </p>
            <div className="flex justify-center space-x-3">
              <Link
                to="/inbox"
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Go to Inbox
              </Link>
              <Link
                to="/outbox"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Go to Outbox
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back</span>
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <h1 className="text-2xl font-bold text-gray-900">Message Details</h1>
          </div>
          
          {message.direction === 'inbound' && (
            <button
              onClick={handleReply}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Reply className="h-4 w-4" />
              <span>Reply</span>
            </button>
          )}
        </div>

        {/* Message Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`p-3 rounded-full ${
                  message.direction === 'inbound' ? 'bg-green-100' : 'bg-blue-100'
                }`}>
                  {message.direction === 'inbound' ? (
                    <Inbox className={`h-6 w-6 ${
                      message.direction === 'inbound' ? 'text-green-600' : 'text-blue-600'
                    }`} />
                  ) : (
                    <Send className={`h-6 w-6 ${
                      message.direction === 'inbound' ? 'text-green-600' : 'text-blue-600'
                    }`} />
                  )}
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    {message.direction === 'inbound' ? 'Received Message' : 'Sent Message'}
                  </h2>
                  <p className="text-sm text-gray-600">
                    Message ID: {message.message_id}
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(message.status)}`}>
                  <span className="mr-1">{getStatusIcon(message.status)}</span>
                  {message.status}
                </span>
              </div>
            </div>
          </div>

          {/* Message Details */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              {/* Phone Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {message.direction === 'inbound' ? 'From' : 'To'}
                </label>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-lg font-mono text-gray-900">{message.phone_number}</span>
                  <button
                    onClick={handleCopyPhoneNumber}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    title="Copy phone number"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Timestamp */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {message.direction === 'inbound' ? 'Received At' : 'Sent At'}
                </label>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-900">{formatDateTime(message.created_at)}</span>
                </div>
              </div>

              {/* GSM Port */}
              {message.gsm_port && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    GSM Port
                  </label>
                  <span className="text-gray-900">Port {message.gsm_port}</span>
                </div>
              )}

              {/* SMSC */}
              {message.smsc && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMS Center
                  </label>
                  <span className="text-gray-900 font-mono">{message.smsc}</span>
                </div>
              )}

              {/* Reply To */}
              {message.reply_to_id && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reply To
                  </label>
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-900 font-mono">{message.reply_to_id}</span>
                    <Link
                      to={`/message/${message.reply_to_id}`}
                      className="p-1 text-blue-600 hover:text-blue-800 transition-colors"
                      title="View original message"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* Message Content */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  Message Content
                </label>
                <button
                  onClick={handleCopyContent}
                  className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors"
                >
                  <Copy className="h-4 w-4" />
                  <span>Copy</span>
                </button>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <p className="text-gray-900 whitespace-pre-wrap leading-relaxed">
                  {message.content}
                </p>
              </div>
              
              <div className="mt-2 text-sm text-gray-500">
                {message.content.length} character{message.content.length !== 1 ? 's' : ''}
                {message.content.length > 160 && (
                  <span className="ml-2 text-yellow-600">
                    • {Math.ceil(message.content.length / 153)} SMS segment{Math.ceil(message.content.length / 153) !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="p-6 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Last updated: {formatDateTime(message.updated_at)}
              </div>
              
              <div className="flex items-center space-x-3">
                {message.direction === 'inbound' && (
                  <button
                    onClick={handleReply}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Reply className="h-4 w-4" />
                    <span>Reply</span>
                  </button>
                )}
                
                <Link
                  to={message.direction === 'inbound' ? '/inbox' : '/outbox'}
                  className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {message.direction === 'inbound' ? (
                    <Inbox className="h-4 w-4" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                  <span>Back to {message.direction === 'inbound' ? 'Inbox' : 'Outbox'}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageView;
