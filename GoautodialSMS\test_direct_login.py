#!/usr/bin/env python3
"""
Test direct login with curl to see if the issue is with requests library
"""

import subprocess
import json

BASE_URL = "http://127.0.0.1:5000"

def test_with_curl():
    print("🧪 Testing login with curl...")
    
    # Test admin login with curl
    print("\n1️⃣ Testing admin login with curl:")
    cmd = [
        'curl', '-X', 'POST',
        '-d', 'username=admin&password=admin123',
        '-H', 'Content-Type: application/x-www-form-urlencoded',
        '-i', '-s',
        f'{BASE_URL}/login'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        print(f"Exit code: {result.returncode}")
        print(f"Response:\n{result.stdout}")
        if result.stderr:
            print(f"Error: {result.stderr}")
    except Exception as e:
        print(f"Error running curl: {e}")
    
    # Test test1 login with curl
    print("\n2️⃣ Testing test1 login with curl:")
    cmd = [
        'curl', '-X', 'POST',
        '-d', 'username=test1&password=test123',
        '-H', 'Content-Type: application/x-www-form-urlencoded',
        '-i', '-s',
        f'{BASE_URL}/login'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        print(f"Exit code: {result.returncode}")
        print(f"Response:\n{result.stdout}")
        if result.stderr:
            print(f"Error: {result.stderr}")
    except Exception as e:
        print(f"Error running curl: {e}")

if __name__ == "__main__":
    test_with_curl()
