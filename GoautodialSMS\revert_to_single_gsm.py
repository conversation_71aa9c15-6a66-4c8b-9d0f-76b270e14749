#!/usr/bin/env python3
"""
Revert to Single GSM Gateway
This script removes all multi-GSM complexity and creates a simple single gateway system
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app import app, db, GSMGateway, SMSPort, User

def revert_to_single_gsm():
    """Clean database and create single GSM gateway"""
    print("🔧 Reverting to Single GSM Gateway System")
    print("=" * 60)
    
    with app.app_context():
        try:
            # Step 1: Clean up all existing gateways and ports
            print("📋 Step 1: Cleaning up existing data...")
            
            # Delete all SMS ports
            ports_deleted = SMSPort.query.delete()
            print(f"   🗑️ Deleted {ports_deleted} SMS ports")
            
            # Delete all GSM gateways
            gateways_deleted = GSMGateway.query.delete()
            print(f"   🗑️ Deleted {gateways_deleted} GSM gateways")
            
            db.session.commit()
            print("   ✅ Database cleaned")
            
            # Step 2: Create single GSM gateway
            print("\n📋 Step 2: Creating single GSM gateway...")
            
            single_gateway = GSMGateway(
                name='Main Gateway',
                ip_address='*************',
                api_port='80',
                tg_sms_port='5038',
                username='apiuser',
                password='apipass',
                max_ports=4,  # Your hardware has 4 ports
                status='active',
                location='Main Office',
                description='Single GSM Gateway for SMS Operations',
                is_primary=True
            )
            
            db.session.add(single_gateway)
            db.session.flush()  # Get the ID
            
            print(f"   ✅ Created gateway: {single_gateway.name}")
            print(f"      IP: {single_gateway.ip_address}")
            print(f"      Ports: {single_gateway.max_ports}")
            
            # Step 3: Create 4 simple ports
            print("\n📋 Step 3: Creating 4 simple ports...")
            
            for port_num in range(1, 5):
                # Port 2 has no SIM (based on your feedback)
                has_sim = port_num != 2
                
                new_port = SMSPort(
                    port_number=str(port_num),  # Simple: 1, 2, 3, 4
                    status='detected' if has_sim else 'no_sim',
                    network_name='Unknown' if has_sim else 'None',
                    signal_quality='Unknown' if has_sim else 'N/A',
                    is_active=True,  # All ports are active in database
                    gateway_id=single_gateway.id
                )
                
                db.session.add(new_port)
                status_icon = "✅" if has_sim else "❌"
                print(f"   {status_icon} Created port {port_num} ({'SIM' if has_sim else 'No SIM'})")
            
            db.session.commit()
            
            # Step 4: Update user port assignments to simple format
            print("\n📋 Step 4: Updating user port assignments...")
            
            users = User.query.all()
            for user in users:
                if user.assigned_ports:
                    # Convert complex port assignments to simple 1,2,3,4 format
                    old_ports = user.assigned_ports
                    
                    # Extract simple port numbers
                    simple_ports = []
                    for port in old_ports.split(','):
                        port = port.strip()
                        if port.isdigit() and 1 <= int(port) <= 4:
                            simple_ports.append(port)
                        elif 'port' in port:
                            # Extract from gw2_port1 format
                            try:
                                port_num = port.split('port')[1]
                                if port_num.isdigit() and 1 <= int(port_num) <= 4:
                                    simple_ports.append(port_num)
                            except:
                                pass
                    
                    # Default assignment if no valid ports found
                    if not simple_ports:
                        if user.role == 'admin':
                            simple_ports = ['1', '2', '3', '4']  # Admin gets all ports
                        else:
                            simple_ports = ['1', '3']  # Regular users get ports with SIM
                    
                    user.assigned_ports = ','.join(simple_ports)
                    print(f"   👤 Updated {user.username}: {old_ports} → {user.assigned_ports}")
            
            db.session.commit()
            
            # Step 5: Verify the setup
            print("\n📋 Step 5: Verifying single GSM setup...")
            
            gateway = GSMGateway.query.first()
            ports = SMSPort.query.filter_by(gateway_id=gateway.id).all()
            users = User.query.all()
            
            print(f"   🏢 Gateway: {gateway.name}")
            print(f"      IP: {gateway.ip_address}")
            print(f"      Max Ports: {gateway.max_ports}")
            print(f"      Status: {gateway.status}")
            
            print(f"   📱 Ports: {len(ports)} total")
            for port in ports:
                status_icon = "✅" if port.status == 'detected' else "❌"
                print(f"      {status_icon} Port {port.port_number}: {port.status}")
            
            print(f"   👥 Users: {len(users)} total")
            for user in users:
                print(f"      👤 {user.username}: ports {user.assigned_ports}")
            
            print(f"\n✅ Single GSM Gateway Setup Complete!")
            print(f"📋 Summary:")
            print(f"   - 1 GSM Gateway (Main Gateway)")
            print(f"   - 4 Ports (1,2,3,4) - Port 2 has no SIM")
            print(f"   - Simple port numbering (no prefixes)")
            print(f"   - All multi-GSM complexity removed")
            print(f"   - Users assigned to simple port numbers")
            
            return True
            
        except Exception as e:
            print(f"❌ Error reverting to single GSM: {str(e)}")
            db.session.rollback()
            return False

def show_current_status():
    """Show current system status after revert"""
    print(f"\n📊 Current System Status")
    print("=" * 50)
    
    with app.app_context():
        try:
            gateways = GSMGateway.query.all()
            ports = SMSPort.query.all()
            users = User.query.all()
            
            print(f"🏢 Gateways: {len(gateways)}")
            for gateway in gateways:
                print(f"   - {gateway.name} ({gateway.ip_address})")
            
            print(f"📱 Ports: {len(ports)}")
            for port in ports:
                print(f"   - Port {port.port_number}: {port.status}")
            
            print(f"👥 Users: {len(users)}")
            for user in users:
                print(f"   - {user.username}: {user.assigned_ports}")
                
        except Exception as e:
            print(f"❌ Error showing status: {str(e)}")

if __name__ == "__main__":
    print("🚀 Single GSM Gateway Revert Tool")
    print("=" * 60)
    print("This will remove all multi-GSM complexity and create a simple single gateway system.")
    print()
    
    try:
        response = input("Do you want to proceed? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            success = revert_to_single_gsm()
            if success:
                show_current_status()
                print(f"\n🎯 Next Steps:")
                print(f"1. Restart the Flask application")
                print(f"2. Check GSM Gateways page - should show 1 gateway with 4 ports")
                print(f"3. Test SMS sending from ports 1, 3, 4 (port 2 has no SIM)")
                print(f"4. All complexity removed - simple single gateway design")
            else:
                print(f"\n❌ Revert failed. Please check the errors above.")
        else:
            print("✅ Operation cancelled.")
    except KeyboardInterrupt:
        print("\n\n✅ Operation cancelled by user.")
