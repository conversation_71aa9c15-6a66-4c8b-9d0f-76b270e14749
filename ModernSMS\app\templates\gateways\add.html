{% extends "base.html" %}

{% block title %}Add Gateway - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Add New Gateway</h1>
        <p class="text-muted">Configure a new GSM gateway for SMS communications</p>
    </div>
    <div>
        <a href="{{ url_for('gateways.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Gateways
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-plus-circle"></i> Gateway Configuration</h5>
            </div>
            <div class="card-body">
                <form id="gatewayForm" method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label fw-semibold">Gateway Name *</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   placeholder="e.g., Main Office Gateway"
                                   required>
                            <div class="form-text">A descriptive name for this gateway</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="ip_address" class="form-label fw-semibold">IP Address *</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="ip_address" 
                                   name="ip_address" 
                                   placeholder="************"
                                   pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$"
                                   required>
                            <div class="form-text">Gateway IP address on your network</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="api_port" class="form-label fw-semibold">API Port</label>
                            <input type="number" 
                                   class="form-control" 
                                   id="api_port" 
                                   name="api_port" 
                                   value="80"
                                   min="1" 
                                   max="65535">
                            <div class="form-text">HTTP API port (usually 80)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="tg_sms_port" class="form-label fw-semibold">TG SMS Port</label>
                            <input type="number" 
                                   class="form-control" 
                                   id="tg_sms_port" 
                                   name="tg_sms_port" 
                                   value="5038"
                                   min="1" 
                                   max="65535">
                            <div class="form-text">TG SMS service port (usually 5038)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label fw-semibold">Username *</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="username" 
                                   name="username" 
                                   placeholder="apiuser"
                                   required>
                            <div class="form-text">Gateway API username</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label fw-semibold">Password *</label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control" 
                                       id="password" 
                                       name="password" 
                                       placeholder="Enter password"
                                       required>
                                <button class="btn btn-outline-secondary" 
                                        type="button" 
                                        onclick="togglePassword('password')">
                                    <i class="bi bi-eye" id="passwordIcon"></i>
                                </button>
                            </div>
                            <div class="form-text">Gateway API password</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_ports" class="form-label fw-semibold">Maximum Ports</label>
                            <input type="number" 
                                   class="form-control" 
                                   id="max_ports" 
                                   name="max_ports" 
                                   value="8"
                                   min="1" 
                                   max="32">
                            <div class="form-text">Number of SIM card slots</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="location" class="form-label fw-semibold">Location</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="location" 
                                   name="location" 
                                   placeholder="e.g., Main Office, Building A">
                            <div class="form-text">Physical location of the gateway</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label fw-semibold">Description</label>
                        <textarea class="form-control" 
                                  id="description" 
                                  name="description" 
                                  rows="3"
                                  placeholder="Optional description or notes about this gateway"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="is_primary" 
                                   name="is_primary">
                            <label class="form-check-label fw-semibold" for="is_primary">
                                Set as Primary Gateway
                            </label>
                            <div class="form-text">Primary gateway is used as default for sending SMS</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="test_connection" 
                                   name="test_connection"
                                   checked>
                            <label class="form-check-label fw-semibold" for="test_connection">
                                Test Connection After Adding
                            </label>
                            <div class="form-text">Verify gateway connectivity before saving</div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="bi bi-check-circle"></i>
                            <span class="btn-text">Add Gateway</span>
                            <span class="loading-spinner d-none"></span>
                        </button>
                        
                        <button type="button" class="btn btn-outline-info" onclick="testConnectionNow()">
                            <i class="bi bi-wifi"></i> Test Connection
                        </button>
                        
                        <a href="{{ url_for('gateways.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Configuration Help</h6>
            </div>
            <div class="card-body">
                <h6 class="fw-bold">Required Information</h6>
                <ul class="list-unstyled">
                    <li><i class="bi bi-check text-success"></i> Gateway Name</li>
                    <li><i class="bi bi-check text-success"></i> IP Address</li>
                    <li><i class="bi bi-check text-success"></i> Username</li>
                    <li><i class="bi bi-check text-success"></i> Password</li>
                </ul>
                
                <hr>
                
                <h6 class="fw-bold">Default Ports</h6>
                <ul class="list-unstyled">
                    <li><strong>API Port:</strong> 80</li>
                    <li><strong>TG SMS Port:</strong> 5038</li>
                </ul>
                
                <hr>
                
                <h6 class="fw-bold">Tips</h6>
                <ul class="small">
                    <li>Use descriptive names for easy identification</li>
                    <li>Test connection before saving</li>
                    <li>Only one gateway can be primary</li>
                    <li>Ports will be auto-detected after adding</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-shield-check"></i> Connection Test</h6>
            </div>
            <div class="card-body">
                <div id="testResult" class="d-none">
                    <!-- Test results will appear here -->
                </div>
                <p class="small text-muted mb-0">
                    Click "Test Connection" to verify gateway accessibility before saving.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .form-label.fw-semibold {
        color: var(--dark-color);
    }
    
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .card-header h6 {
        color: var(--dark-color);
    }
    
    .list-unstyled li {
        padding: 2px 0;
    }
    
    #testResult {
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 15px;
    }
    
    .test-success {
        background: rgba(25, 135, 84, 0.1);
        border: 1px solid rgba(25, 135, 84, 0.2);
        color: #198754;
    }
    
    .test-error {
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.2);
        color: #dc3545;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + 'Icon');
        
        if (field.type === 'password') {
            field.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            field.type = 'password';
            icon.className = 'bi bi-eye';
        }
    }

    function testConnectionNow() {
        const form = document.getElementById('gatewayForm');
        const formData = new FormData(form);
        const testResult = document.getElementById('testResult');
        
        // Validate required fields
        const requiredFields = ['name', 'ip_address', 'username', 'password'];
        for (let field of requiredFields) {
            if (!formData.get(field)) {
                showToast(`Please fill in the ${field.replace('_', ' ')} field`, 'error');
                return;
            }
        }
        
        // Show loading
        testResult.className = 'd-block p-3 border rounded';
        testResult.innerHTML = '<i class="bi bi-hourglass-split"></i> Testing connection...';
        
        // Create test data
        const testData = {
            ip_address: formData.get('ip_address'),
            api_port: formData.get('api_port') || '80',
            username: formData.get('username'),
            password: formData.get('password')
        };
        
        // Simulate connection test (in real implementation, this would call the gateway)
        setTimeout(() => {
            // For demo purposes, randomly succeed or fail
            const success = Math.random() > 0.3;
            
            if (success) {
                testResult.className = 'd-block test-success';
                testResult.innerHTML = `
                    <i class="bi bi-check-circle"></i> 
                    <strong>Connection Successful!</strong><br>
                    <small>Gateway is responding and accessible.</small>
                `;
                showToast('Gateway connection test successful!', 'success');
            } else {
                testResult.className = 'd-block test-error';
                testResult.innerHTML = `
                    <i class="bi bi-exclamation-triangle"></i> 
                    <strong>Connection Failed</strong><br>
                    <small>Unable to connect to gateway. Please check IP address and credentials.</small>
                `;
                showToast('Gateway connection test failed', 'error');
            }
        }, 2000);
    }

    // Handle form submission
    document.getElementById('gatewayForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const saveBtn = document.getElementById('saveBtn');
        const btnText = saveBtn.querySelector('.btn-text');
        const spinner = saveBtn.querySelector('.loading-spinner');
        
        // Show loading state
        btnText.classList.add('d-none');
        spinner.classList.remove('d-none');
        saveBtn.disabled = true;
        
        // Submit form data
        const formData = new FormData(this);
        
        fetch(this.action || window.location.pathname, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (response.ok) {
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        // If not JSON, assume success and redirect
                        window.location.href = "{{ url_for('gateways.index') }}";
                        return null;
                    }
                });
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        })
        .then(data => {
            if (data && data.success) {
                showToast(data.message || 'Gateway added successfully!', 'success');
                setTimeout(() => {
                    window.location.href = "{{ url_for('gateways.index') }}";
                }, 1000);
            } else if (data && !data.success) {
                showToast('Error: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Failed to add gateway: ' + error.message, 'error');
        })
        .finally(() => {
            // Restore button state
            btnText.classList.remove('d-none');
            spinner.classList.add('d-none');
            saveBtn.disabled = false;
        });
    });

    // Auto-format IP address
    document.getElementById('ip_address').addEventListener('input', function(e) {
        let value = e.target.value;
        // Remove any non-digit and non-dot characters
        value = value.replace(/[^\d.]/g, '');
        e.target.value = value;
    });

    // Validate IP address on blur
    document.getElementById('ip_address').addEventListener('blur', function(e) {
        const ip = e.target.value;
        const ipPattern = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        
        if (ip && !ipPattern.test(ip)) {
            showToast('Please enter a valid IP address', 'error');
            e.target.focus();
        }
    });
</script>
{% endblock %}
