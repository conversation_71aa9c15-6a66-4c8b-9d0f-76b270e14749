#!/usr/bin/env python3
"""
Verify 8-port GSM gateway setup
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSPort, User, SMSMessage, SMS_API_CONFIG

def verify_8port_setup():
    with app.app_context():
        print("🔌 8-Port GSM Gateway Configuration Verification")
        print("=" * 60)
        
        # Check configuration
        max_ports = SMS_API_CONFIG.get('max_ports', 8)
        print(f"📊 System Configuration:")
        print(f"   Max Ports Configured: {max_ports}")
        print(f"   GSM Gateway IP: {SMS_API_CONFIG['ip']}")
        
        # Check detected ports
        try:
            ports = SMSPort.query.order_by(SMSPort.port_number.asc()).all()
            print(f"\n🔌 Detected GSM Ports: {len(ports)}")
            
            for port in ports:
                print(f"   Port {port.port_number}: {port.status} (Active: {port.is_active})")
            
            # Check users and their port assignments
            users = User.query.all()
            print(f"\n👥 User Port Assignments:")
            
            for user in users:
                assigned_ports = user.get_assigned_ports()
                if user.role == 'admin':
                    print(f"   🔑 {user.username}: ALL ports (1-8) - Admin access")
                elif assigned_ports:
                    print(f"   👤 {user.username}: Ports {assigned_ports}")
                else:
                    print(f"   👤 {user.username}: No specific ports (sees all)")
            
            # Show port usage statistics
            print(f"\n📊 Port Usage Statistics:")
            for port in ports:
                total_msgs = SMSMessage.query.filter_by(gsm_port=port.port_number).count()
                inbound = SMSMessage.query.filter_by(gsm_port=port.port_number, direction='inbound').count()
                outbound = SMSMessage.query.filter_by(gsm_port=port.port_number, direction='outbound').count()
                
                print(f"   Port {port.port_number}: {total_msgs} total ({inbound} in, {outbound} out)")
            
            # Show recommended user assignments for 8 ports
            print(f"\n💡 Recommended 8-Port User Assignments:")
            print(f"   🔑 admin: All ports (1-8) - System administration")
            print(f"   👥 sales_team: Ports 1-2 - Sales communications")
            print(f"   👥 support_team: Ports 3-4 - Customer support")
            print(f"   👥 marketing_team: Ports 5-6 - Marketing campaigns")
            print(f"   👥 operations_team: Ports 7-8 - Internal operations")
            
            # Show capacity information
            print(f"\n📈 8-Port System Capacity:")
            print(f"   • Simultaneous SMS channels: 8")
            print(f"   • Recommended users: 4-6 (including admin)")
            print(f"   • Typical throughput: ~240 SMS/hour (30 per port)")
            print(f"   • Ideal for: Medium business, small call center")
            
            print(f"\n✅ Your 8-port GSM gateway is perfectly configured!")
            
        except Exception as e:
            print(f"❌ Error checking ports: {str(e)}")

def show_8port_examples():
    print(f"\n🌍 8-Port GSM Gateway Usage Examples:")
    print("=" * 45)
    
    examples = [
        {
            "scenario": "Small Call Center",
            "users": {
                "supervisor": "All ports (1-8)",
                "agent_team_a": "Ports 1-2", 
                "agent_team_b": "Ports 3-4",
                "agent_team_c": "Ports 5-6",
                "admin_team": "Ports 7-8"
            }
        },
        {
            "scenario": "Multi-Department Business",
            "users": {
                "admin": "All ports (1-8)",
                "sales": "Ports 1-3",
                "support": "Ports 4-5", 
                "marketing": "Ports 6-7",
                "management": "Port 8"
            }
        },
        {
            "scenario": "Regional Office",
            "users": {
                "manager": "All ports (1-8)",
                "local_sales": "Ports 1-2",
                "customer_service": "Ports 3-4",
                "field_operations": "Ports 5-6",
                "emergency_line": "Ports 7-8"
            }
        }
    ]
    
    for example in examples:
        print(f"\n📞 {example['scenario']}:")
        for user, ports in example['users'].items():
            print(f"   • {user}: {ports}")

if __name__ == "__main__":
    verify_8port_setup()
    show_8port_examples()
