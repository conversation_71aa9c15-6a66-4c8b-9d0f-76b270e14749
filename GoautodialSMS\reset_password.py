#!/usr/bin/env python3
"""
Reset password for test users
"""

import sys
import os
sys.path.append('backend')

from app import app, db, User

def reset_passwords():
    with app.app_context():
        # Reset test1 password
        test1 = User.query.filter_by(username='test1').first()
        if test1:
            test1.set_password('test123')
            print("✅ Reset test1 password to 'test123'")
        
        # Reset test2 password  
        test2 = User.query.filter_by(username='test2').first()
        if test2:
            test2.set_password('test123')
            print("✅ Reset test2 password to 'test123'")
            
        # Reset diet password
        diet = User.query.filter_by(username='diet').first()
        if diet:
            diet.set_password('test123')
            print("✅ Reset diet password to 'test123'")
        
        db.session.commit()
        print("✅ All passwords updated!")

if __name__ == "__main__":
    reset_passwords()
