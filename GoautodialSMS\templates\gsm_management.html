{% extends "base.html" %}

{% block title %}GSM Gateway Management - SMS Manager{% endblock %}

{% block extra_css %}
<style>
.gateway-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.gateway-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.port-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.port-status {
    padding: 12px;
    border-radius: 6px;
    text-align: center;
    font-size: 0.875rem;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.port-active {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.port-sim-only {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.port-no-sim {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.port-unknown {
    background-color: #e2e3e5;
    border-color: #d6d8db;
    color: #383d41;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-maintenance {
    background-color: #fff3cd;
    color: #856404;
}

.connection-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.connection-online {
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.connection-offline {
    background-color: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.config-form {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.real-time-status {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-hdd-network"></i> GSM Gateway Management</h2>
                    <p class="text-muted">Configure and monitor GSM gateways and ports</p>
                </div>
                <div class="btn-group">
                    <button onclick="refreshAllGateways()" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-clockwise"></i> Refresh Status
                    </button>
                    <button onclick="showAddGatewayModal()" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Gateway
                    </button>
                    <button onclick="detectAllPorts()" class="btn btn-success">
                        <i class="bi bi-search"></i> Detect Ports
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time System Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="real-time-status">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 id="totalGateways">{{ gateways|length }}</h4>
                        <small>Total Gateways</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 id="activeGateways">{{ active_gateways }}</h4>
                        <small>Active Gateways</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 id="totalPorts">{{ total_ports }}</h4>
                        <small>Total Ports</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 id="activePorts">{{ active_ports }}</h4>
                        <small>Active Ports</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gateway Configuration Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="config-form">
                <h5><i class="bi bi-gear"></i> Quick Gateway Configuration</h5>
                <form id="quickConfigForm" onsubmit="saveQuickConfig(event)">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Gateway Name</label>
                                <input type="text" class="form-control" name="name" placeholder="Main Gateway" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">IP Address</label>
                                <input type="text" class="form-control" name="ip_address" placeholder="************" required>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label">API Port</label>
                                <input type="number" class="form-control" name="api_port" value="80" required>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label">TG SMS Port</label>
                                <input type="number" class="form-control" name="tg_sms_port" value="5038" required>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-3">
                                <label class="form-label">Max Ports</label>
                                <input type="number" class="form-control" name="max_ports" value="8" min="1" max="32" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" name="username" placeholder="apiuser" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="password" placeholder="apipass" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Location</label>
                                <input type="text" class="form-control" name="location" placeholder="Main Office">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Actions</label>
                                <div class="d-flex gap-2">
                                    <button type="button" onclick="testConnection()" class="btn btn-outline-info btn-sm">
                                        <i class="bi bi-wifi"></i> Test
                                    </button>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="bi bi-check"></i> Save
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_primary" id="isPrimary">
                        <label class="form-check-label" for="isPrimary">
                            Set as Primary Gateway
                        </label>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Gateway Cards -->
    <div class="row" id="gatewayContainer">
        {% if gateways %}
            {% for gateway in gateways %}
            <div class="col-lg-6 mb-4" id="gateway-{{ gateway.id }}">
                <div class="card gateway-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">
                                <span class="connection-indicator {{ 'connection-online' if gateway.status == 'active' else 'connection-offline' }}"></span>
                                {{ gateway.name }}
                                {% if gateway.is_primary %}
                                    <span class="badge bg-primary ms-2">Primary</span>
                                {% endif %}
                            </h5>
                            <small class="text-muted">{{ gateway.ip_address }}:{{ gateway.api_port }}</small>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="editGateway({{ gateway.id }})">
                                    <i class="bi bi-pencil"></i> Edit
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="testGatewayConnection({{ gateway.id }})">
                                    <i class="bi bi-wifi"></i> Test Connection
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="detectGatewayPorts({{ gateway.id }})">
                                    <i class="bi bi-search"></i> Detect Ports
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteGateway({{ gateway.id }})">
                                    <i class="bi bi-trash"></i> Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Status:</strong><br>
                                <span class="status-badge status-{{ gateway.status }}">{{ gateway.status }}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>Location:</strong><br>
                                <span class="text-muted">{{ gateway.location or 'Not specified' }}</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <strong>Configuration:</strong>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <small><strong>API Port:</strong> {{ gateway.api_port }}</small>
                                </div>
                                <div class="col-6">
                                    <small><strong>TG SMS Port:</strong> {{ gateway.tg_sms_port }}</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <strong>SMS Ports ({{ gateway.max_ports }}):</strong>
                            <div class="port-grid" id="ports-{{ gateway.id }}">
                                <!-- Ports will be loaded dynamically -->
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm" role="status">
                                        <span class="visually-hidden">Loading ports...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if gateway.description %}
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            {{ gateway.description }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-hdd-network text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">No GSM Gateways Found</h4>
                    <p class="text-muted">Add your first GSM gateway to start managing SMS communications.</p>
                    <button onclick="showAddGatewayModal()" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add First Gateway
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Add Gateway Modal -->
<div class="modal fade" id="addGatewayModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New GSM Gateway</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addGatewayForm">
                    <!-- Form content will be added via JavaScript -->
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh every 30 seconds
setInterval(refreshAllGateways, 30000);

// Load ports for each gateway on page load
document.addEventListener('DOMContentLoaded', function() {
    {% for gateway in gateways %}
    loadGatewayPorts({{ gateway.id }});
    {% endfor %}
});

function refreshAllGateways() {
    console.log('Refreshing all gateways...');
    location.reload();
}

function loadGatewayPorts(gatewayId) {
    const container = document.getElementById(`ports-${gatewayId}`);
    if (!container) return;

    fetch(`/api/gateway/${gatewayId}/ports`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPorts(container, data.ports);
            } else {
                container.innerHTML = '<div class="text-danger">Error loading ports</div>';
            }
        })
        .catch(error => {
            console.error('Error loading ports:', error);
            container.innerHTML = '<div class="text-warning">Failed to load ports</div>';
        });
}

function displayPorts(container, ports) {
    let html = '';
    ports.forEach(port => {
        const statusClass = getPortStatusClass(port.status);
        html += `
            <div class="port-status ${statusClass}">
                <strong>Port ${port.port_number}</strong><br>
                <small>${getPortStatusText(port.status)}</small>
                ${port.network ? `<br><small>Network: ${port.network}</small>` : ''}
                ${port.signal_strength && port.signal_strength !== 'Unknown' ? `<br><small>Signal: ${port.signal_strength}</small>` : ''}
            </div>
        `;
    });
    container.innerHTML = html;
}

function getPortStatusClass(status) {
    switch(status) {
        case 'active': return 'port-active';
        case 'sim_only': return 'port-sim-only';
        case 'no_sim': return 'port-no-sim';
        default: return 'port-unknown';
    }
}

function getPortStatusText(status) {
    switch(status) {
        case 'active': return 'Active & Registered';
        case 'sim_only': return 'SIM Present';
        case 'no_sim': return 'No SIM Card';
        default: return 'Unknown Status';
    }
}

function saveQuickConfig(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    
    fetch('/gsm_gateways/add', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Gateway added successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to save gateway configuration');
    });
}

function testConnection() {
    const form = document.getElementById('quickConfigForm');
    const formData = new FormData(form);
    
    // Test connection logic here
    alert('Testing connection to ' + formData.get('ip_address') + '...');
}

function detectAllPorts() {
    alert('Detecting ports on all gateways...');
    // Implement port detection logic
}

function showAddGatewayModal() {
    const modal = new bootstrap.Modal(document.getElementById('addGatewayModal'));
    modal.show();
}

function editGateway(gatewayId) {
    alert('Edit gateway ' + gatewayId);
}

function testGatewayConnection(gatewayId) {
    alert('Testing connection for gateway ' + gatewayId);
}

function detectGatewayPorts(gatewayId) {
    alert('Detecting ports for gateway ' + gatewayId);
    loadGatewayPorts(gatewayId);
}

function deleteGateway(gatewayId) {
    if (confirm('Are you sure you want to delete this gateway?')) {
        fetch(`/gsm_gateways/${gatewayId}/delete`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById(`gateway-${gatewayId}`).remove();
                alert('Gateway deleted successfully');
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete gateway');
        });
    }
}
</script>
{% endblock %}
