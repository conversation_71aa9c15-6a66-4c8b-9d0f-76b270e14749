{% extends "base.html" %}

{% block title %}Edit User - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Edit User</h1>
        <p class="text-muted">Update user account details</p>
    </div>
    <div>
        <a href="{{ url_for('users.detail', user_id=user.id) }}" class="btn btn-outline-info">
            <i class="bi bi-eye"></i> View Details
        </a>
        <a href="{{ url_for('users.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person-gear"></i> Edit User Information</h5>
            </div>
            <div class="card-body">
                <form id="userForm" method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label fw-semibold">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="{{ user.username }}" required>
                            <div class="form-text">Unique username for login</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label fw-semibold">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ user.email }}" required>
                            <div class="form-text">User's email address</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label fw-semibold">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   value="{{ user.first_name or '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label fw-semibold">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   value="{{ user.last_name or '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_password" class="form-label fw-semibold">New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="new_password" name="new_password">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="bi bi-eye" id="passwordIcon"></i>
                                </button>
                            </div>
                            <div class="form-text">Leave blank to keep current password</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="role_id" class="form-label fw-semibold">Role</label>
                            <select class="form-select" id="role_id" name="role_id">
                                <option value="">Select role...</option>
                                {% for role in roles %}
                                <option value="{{ role.id }}" 
                                        {% if user.role and user.role.id == role.id %}selected{% endif %}>
                                    {{ role.name.title() }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">User's role in the system</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {% if user.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">
                                <i class="bi bi-person-check text-success"></i> Account Active
                            </label>
                        </div>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Permissions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_send_sms" name="can_send_sms" 
                                               {% if user.can_send_sms %}checked{% endif %}>
                                        <label class="form-check-label" for="can_send_sms">
                                            <i class="bi bi-send text-primary"></i> Can Send SMS
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_view_inbox" name="can_view_inbox" 
                                               {% if user.can_view_inbox %}checked{% endif %}>
                                        <label class="form-check-label" for="can_view_inbox">
                                            <i class="bi bi-inbox text-info"></i> Can View Inbox
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_view_outbox" name="can_view_outbox" 
                                               {% if user.can_view_outbox %}checked{% endif %}>
                                        <label class="form-check-label" for="can_view_outbox">
                                            <i class="bi bi-send text-success"></i> Can View Outbox
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_manage_gateways" name="can_manage_gateways" 
                                               {% if user.can_manage_gateways %}checked{% endif %}>
                                        <label class="form-check-label" for="can_manage_gateways">
                                            <i class="bi bi-hdd-network text-warning"></i> Can Manage Gateways
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_manage_users" name="can_manage_users" 
                                               {% if user.can_manage_users %}checked{% endif %}>
                                        <label class="form-check-label" for="can_manage_users">
                                            <i class="bi bi-people text-danger"></i> Can Manage Users
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_view_reports" name="can_view_reports" 
                                               {% if user.can_view_reports %}checked{% endif %}>
                                        <label class="form-check-label" for="can_view_reports">
                                            <i class="bi bi-graph-up text-secondary"></i> Can View Reports
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_export_data" name="can_export_data" 
                                               {% if user.can_export_data %}checked{% endif %}>
                                        <label class="form-check-label" for="can_export_data">
                                            <i class="bi bi-download text-dark"></i> Can Export Data
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Port Assignments</h6>
                            <small class="text-muted">Leave empty for no port access</small>
                        </div>
                        <div class="card-body">
                            <div id="portAssignments">
                                <p class="text-muted small mb-3">
                                    <i class="bi bi-info-circle"></i> 
                                    Select specific ports this user can access. If none selected, user cannot access any ports.
                                </p>
                                
                                <div class="row" id="availablePorts">
                                    <!-- Ports will be loaded here via JavaScript -->
                                    <div class="col-12 text-center">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">Loading ports...</span>
                                        </div>
                                        <p class="text-muted mt-2">Loading available ports...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2 mt-4">
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="bi bi-check-circle"></i>
                            <span class="btn-text">Update User</span>
                            <span class="loading-spinner d-none"></span>
                        </button>
                        
                        <a href="{{ url_for('users.detail', user_id=user.id) }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        
                        <a href="{{ url_for('users.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to List
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Current User Info</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <small class="text-muted">{{ user.created_at.strftime('%m/%d/%Y %H:%M') if user.created_at else 'Unknown' }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Login:</strong><br>
                    <small class="text-muted">{{ user.last_login.strftime('%m/%d/%Y %H:%M') if user.last_login else 'Never' }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Current Status:</strong><br>
                    {% if user.is_active %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-danger">Inactive</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>Current Role:</strong><br>
                    {% if user.role %}
                        <span class="badge bg-primary">{{ user.role.name.title() }}</span>
                    {% else %}
                        <span class="badge bg-secondary">No Role</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>Current Ports:</strong><br>
                    {% if user.assigned_ports %}
                        {% for assignment in user.assigned_ports %}
                            <span class="badge bg-info me-1">GW{{ assignment.gateway_id }}:{{ assignment.port }}</span>
                        {% endfor %}
                    {% else %}
                        <span class="text-muted">No ports assigned</span>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Important Notes</h6>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li>Leave password field blank to keep current password</li>
                    <li>Deactivating a user will prevent login</li>
                    <li>Admin role grants all permissions</li>
                    <li>Port assignments control SMS access</li>
                    <li>Changes take effect immediately</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword() {
        const passwordInput = document.getElementById('new_password');
        const toggleIcon = document.getElementById('passwordIcon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'bi bi-eye';
        }
    }

    // Load available ports and set current assignments
    function loadAvailablePorts() {
        fetch('/api/gateways')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.gateways) {
                    displayPorts(data.gateways);
                } else {
                    showNoPorts();
                }
            })
            .catch(error => {
                console.error('Error loading ports:', error);
                showNoPorts();
            });
    }

    function displayPorts(gateways) {
        const container = document.getElementById('availablePorts');
        
        if (gateways.length === 0) {
            showNoPorts();
            return;
        }

        // Get current user assignments
        const currentAssignments = {{ user.assigned_ports | tojson | safe }};
        const assignedPorts = new Set();
        
        if (currentAssignments) {
            currentAssignments.forEach(assignment => {
                assignedPorts.add(`${assignment.gateway_id}:${assignment.port}`);
            });
        }

        let portsHtml = '';
        gateways.forEach(gateway => {
            if (gateway.port_summary && gateway.port_summary.total > 0) {
                portsHtml += `
                    <div class="col-12 mb-3">
                        <h6 class="fw-bold text-primary">${gateway.name}</h6>
                        <div class="row">
                `;
                
                // Generate ports for this gateway
                for (let i = 1; i <= gateway.max_ports; i++) {
                    const portValue = `${gateway.id}:${i}`;
                    const isChecked = assignedPorts.has(portValue) ? 'checked' : '';
                    
                    portsHtml += `
                        <div class="col-md-4 col-sm-6 mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       id="port_${gateway.id}_${i}" 
                                       name="assigned_ports" 
                                       value="${portValue}" ${isChecked}>
                                <label class="form-check-label" for="port_${gateway.id}_${i}">
                                    <small>Port ${i}</small>
                                    <span class="badge bg-secondary ms-1">Gateway ${gateway.id}</span>
                                </label>
                            </div>
                        </div>
                    `;
                }
                
                portsHtml += `
                        </div>
                    </div>
                `;
            }
        });

        if (portsHtml) {
            container.innerHTML = portsHtml;
        } else {
            showNoPorts();
        }
    }

    function showNoPorts() {
        const container = document.getElementById('availablePorts');
        container.innerHTML = `
            <div class="col-12 text-center">
                <i class="bi bi-diagram-3 text-muted" style="font-size: 2rem;"></i>
                <p class="text-muted mt-2">No gateways configured yet.</p>
                <p class="text-muted small">Add a gateway first to assign ports to users.</p>
            </div>
        `;
    }

    // Auto-set admin permissions when admin role is selected
    document.getElementById('role_id').addEventListener('change', function() {
        const selectedRole = this.options[this.selectedIndex].text.toLowerCase();
        
        if (selectedRole === 'admin') {
            // Check all permissions for admin
            document.querySelectorAll('input[type="checkbox"]:not([name^="assigned_ports"])').forEach(checkbox => {
                if (checkbox.name !== 'is_active') {
                    checkbox.checked = true;
                }
            });
        }
    });

    // Load ports when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadAvailablePorts();
    });
</script>
{% endblock %}
