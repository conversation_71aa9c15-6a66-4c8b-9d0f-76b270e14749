#!/usr/bin/env python3
"""
Test SMS receiving after gateway reboot
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def test_after_reboot():
    """Test SMS API after gateway reboot"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔄 TESTING AFTER GATEWAY REBOOT")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        print(f"📡 Gateway: {gateway.ip_address}:{gateway.tg_sms_port}")
        print(f"🔐 Credentials: {gateway.username}/{gateway.password}")
        
        try:
            # Test connection
            print(f"\n🌐 Testing connection...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            local_ip = sock.getsockname()[0]
            print(f"📍 Connecting from: {local_ip}")
            
            # Read initial response
            initial = sock.recv(1024).decode()
            print(f"📥 Initial: {initial.strip()}")
            
            # Send login
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            login_resp = sock.recv(1024).decode()
            print(f"📥 Login: {login_resp.strip()}")
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed!")
                sock.close()
                return False
            
            print("✅ Login successful!")
            
            # Test SMS commands (should work after reboot)
            print(f"\n📱 Testing SMS commands...")
            gsm_cmd = "Action: smscommand\r\ncommand: gsm show spans\r\n\r\n"
            sock.send(gsm_cmd.encode())
            
            gsm_resp = sock.recv(2048).decode()
            print(f"📥 GSM Response: {gsm_resp.strip()}")
            
            if "GSM span" in gsm_resp or "Power on" in gsm_resp:
                print("✅ SMS commands working!")
            else:
                print("⚠️  SMS commands response unclear")
            
            # Test event subscription (should work after reboot)
            print(f"\n📡 Testing event subscription...")
            event_cmd = "Action: Events\r\n\r\n"
            sock.send(event_cmd.encode())
            
            event_resp = sock.recv(1024).decode()
            print(f"📥 Event response: {event_resp.strip()}")
            
            if "Events: On" in event_resp:
                print("🎉 EVENTS ARE ENABLED! SMS receiving should work!")
                
                # Listen for SMS events
                print(f"\n👂 Listening for SMS events for 15 seconds...")
                print(f"📱 SEND AN SMS TO YOUR GATEWAY NUMBER NOW!")
                
                sock.settimeout(1)
                start_time = time.time()
                event_count = 0
                
                while time.time() - start_time < 15:
                    try:
                        data = sock.recv(4096).decode()
                        if data and "Event:" in data:
                            event_count += 1
                            print(f"\n📨 SMS EVENT {event_count} RECEIVED!")
                            print(f"📄 {data}")
                            
                            if "ReceivedSMS" in data:
                                print(f"🎉 SMS RECEIVING IS WORKING!")
                                sock.close()
                                return True
                                
                    except socket.timeout:
                        print(".", end="", flush=True)
                        continue
                
                print(f"\n📊 Events received: {event_count}")
                sock.close()
                return event_count > 0
                
            elif "Success" in event_resp:
                print("✅ Event subscription successful (checking status...)")
                sock.close()
                return True
            else:
                print("❌ Event subscription still not working")
                sock.close()
                return False
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False

def restart_sms_receiver_after_reboot():
    """Restart SMS receiver after gateway reboot"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔄 RESTARTING SMS RECEIVER AFTER REBOOT")
        print("="*60)
        
        try:
            from app.sms.receiver import SMSReceiver
            
            # Stop any existing receiver
            receiver = getattr(app, 'sms_receiver', None)
            if receiver:
                print("🛑 Stopping existing receiver...")
                receiver.stop_receiver()
                time.sleep(3)
            
            # Start fresh receiver
            print("🚀 Starting fresh SMS receiver...")
            new_receiver = SMSReceiver(app)
            new_receiver.start_receiver()
            app.sms_receiver = new_receiver
            
            # Wait and check status
            time.sleep(5)
            
            print(f"📊 SMS Receiver Status:")
            print(f"   Running: {'✅' if new_receiver.running else '❌'}")
            print(f"   Connected: {'✅' if new_receiver.connected else '❌'}")
            print(f"   Thread alive: {'✅' if (new_receiver.thread and new_receiver.thread.is_alive()) else '❌'}")
            
            if new_receiver.running and new_receiver.connected:
                print(f"🎉 SMS receiver is working after reboot!")
                return True
            else:
                print(f"❌ SMS receiver still has issues")
                return False
                
        except Exception as e:
            print(f"❌ Error restarting receiver: {str(e)}")
            return False

if __name__ == '__main__':
    print("🚀 Post-Reboot SMS Test")
    print("📋 Run this AFTER rebooting the TG SMS gateway")
    
    # Test gateway after reboot
    gateway_ok = test_after_reboot()
    
    if gateway_ok:
        print(f"\n✅ Gateway is working after reboot!")
        
        # Restart SMS receiver
        receiver_ok = restart_sms_receiver_after_reboot()
        
        if receiver_ok:
            print(f"\n🎉 SUCCESS! SMS RECEIVING IS FULLY OPERATIONAL!")
            print(f"📱 Send an SMS to test receiving")
            print(f"🌐 Check your inbox in the web interface")
        else:
            print(f"\n⚠️  Gateway works but receiver needs attention")
    else:
        print(f"\n❌ Gateway still has issues after reboot")
        print(f"💡 Check TG SMS service status in gateway interface")
    
    print("\n" + "="*60)
    print("💡 If still not working after reboot:")
    print("   1. Check TG SMS service is running")
    print("   2. Verify API settings are still enabled")
    print("   3. Check user permissions in gateway")
    print("="*60)
