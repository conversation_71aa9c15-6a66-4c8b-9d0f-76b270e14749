#!/usr/bin/env python3
"""
Advanced Port Assignment System for Multi-GSM Gateways
"""

import sys
import os
sys.path.append('backend')

from multi_gsm_system import app, db, GSMGateway, EnhancedSMSPort, UserPortAssignment
from app import User
from datetime import datetime

class MultiGSMPortManager:
    """Advanced port management for multiple GSM gateways"""
    
    @staticmethod
    def assign_user_to_ports(username, assignments, assigned_by_username=None):
        """
        Assign user to multiple ports across different gateways
        
        assignments format:
        [
            {
                'gateway_name': 'Main Office Gateway',
                'ports': [1, 2, 3],
                'assignment_type': 'exclusive',  # exclusive, shared, backup
                'priority': 1
            },
            {
                'gateway_name': 'Branch Office Gateway', 
                'ports': [1],
                'assignment_type': 'backup',
                'priority': 2
            }
        ]
        """
        with app.app_context():
            try:
                user = User.query.filter_by(username=username).first()
                if not user:
                    return {'success': False, 'error': f'User {username} not found'}
                
                assigned_by = None
                if assigned_by_username:
                    assigned_by = User.query.filter_by(username=assigned_by_username).first()
                
                results = []
                
                for assignment in assignments:
                    gateway = GSMGateway.query.filter_by(name=assignment['gateway_name']).first()
                    if not gateway:
                        results.append({
                            'gateway': assignment['gateway_name'],
                            'success': False,
                            'error': 'Gateway not found'
                        })
                        continue
                    
                    gateway_results = []
                    
                    for port_num in assignment['ports']:
                        port = EnhancedSMSPort.query.filter_by(
                            gateway_id=gateway.id,
                            port_number=str(port_num)
                        ).first()
                        
                        if not port:
                            gateway_results.append({
                                'port': port_num,
                                'success': False,
                                'error': 'Port not found'
                            })
                            continue
                        
                        # Check if assignment already exists
                        existing = UserPortAssignment.query.filter_by(
                            user_id=user.id,
                            port_id=port.id
                        ).first()
                        
                        if existing:
                            # Update existing assignment
                            existing.assignment_type = assignment.get('assignment_type', 'exclusive')
                            existing.priority = assignment.get('priority', 1)
                            existing.is_active = True
                            existing.notes = assignment.get('notes', '')
                            gateway_results.append({
                                'port': port_num,
                                'success': True,
                                'action': 'updated'
                            })
                        else:
                            # Create new assignment
                            new_assignment = UserPortAssignment(
                                user_id=user.id,
                                port_id=port.id,
                                assignment_type=assignment.get('assignment_type', 'exclusive'),
                                priority=assignment.get('priority', 1),
                                assigned_by=assigned_by.id if assigned_by else None,
                                notes=assignment.get('notes', '')
                            )
                            db.session.add(new_assignment)
                            gateway_results.append({
                                'port': port_num,
                                'success': True,
                                'action': 'created'
                            })
                    
                    results.append({
                        'gateway': assignment['gateway_name'],
                        'success': True,
                        'ports': gateway_results
                    })
                
                db.session.commit()
                
                return {
                    'success': True,
                    'user': username,
                    'assignments': results
                }
                
            except Exception as e:
                db.session.rollback()
                return {'success': False, 'error': str(e)}
    
    @staticmethod
    def get_user_assignments(username):
        """Get all port assignments for a user"""
        with app.app_context():
            try:
                user = User.query.filter_by(username=username).first()
                if not user:
                    return {'success': False, 'error': f'User {username} not found'}
                
                assignments = UserPortAssignment.query.filter_by(
                    user_id=user.id,
                    is_active=True
                ).all()
                
                result = {
                    'success': True,
                    'user': username,
                    'total_assignments': len(assignments),
                    'gateways': {}
                }
                
                for assignment in assignments:
                    gateway_name = assignment.port.gateway.name
                    
                    if gateway_name not in result['gateways']:
                        result['gateways'][gateway_name] = {
                            'gateway_ip': assignment.port.gateway.ip_address,
                            'location': assignment.port.gateway.location,
                            'ports': []
                        }
                    
                    result['gateways'][gateway_name]['ports'].append({
                        'port_number': assignment.port.port_number,
                        'assignment_type': assignment.assignment_type,
                        'priority': assignment.priority,
                        'sim_phone': assignment.port.sim_phone_number,
                        'signal_quality': assignment.port.signal_quality,
                        'assigned_at': assignment.assigned_at.isoformat() if assignment.assigned_at else None
                    })
                
                return result
                
            except Exception as e:
                return {'success': False, 'error': str(e)}
    
    @staticmethod
    def get_gateway_overview():
        """Get overview of all gateways and their port assignments"""
        with app.app_context():
            try:
                gateways = GSMGateway.query.all()
                
                result = {
                    'success': True,
                    'total_gateways': len(gateways),
                    'gateways': []
                }
                
                for gateway in gateways:
                    ports = EnhancedSMSPort.query.filter_by(gateway_id=gateway.id).all()
                    
                    gateway_info = {
                        'name': gateway.name,
                        'ip_address': gateway.ip_address,
                        'location': gateway.location,
                        'is_primary': gateway.is_primary,
                        'status': gateway.status,
                        'total_ports': len(ports),
                        'active_ports': len([p for p in ports if p.is_active]),
                        'assigned_ports': 0,
                        'available_ports': 0,
                        'ports': []
                    }
                    
                    for port in ports:
                        assignments = UserPortAssignment.query.filter_by(
                            port_id=port.id,
                            is_active=True
                        ).all()
                        
                        if assignments:
                            gateway_info['assigned_ports'] += 1
                        else:
                            gateway_info['available_ports'] += 1
                        
                        port_info = {
                            'port_number': port.port_number,
                            'status': port.status,
                            'is_active': port.is_active,
                            'sim_phone': port.sim_phone_number,
                            'signal_quality': port.signal_quality,
                            'assignments': []
                        }
                        
                        for assignment in assignments:
                            port_info['assignments'].append({
                                'user': assignment.user.username,
                                'assignment_type': assignment.assignment_type,
                                'priority': assignment.priority,
                                'assigned_at': assignment.assigned_at.isoformat() if assignment.assigned_at else None
                            })
                        
                        gateway_info['ports'].append(port_info)
                    
                    result['gateways'].append(gateway_info)
                
                return result
                
            except Exception as e:
                return {'success': False, 'error': str(e)}

def setup_sample_assignments():
    """Set up sample user assignments for demonstration"""
    with app.app_context():
        print("👥 Setting Up Sample Multi-GSM User Assignments")
        print("=" * 50)
        
        # Sample assignment configurations
        sample_assignments = [
            {
                'username': 'admin',
                'assignments': [
                    {
                        'gateway_name': 'Main Office Gateway',
                        'ports': [1, 2, 3, 4, 5, 6, 7, 8],
                        'assignment_type': 'exclusive',
                        'priority': 1,
                        'notes': 'Full admin access to main gateway'
                    },
                    {
                        'gateway_name': 'Branch Office Gateway',
                        'ports': [1, 2, 3, 4],
                        'assignment_type': 'exclusive',
                        'priority': 1,
                        'notes': 'Admin access to branch gateway'
                    },
                    {
                        'gateway_name': 'Backup Gateway',
                        'ports': list(range(1, 17)),
                        'assignment_type': 'exclusive',
                        'priority': 1,
                        'notes': 'Full admin access to backup gateway'
                    }
                ]
            },
            {
                'username': 'sales_team',
                'assignments': [
                    {
                        'gateway_name': 'Main Office Gateway',
                        'ports': [1, 2],
                        'assignment_type': 'exclusive',
                        'priority': 1,
                        'notes': 'Primary sales communication ports'
                    },
                    {
                        'gateway_name': 'Backup Gateway',
                        'ports': [1, 2],
                        'assignment_type': 'backup',
                        'priority': 2,
                        'notes': 'Backup ports for high-volume sales'
                    }
                ]
            },
            {
                'username': 'support_team',
                'assignments': [
                    {
                        'gateway_name': 'Main Office Gateway',
                        'ports': [3, 4],
                        'assignment_type': 'exclusive',
                        'priority': 1,
                        'notes': 'Customer support dedicated ports'
                    },
                    {
                        'gateway_name': 'Branch Office Gateway',
                        'ports': [1, 2],
                        'assignment_type': 'shared',
                        'priority': 1,
                        'notes': 'Shared support access to branch'
                    }
                ]
            },
            {
                'username': 'marketing_team',
                'assignments': [
                    {
                        'gateway_name': 'Main Office Gateway',
                        'ports': [5, 6],
                        'assignment_type': 'exclusive',
                        'priority': 1,
                        'notes': 'Marketing campaign ports'
                    },
                    {
                        'gateway_name': 'Backup Gateway',
                        'ports': [3, 4, 5, 6],
                        'assignment_type': 'shared',
                        'priority': 2,
                        'notes': 'High-volume campaign backup'
                    }
                ]
            },
            {
                'username': 'operations_team',
                'assignments': [
                    {
                        'gateway_name': 'Main Office Gateway',
                        'ports': [7, 8],
                        'assignment_type': 'exclusive',
                        'priority': 1,
                        'notes': 'Internal operations and alerts'
                    },
                    {
                        'gateway_name': 'Branch Office Gateway',
                        'ports': [3, 4],
                        'assignment_type': 'exclusive',
                        'priority': 1,
                        'notes': 'Branch operations management'
                    }
                ]
            }
        ]
        
        # Apply assignments
        for user_config in sample_assignments:
            print(f"\n👤 Assigning ports to {user_config['username']}:")
            
            result = MultiGSMPortManager.assign_user_to_ports(
                username=user_config['username'],
                assignments=user_config['assignments'],
                assigned_by_username='admin'
            )
            
            if result['success']:
                for gateway_result in result['assignments']:
                    if gateway_result['success']:
                        gateway_name = gateway_result['gateway']
                        port_count = len(gateway_result['ports'])
                        print(f"  ✅ {gateway_name}: {port_count} ports assigned")
                    else:
                        print(f"  ❌ {gateway_result['gateway']}: {gateway_result['error']}")
            else:
                print(f"  ❌ Failed: {result['error']}")

def demonstrate_multi_gsm_capabilities():
    """Demonstrate multi-GSM capabilities"""
    print(f"\n🚀 Multi-GSM Gateway Capabilities Demonstration")
    print("=" * 55)
    
    # Show gateway overview
    overview = MultiGSMPortManager.get_gateway_overview()
    if overview['success']:
        print(f"\n📊 Gateway Overview:")
        print(f"   Total Gateways: {overview['total_gateways']}")
        
        for gateway in overview['gateways']:
            print(f"\n   🏢 {gateway['name']} ({gateway['ip_address']}):")
            print(f"      Location: {gateway['location']}")
            print(f"      Status: {gateway['status']}")
            print(f"      Primary: {'Yes' if gateway['is_primary'] else 'No'}")
            print(f"      Ports: {gateway['total_ports']} total, {gateway['assigned_ports']} assigned, {gateway['available_ports']} available")
    
    # Show user assignments
    print(f"\n👥 User Assignment Examples:")
    for username in ['sales_team', 'support_team', 'marketing_team']:
        assignments = MultiGSMPortManager.get_user_assignments(username)
        if assignments['success']:
            print(f"\n   👤 {username}:")
            print(f"      Total Assignments: {assignments['total_assignments']}")
            for gateway_name, gateway_info in assignments['gateways'].items():
                ports = [p['port_number'] for p in gateway_info['ports']]
                print(f"      📡 {gateway_name}: Ports {ports}")

if __name__ == "__main__":
    setup_sample_assignments()
    demonstrate_multi_gsm_capabilities()
    
    print(f"\n🎉 Multi-GSM Port Assignment System Complete!")
    print(f"✅ Multiple GSM gateway support")
    print(f"✅ Advanced user-to-port assignments")
    print(f"✅ Priority-based port allocation")
    print(f"✅ Exclusive, shared, and backup assignments")
    print(f"✅ Cross-gateway user access")
    print(f"✅ Scalable to unlimited gateways")
