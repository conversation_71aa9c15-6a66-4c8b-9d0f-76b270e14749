#!/usr/bin/env python3
"""
Test page access with direct login simulation
"""

import sys
import os
sys.path.append('backend')

from app import app, db, User
from flask_login import login_user, current_user

def test_page_access():
    with app.app_context():
        print("🧪 Testing Page Access with Direct Login")
        print("=" * 50)
        
        # Test with test1 user
        test1 = User.query.filter_by(username='test1').first()
        if test1:
            print(f"\n👤 Testing access for user: {test1.username}")
            print(f"   Permissions: send_sms={test1.can_send_sms}, view_inbox={test1.can_view_inbox}, view_outbox={test1.can_view_outbox}")
            print(f"   Assigned ports: {test1.get_assigned_ports()}")
            
            # Test with Flask test client
            with app.test_client() as client:
                with client.session_transaction() as sess:
                    # Manually set session to simulate login
                    sess['_user_id'] = str(test1.id)
                    sess['_fresh'] = True
                
                # Test accessing different pages
                pages_to_test = [
                    ('/', 'Dashboard'),
                    ('/inbox', 'Inbox'),
                    ('/outbox', 'Outbox'),
                    ('/compose', 'Compose'),
                    ('/settings', 'Settings'),
                    ('/users', 'User Management'),
                    ('/ports', 'Port Management'),
                ]
                
                for url, name in pages_to_test:
                    try:
                        response = client.get(url, follow_redirects=False)
                        if response.status_code == 200:
                            print(f"   ✅ {name} ({url}): Accessible (200)")
                        elif response.status_code == 302:
                            location = response.headers.get('Location', '')
                            if 'login' in location:
                                print(f"   ❌ {name} ({url}): Redirected to login (no permission)")
                            else:
                                print(f"   ⚠️ {name} ({url}): Redirected to {location}")
                        else:
                            print(f"   ⚠️ {name} ({url}): Status {response.status_code}")
                    except Exception as e:
                        print(f"   ❌ {name} ({url}): Error - {str(e)}")

def test_permission_decorator():
    """Test the permission decorator directly"""
    with app.app_context():
        print(f"\n🔍 Testing Permission Decorator Logic")
        print("=" * 30)
        
        # Get test1 user
        test1 = User.query.filter_by(username='test1').first()
        if test1:
            print(f"Testing with user: {test1.username}")
            
            # Test permission checks that are used in routes
            permissions_to_test = [
                'can_view_inbox',
                'can_view_outbox', 
                'can_send_sms',
                'can_manage_settings',
                'can_manage_users',
                'can_manage_ports'
            ]
            
            for perm in permissions_to_test:
                has_perm = test1.has_permission(perm)
                direct_attr = getattr(test1, perm, None)
                print(f"   {perm}: has_permission()={has_perm}, direct_attr={direct_attr}")

if __name__ == "__main__":
    test_page_access()
    test_permission_decorator()
