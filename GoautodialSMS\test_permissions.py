#!/usr/bin/env python3
"""
Test script to verify user permissions are working correctly
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_login(username, password):
    """Test login and return session"""
    session = requests.Session()

    # Get login page first to get any CSRF tokens
    login_page = session.get(f"{BASE_URL}/login")
    print(f"Login page status: {login_page.status_code}")

    # Login
    login_data = {
        'username': username,
        'password': password
    }

    login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
    print(f"Login response status: {login_response.status_code}")

    if login_response.status_code == 302:  # Redirect means successful login
        print(f"✅ Successfully logged in as {username}")
        return session
    else:
        print(f"❌ Failed to login as {username}")
        return None

def test_permissions(session, username):
    """Test user permissions"""
    print(f"\n🔍 Testing permissions for {username}:")

    # Test debug permissions endpoint
    perm_response = session.get(f"{BASE_URL}/debug/user_permissions")
    if perm_response.status_code == 200:
        perm_data = perm_response.json()
        print(f"✅ Permission check successful")
        print(f"   Username: {perm_data['user_info']['username']}")
        print(f"   Role: {perm_data['user_info']['role']}")
        print(f"   Assigned Ports: {perm_data['user_info']['assigned_ports']}")
        print(f"   Permissions:")
        for perm, details in perm_data['user_info']['permissions'].items():
            status = "✅" if details['has_permission'] else "❌"
            print(f"     {status} {perm}: {details['has_permission']}")
    else:
        print(f"❌ Permission check failed: {perm_response.status_code}")

    # Test access to different pages
    pages_to_test = [
        ('/inbox', 'Inbox'),
        ('/outbox', 'Outbox'),
        ('/compose', 'Compose'),
        ('/settings', 'Settings'),
        ('/users', 'User Management'),
        ('/ports', 'Port Management'),
        ('/export', 'Export'),
        ('/reports', 'Reports')
    ]

    print(f"\n📄 Testing page access for {username}:")
    for url, name in pages_to_test:
        response = session.get(f"{BASE_URL}{url}", allow_redirects=False)
        if response.status_code == 200:
            print(f"   ✅ {name}: Accessible")
        elif response.status_code == 302:
            print(f"   ❌ {name}: Redirected (no permission)")
        else:
            print(f"   ⚠️ {name}: Status {response.status_code}")

def main():
    print("🧪 Testing SMS System Permissions")
    print("=" * 50)

    # Test admin user
    print("\n1️⃣ Testing Admin User")
    admin_session = test_login('admin', 'admin123')
    if admin_session:
        test_permissions(admin_session, 'admin')

    # Test regular users
    print("\n2️⃣ Testing Test1 User (Port 1 assigned)")
    user1_session = test_login('test1', 'test123')
    if user1_session:
        test_permissions(user1_session, 'test1')

    print("\n3️⃣ Testing Diet User (Port 2 assigned, has export)")
    user2_session = test_login('diet', 'test123')
    if user2_session:
        test_permissions(user2_session, 'diet')

    print("\n4️⃣ Testing Test2 User (No ports assigned)")
    user3_session = test_login('test2', 'test123')
    if user3_session:
        test_permissions(user3_session, 'test2')

    print("\n✅ Permission testing completed!")

if __name__ == "__main__":
    main()
