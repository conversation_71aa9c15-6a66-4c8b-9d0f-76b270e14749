"""
Database models for Modern SMS Manager
"""
from datetime import datetime, timezone
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class Role(db.Model):
    """User roles"""
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    users = db.relationship('User', backref='role', lazy='dynamic')
    
    def __repr__(self):
        return f'<Role {self.name}>'

class User(UserMixin, db.Model):
    """User model"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)
    
    # Foreign Keys
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'))
    
    # Permissions
    can_send_sms = db.Column(db.Boolean, default=True)
    can_view_inbox = db.Column(db.Boolean, default=True)
    can_view_outbox = db.Column(db.Boolean, default=True)
    can_manage_gateways = db.Column(db.Boolean, default=False)
    can_manage_users = db.Column(db.Boolean, default=False)
    can_view_reports = db.Column(db.Boolean, default=False)
    can_export_data = db.Column(db.Boolean, default=False)
    
    # Port assignments (JSON field for flexibility)
    assigned_ports = db.Column(db.JSON)
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password"""
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission):
        """Check if user has specific permission"""
        if self.role and self.role.name == 'admin':
            return True
        return getattr(self, f'can_{permission}', False)
    
    def get_assigned_ports(self):
        """Get list of assigned ports"""
        return self.assigned_ports or []
    
    def can_use_port(self, port_number, gateway_id=None):
        """Check if user can use specific port - STRICT enforcement"""
        if self.role and self.role.name == 'admin':
            return True

        assigned = self.get_assigned_ports()
        if not assigned:
            return False  # If no assignments, deny access (strict enforcement)

        # Check if port is in assigned list
        for assignment in assigned:
            if assignment.get('port') == str(port_number):
                if gateway_id is None or assignment.get('gateway_id') == gateway_id:
                    return True
        return False
    
    @property
    def full_name(self):
        """Get full name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'role': self.role.name if self.role else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'

class GSMGateway(db.Model):
    """GSM Gateway model"""
    __tablename__ = 'gsm_gateways'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    ip_address = db.Column(db.String(15), nullable=False, unique=True, index=True)
    api_port = db.Column(db.Integer, default=80)
    tg_sms_port = db.Column(db.Integer, default=5038)
    username = db.Column(db.String(50), nullable=False)
    password = db.Column(db.String(100), nullable=False)
    max_ports = db.Column(db.Integer, default=8)
    status = db.Column(db.Enum('active', 'inactive', 'maintenance'), default='active')
    location = db.Column(db.String(100))
    description = db.Column(db.Text)
    is_primary = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    ports = db.relationship('SMSPort', backref='gateway', lazy='dynamic', cascade='all, delete-orphan')
    messages = db.relationship('SMSMessage', backref='gateway', lazy='dynamic')
    
    def get_active_ports(self):
        """Get active ports for this gateway"""
        return self.ports.filter_by(is_active=True).all()
    
    def get_port_status_summary(self):
        """Get summary of port statuses"""
        ports = self.ports.all()
        summary = {
            'total': len(ports),
            'active': len([p for p in ports if p.status == 'active']),
            'sim_only': len([p for p in ports if p.status == 'sim_only']),
            'no_sim': len([p for p in ports if p.status == 'no_sim']),
            'unknown': len([p for p in ports if p.status == 'unknown'])
        }
        return summary
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'ip_address': self.ip_address,
            'api_port': self.api_port,
            'tg_sms_port': self.tg_sms_port,
            'username': self.username,
            'max_ports': self.max_ports,
            'status': self.status,
            'location': self.location,
            'description': self.description,
            'is_primary': self.is_primary,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'port_summary': self.get_port_status_summary()
        }
    
    def __repr__(self):
        return f'<GSMGateway {self.name}>'

class SMSPort(db.Model):
    """SMS Port model"""
    __tablename__ = 'sms_ports'

    id = db.Column(db.Integer, primary_key=True)
    port_number = db.Column(db.String(10), nullable=False, index=True)
    status = db.Column(db.Enum('active', 'sim_only', 'no_sim', 'unknown', 'error'), default='unknown')
    network_name = db.Column(db.String(100))
    signal_strength = db.Column(db.String(20))
    phone_number = db.Column(db.String(20))
    sim_imsi = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True)
    last_checked = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Foreign Keys
    gateway_id = db.Column(db.Integer, db.ForeignKey('gsm_gateways.id'), nullable=False)

    # Relationships
    sent_messages = db.relationship('SMSMessage',
                                  foreign_keys='SMSMessage.port_id',
                                  backref='port', lazy='dynamic')

    # Unique constraint for port number per gateway
    __table_args__ = (db.UniqueConstraint('gateway_id', 'port_number', name='unique_gateway_port'),)

    def get_full_port_name(self):
        """Get full port identifier"""
        return f"Gateway {self.gateway_id} - Port {self.port_number}"

    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'port_number': self.port_number,
            'status': self.status,
            'network_name': self.network_name,
            'signal_strength': self.signal_strength,
            'phone_number': self.phone_number,
            'sim_imsi': self.sim_imsi,
            'is_active': self.is_active,
            'last_checked': self.last_checked.isoformat() if self.last_checked else None,
            'gateway_id': self.gateway_id,
            'gateway_name': self.gateway.name if self.gateway else None
        }

    def __repr__(self):
        return f'<SMSPort {self.gateway_id}:{self.port_number}>'

class SMSMessage(db.Model):
    """SMS Message model"""
    __tablename__ = 'sms_messages'

    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.String(100), unique=True, nullable=False, index=True)
    direction = db.Column(db.Enum('inbound', 'outbound'), nullable=False, index=True)
    phone_number = db.Column(db.String(20), nullable=False, index=True)
    content = db.Column(db.Text, nullable=False)
    status = db.Column(db.Enum('pending', 'sent', 'delivered', 'failed', 'received'),
                      default='pending', index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    sent_at = db.Column(db.DateTime)
    delivered_at = db.Column(db.DateTime)

    # SMS specific fields
    smsc = db.Column(db.String(50))
    encoding = db.Column(db.String(20), default='utf-8')
    message_parts = db.Column(db.Integer, default=1)

    # Foreign Keys
    gateway_id = db.Column(db.Integer, db.ForeignKey('gsm_gateways.id'))
    port_id = db.Column(db.Integer, db.ForeignKey('sms_ports.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    reply_to_id = db.Column(db.Integer, db.ForeignKey('sms_messages.id'))

    # Relationships
    user = db.relationship('User', backref='messages')
    replies = db.relationship('SMSMessage', backref=db.backref('reply_to', remote_side=[id]))

    def get_conversation_messages(self):
        """Get all messages in this conversation"""
        return SMSMessage.query.filter_by(phone_number=self.phone_number)\
                              .order_by(SMSMessage.created_at.asc()).all()

    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'message_id': self.message_id,
            'direction': self.direction,
            'phone_number': self.phone_number,
            'content': self.content,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'delivered_at': self.delivered_at.isoformat() if self.delivered_at else None,
            'gateway_id': self.gateway_id,
            'port_id': self.port_id,
            'user_id': self.user_id,
            'reply_to_id': self.reply_to_id,
            'message_parts': self.message_parts
        }

    def __repr__(self):
        return f'<SMSMessage {self.message_id}>'
