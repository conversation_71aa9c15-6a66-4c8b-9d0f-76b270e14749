#!/usr/bin/env python3
"""
Modern SMS Manager - Main Application Entry Point
"""
import os
import sys
from app import create_app, db
from app.models import User, Role, GSMGateway, SMSPort
from config import Config

def create_default_data():
    """Create default data if not exists"""
    try:
        # Create default gateway if none exists
        if GSMGateway.query.count() == 0:
            print("Creating default gateway...")
            
            default_config = Config.DEFAULT_GSM_CONFIG
            
            gateway = GSMGateway(
                name='Default Gateway',
                ip_address=default_config['ip'],
                api_port=default_config['api_port'],
                tg_sms_port=default_config['tg_port'],
                username=default_config['username'],
                password=default_config['password'],
                max_ports=default_config['max_ports'],
                status='active',
                location='Main Office',
                description='Default GSM Gateway',
                is_primary=True
            )
            
            db.session.add(gateway)
            db.session.flush()  # Get gateway ID
            
            # Create ports for default gateway
            for port_num in range(1, default_config['max_ports'] + 1):
                port = SMSPort(
                    port_number=str(port_num),
                    gateway_id=gateway.id,
                    status='unknown',
                    is_active=True
                )
                db.session.add(port)
            
            db.session.commit()
            print(f"✅ Created default gateway with {default_config['max_ports']} ports")
        
        # Update admin user permissions
        admin_user = User.query.filter_by(username='admin').first()
        if admin_user:
            admin_user.can_send_sms = True
            admin_user.can_view_inbox = True
            admin_user.can_view_outbox = True
            admin_user.can_manage_gateways = True
            admin_user.can_manage_users = True
            admin_user.can_view_reports = True
            admin_user.can_export_data = True
            db.session.commit()
            print("✅ Updated admin user permissions")
        
    except Exception as e:
        print(f"❌ Error creating default data: {str(e)}")
        db.session.rollback()

def main():
    """Main application entry point"""
    # Get configuration environment
    config_name = os.environ.get('FLASK_ENV', 'development')
    
    # Create application
    app = create_app(config_name)
    
    with app.app_context():
        # Create default data
        create_default_data()
        
        print("\n" + "="*60)
        print("🚀 Modern SMS Manager Starting...")
        print("="*60)
        print(f"📊 Environment: {config_name}")
        print(f"🗄️  Database: {app.config['SQLALCHEMY_DATABASE_URI']}")
        print(f"🔐 Admin Login: admin / admin123")
        print(f"🌐 URL: http://127.0.0.1:5000")
        print("="*60)
        
        # Check database connection
        try:
            from sqlalchemy import text
            db.session.execute(text('SELECT 1'))
            print("✅ Database connection successful")
        except Exception as e:
            print(f"❌ Database connection failed: {str(e)}")
            print("Please check your database configuration in .env file")
            sys.exit(1)
        
        # Show statistics
        try:
            user_count = User.query.count()
            gateway_count = GSMGateway.query.count()
            port_count = SMSPort.query.count()
            
            print(f"👥 Users: {user_count}")
            print(f"🌐 Gateways: {gateway_count}")
            print(f"📡 Ports: {port_count}")
            print("="*60)
            
        except Exception as e:
            print(f"⚠️  Could not load statistics: {str(e)}")
    
    # Run application
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=app.config.get('DEBUG', False),
        use_reloader=False  # Disable reloader to avoid issues
    )

if __name__ == '__main__':
    main()
