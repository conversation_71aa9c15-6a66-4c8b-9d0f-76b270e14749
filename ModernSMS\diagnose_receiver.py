#!/usr/bin/env python3
"""
Diagnose SMS receiver issues
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway, SMSMessage

def test_gateway_connection():
    """Test connection to GSM gateway"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔍 SMS RECEIVER DIAGNOSTICS")
        print("="*60)
        
        # Get primary gateway
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        
        if not gateway:
            print("❌ No active primary gateway found!")
            return False
        
        print(f"📡 Primary Gateway: {gateway.name}")
        print(f"   IP Address: {gateway.ip_address}")
        print(f"   TG SMS Port: {gateway.tg_sms_port}")
        print(f"   Username: {gateway.username}")
        print(f"   Status: {gateway.status}")
        
        # Test network connectivity
        print(f"\n🌐 Testing network connectivity...")
        try:
            # Test ping-like connection
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((gateway.ip_address, gateway.tg_sms_port))
            sock.close()
            
            if result == 0:
                print(f"✅ Network connection to {gateway.ip_address}:{gateway.tg_sms_port} successful")
            else:
                print(f"❌ Network connection to {gateway.ip_address}:{gateway.tg_sms_port} failed")
                print(f"   Error code: {result}")
                return False
                
        except Exception as e:
            print(f"❌ Network test failed: {str(e)}")
            return False
        
        # Test TG SMS protocol connection
        print(f"\n📱 Testing TG SMS protocol connection...")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Send login command
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            # Read response
            response = sock.recv(1024).decode()
            print(f"📥 Gateway response:")
            print(response)
            
            if "Response: Success" in response:
                print("✅ TG SMS authentication successful")
                
                # Send a test command
                test_cmd = "Action: Command\r\nCommand: gsm show spans\r\n\r\n"
                sock.send(test_cmd.encode())
                
                response = sock.recv(1024).decode()
                print(f"📥 Test command response:")
                print(response)
                
            else:
                print("❌ TG SMS authentication failed")
                print("   Check username/password in gateway settings")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ TG SMS protocol test failed: {str(e)}")
            return False
        
        # Check received messages in database
        print(f"\n📊 Checking received messages in database...")
        received_count = SMSMessage.query.filter_by(direction='inbound').count()
        print(f"   Total received messages: {received_count}")
        
        if received_count > 0:
            recent_messages = SMSMessage.query.filter_by(direction='inbound').order_by(SMSMessage.created_at.desc()).limit(5).all()
            print(f"   Recent received messages:")
            for msg in recent_messages:
                print(f"     - From {msg.phone_number}: {msg.content[:30]}... ({msg.created_at})")
        
        # Check SMS receiver status
        print(f"\n🔧 SMS Receiver Status:")
        try:
            # This would need to be called from within the app context
            print("   SMS receiver status check would need app instance")
        except Exception as e:
            print(f"   Error checking receiver: {str(e)}")
        
        print(f"\n✅ Diagnostics completed!")
        return True

def test_manual_sms_receive():
    """Test manual SMS receiving simulation"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🧪 Testing manual SMS receive simulation...")
        
        try:
            from app.sms.receiver import SMSReceiver
            
            # Create a test SMS event
            test_event = """Event: ReceivedSMS
Sender: +639123456789
Content: Test%20message%20from%20diagnostics
Recvtime: 2024-01-01 12:00:00
GsmPort: 1
--END SMS EVENT--"""
            
            # Create receiver instance
            receiver = SMSReceiver(app)
            
            # Process the test event
            receiver._process_sms_event(test_event)
            
            print("✅ Manual SMS receive test completed")
            
        except Exception as e:
            print(f"❌ Manual SMS receive test failed: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    success = test_gateway_connection()
    
    if success:
        test_manual_sms_receive()
    
    print("\n" + "="*60)
    print("🎯 RECOMMENDATIONS:")
    print("="*60)
    print("1. Ensure GSM gateway is powered on and connected to network")
    print("2. Verify IP address and TG SMS port are correct")
    print("3. Check TG SMS username/password in gateway settings")
    print("4. Ensure TG SMS service is enabled on the gateway")
    print("5. Check firewall settings allow connection to TG SMS port")
    print("6. Try restarting SMS receiver from dashboard")
    print("="*60)
