"""
SMS Receiver for handling incoming SMS messages from GSM gateway
"""
import socket
import threading
import time
import urllib.parse
import uuid
from datetime import datetime
from flask import current_app
from app import db
from app.models import SMSMessage, GSMGateway, SMSPort

class SMSReceiver:
    """Handles incoming SMS messages from GSM gateway"""

    def __init__(self, app=None):
        self.socket = None
        self.connected = False
        self.running = False
        self.buffer = ""
        self.thread = None
        self.app = app
    
    def start_receiver(self):
        """Start SMS receiver in background thread"""
        if self.running:
            current_app.logger.warning("SMS receiver already running")
            return
        
        self.thread = threading.Thread(target=self._receiver_loop, daemon=True)
        self.thread.start()
        if self.app:
            self.app.logger.info("📱 SMS receiver started")
    
    def stop_receiver(self):
        """Stop SMS receiver"""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        if self.app:
            self.app.logger.info("📱 SMS receiver stopped")
    
    def _receiver_loop(self):
        """Main receiver loop"""
        self.running = True

        # Use the app context for database operations
        with self.app.app_context():
            while self.running:
                try:
                    # Get primary gateway for TG SMS connection
                    gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
                    if not gateway:
                        self.app.logger.warning("No active primary gateway found for SMS receiving")
                        time.sleep(30)  # Wait 30 seconds before retrying
                        continue

                    # Connect to TG SMS server
                    if self._connect_to_gateway(gateway):
                        self._listen_for_messages()
                    else:
                        self.app.logger.error("Failed to connect to TG SMS server")
                        time.sleep(10)  # Wait 10 seconds before retrying

                except Exception as e:
                    self.app.logger.error(f"SMS receiver error: {str(e)}")
                    time.sleep(10)
    
    def _connect_to_gateway(self, gateway):
        """Connect to GSM gateway TG SMS server"""
        try:
            current_app.logger.info(f"Connecting to TG SMS server at {gateway.ip_address}:{gateway.tg_sms_port}")

            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((gateway.ip_address, gateway.tg_sms_port))

            # Read initial response (Asterisk Call Manager greeting)
            initial_response = self.socket.recv(1024).decode()
            current_app.logger.debug(f"Initial response: {initial_response.strip()}")

            # Send login credentials
            login_msg = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            self.socket.send(login_msg.encode())

            # Wait for login response
            login_response = self.socket.recv(1024).decode()
            current_app.logger.debug(f"Login response: {login_response.strip()}")

            if "Response: Success" in login_response:
                current_app.logger.info("✅ Successfully connected to TG SMS server")

                # Subscribe to SMS events (use method that works)
                event_cmd = "Action: Events\r\n\r\n"
                self.socket.send(event_cmd.encode())

                # Read event subscription response
                event_response = self.socket.recv(1024).decode()
                current_app.logger.debug(f"Event response: {event_response.strip()}")

                self.connected = True
                return True
            else:
                current_app.logger.error(f"TG SMS login failed: {login_response}")
                return False
                
        except Exception as e:
            current_app.logger.error(f"Error connecting to TG SMS server: {str(e)}")
            return False
    
    def _listen_for_messages(self):
        """Listen for incoming SMS messages"""
        self.buffer = ""
        current_app.logger.info("📱 Listening for incoming SMS messages...")
        
        try:
            while self.running and self.connected:
                self.socket.settimeout(1.0)
                
                try:
                    data = self.socket.recv(4096).decode(errors='ignore')
                    if not data:
                        current_app.logger.warning("Connection closed by TG SMS server")
                        break
                    
                    self.buffer += data
                    current_app.logger.debug(f"📨 Raw data: {repr(data)}")
                    
                    # Process complete events (events end with --END SMS EVENT--)
                    while "--END SMS EVENT--" in self.buffer:
                        event, self.buffer = self.buffer.split("--END SMS EVENT--", 1)

                        if "Event: ReceivedSMS" in event:
                            current_app.logger.info("🎉 SMS event received!")
                            current_app.logger.debug(f"SMS Event: {event}")
                            self._process_received_sms(event)
                        
                except socket.timeout:
                    # Normal timeout, continue listening
                    continue
                    
        except Exception as e:
            current_app.logger.error(f"Error listening for SMS: {str(e)}")
        finally:
            self._disconnect()
    
    def _process_received_sms(self, event):
        """Process received SMS event"""
        try:
            # Parse SMS data
            sms_data = self._parse_sms_event(event)
            if not sms_data:
                current_app.logger.error("Failed to parse SMS event")
                return
            
            # Save to database
            self._save_received_sms(sms_data)
            
        except Exception as e:
            current_app.logger.error(f"Error processing received SMS: {str(e)}")
    
    def _parse_sms_event(self, event):
        """Parse SMS event data"""
        try:
            lines = event.strip().splitlines()
            sms_info = {}
            
            for line in lines:
                if ": " in line:
                    key, val = line.split(": ", 1)
                    sms_info[key.strip()] = val.strip()
            
            # Extract required fields
            sender = sms_info.get('Sender', '')
            content_encoded = sms_info.get('Content', '')
            content = urllib.parse.unquote(content_encoded)
            # Handle both GsmPort and GsmSpan (TG SMS uses GsmSpan)
            gsm_port = sms_info.get('GsmSpan', sms_info.get('GsmPort', '1'))
            message_id = sms_info.get('ID', '').strip()
            # Generate unique ID if empty
            if not message_id:
                import time
                message_id = f"sms_{int(time.time())}_{gsm_port}_{sender.replace('+', '')}"
            
            if not sender or not content:
                current_app.logger.error("Missing required SMS fields")
                return None
            
            return {
                'sender': sender,
                'content': content,
                'gsm_port': gsm_port,
                'message_id': message_id,
                'raw_data': sms_info
            }
            
        except Exception as e:
            current_app.logger.error(f"Error parsing SMS event: {str(e)}")
            return None
    
    def _save_received_sms(self, sms_data):
        """Save received SMS to database"""
        try:
            with current_app.app_context():
                # Check if message already exists (only if message_id is not empty)
                if sms_data['message_id'] and sms_data['message_id'].strip():
                    existing = SMSMessage.query.filter_by(message_id=sms_data['message_id']).first()
                    if existing:
                        current_app.logger.warning(f"SMS {sms_data['message_id']} already exists")
                        return
                
                # Find the port
                port = SMSPort.query.filter_by(port_number=sms_data['gsm_port']).first()
                if not port:
                    # Create a default port if not found
                    gateway = GSMGateway.query.filter_by(is_primary=True).first()
                    if gateway:
                        port = SMSPort(
                            port_number=sms_data['gsm_port'],
                            gateway_id=gateway.id,
                            status='active',
                            is_active=True
                        )
                        db.session.add(port)
                        db.session.flush()
                
                # Create SMS message
                sms_message = SMSMessage(
                    message_id=sms_data['message_id'],
                    direction='inbound',
                    phone_number=sms_data['sender'],
                    content=sms_data['content'],
                    status='received',
                    gateway_id=port.gateway_id if port else None,
                    port_id=port.id if port else None,
                    created_at=datetime.now()
                )
                
                db.session.add(sms_message)
                db.session.commit()
                
                current_app.logger.info(f"✅ SMS saved: {sms_data['sender']} -> {sms_data['content'][:50]}...")
                
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error saving received SMS: {str(e)}")
    
    def _disconnect(self):
        """Disconnect from TG SMS server"""
        self.connected = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None

# SMS receiver will be created with app context in __init__.py
