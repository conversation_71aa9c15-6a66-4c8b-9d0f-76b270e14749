"""
Configuration settings for Modern SMS Manager
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Base configuration"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Database Configuration
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = int(os.environ.get('DB_PORT', 3306))
    DB_NAME = os.environ.get('DB_NAME', 'modern_sms')
    DB_USER = os.environ.get('DB_USER', 'sms_user')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', 'sms_password')
    
    # SQLAlchemy Configuration
    # Use SQLite for development if MySQL not available
    if os.environ.get('USE_SQLITE', 'false').lower() == 'true':
        SQLALCHEMY_DATABASE_URI = 'sqlite:///modern_sms.db'
        SQLALCHEMY_ENGINE_OPTIONS = {}
    else:
        SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        SQLALCHEMY_ENGINE_OPTIONS = {
            'pool_pre_ping': True,
            'pool_recycle': 300,
            'connect_args': {
                'charset': 'utf8mb4',
                'connect_timeout': 60,
                'read_timeout': 60,
                'write_timeout': 60
            }
        }
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Session Configuration
    SESSION_TIMEOUT = int(os.environ.get('SESSION_TIMEOUT', 3600))
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = SESSION_TIMEOUT
    
    # Application Settings
    APP_NAME = os.environ.get('APP_NAME', 'Modern SMS Manager')
    APP_VERSION = os.environ.get('APP_VERSION', '1.0.0')
    ITEMS_PER_PAGE = int(os.environ.get('ITEMS_PER_PAGE', 20))
    MAX_LOGIN_ATTEMPTS = int(os.environ.get('MAX_LOGIN_ATTEMPTS', 5))
    
    # Default GSM Configuration (Fallback)
    DEFAULT_GSM_CONFIG = {
        'ip': os.environ.get('DEFAULT_GSM_IP', '************'),
        'username': os.environ.get('DEFAULT_GSM_USERNAME', 'apiuser'),
        'password': os.environ.get('DEFAULT_GSM_PASSWORD', 'apipass'),
        'api_port': int(os.environ.get('DEFAULT_GSM_API_PORT', 80)),
        'tg_port': int(os.environ.get('DEFAULT_GSM_TG_PORT', 5038)),
        'max_ports': int(os.environ.get('DEFAULT_GSM_MAX_PORTS', 8))
    }

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    FLASK_ENV = 'development'

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    FLASK_ENV = 'production'
    SESSION_COOKIE_SECURE = True

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
