{% extends "base.html" %}

{% block title %}SMS Ports - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-hdd-network"></i> SMS Port Management
            </h1>
            <div class="btn-group">
                <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Settings
                </a>
                <button class="btn btn-primary" onclick="refreshPorts()">
                    <i class="bi bi-arrow-clockwise"></i> Refresh Status
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Port Configuration -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> Port Configuration
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('configure_ports') }}">
                    <div class="mb-3">
                        <label for="max_ports" class="form-label">Maximum Ports</label>
                        <input type="number" class="form-control" id="max_ports" name="max_ports"
                               value="{{ max_ports }}" min="1" max="32" required>
                        <div class="form-text">Configure the maximum number of GSM ports (1-32)</div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update Configuration
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-search"></i> Port Detection
                </h5>
            </div>
            <div class="card-body">
                <p>Detect and initialize GSM ports from hardware.</p>
                <form method="POST" action="{{ url_for('detect_ports') }}">
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-arrow-clockwise"></i> Detect Ports
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Port Status Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-hdd-network fs-1"></i>
                <h4>{{ ports|length }}</h4>
                <small>Total Ports</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-check-circle fs-1"></i>
                <h4>{{ ports|selectattr('is_active', 'equalto', true)|list|length }}</h4>
                <small>Active Ports</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <i class="bi bi-exclamation-triangle fs-1"></i>
                <h4>{{ ports|selectattr('status', 'equalto', 'unknown')|list|length }}</h4>
                <small>Unknown Status</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-people fs-1"></i>
                <h4>{{ max_ports }}</h4>
                <small>Max Configured</small>
            </div>
        </div>
    </div>
</div>

<!-- Ports Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list"></i> SMS Ports Configuration
                </h5>
            </div>
            <div class="card-body">
                {% if ports %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Port Number</th>
                                    <th>Status</th>
                                    <th>Network</th>
                                    <th>Signal Quality</th>
                                    <th>Messages</th>
                                    <th>Users</th>
                                    <th>Active</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for port in ports %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">Port {{ port.port_number }}</strong>
                                    </td>
                                    <td>
                                        {% if port.status == 'active' %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle"></i> Active
                                            </span>
                                        {% elif port.status == 'inactive' %}
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle"></i> Inactive
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning">
                                                <i class="bi bi-question-circle"></i> {{ port.status|title or 'Unknown' }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>{{ port.network_name or 'Unknown' }}</td>
                                    <td>{{ port.signal_quality or 'Unknown' }}</td>
                                    <td>
                                        {% if port_stats[port.port_number] %}
                                            <small>
                                                Total: {{ port_stats[port.port_number].total_messages }}<br>
                                                In: {{ port_stats[port.port_number].inbound_messages }} |
                                                Out: {{ port_stats[port.port_number].outbound_messages }}
                                            </small>
                                        {% else %}
                                            <small class="text-muted">No messages</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if port_stats[port.port_number] %}
                                            {{ port_stats[port.port_number].assigned_users }} users
                                        {% else %}
                                            0 users
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if port.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <form method="POST" action="{{ url_for('toggle_port_status', port_number=port.port_number) }}" style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-outline-{{ 'danger' if port.is_active else 'success' }}">
                                                    <i class="bi bi-{{ 'pause' if port.is_active else 'play' }}"></i>
                                                    {{ 'Deactivate' if port.is_active else 'Activate' }}
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-hdd-network text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No SMS Ports Configured</h4>
                        <p class="text-muted">SMS ports will be automatically detected when the system connects to your SMS gateway.</p>
                        <button class="btn btn-primary" onclick="refreshPorts()">
                            <i class="bi bi-search"></i> Scan for Ports
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Port Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> Port Management Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Port Status Indicators:</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-success me-2">Active</span> Port is online and ready to send/receive SMS</li>
                            <li><span class="badge bg-danger me-2">Inactive</span> Port is offline or has issues</li>
                            <li><span class="badge bg-warning me-2">Unknown</span> Port status could not be determined</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Port Management:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-wifi text-info me-2"></i> Test port connectivity and SMS capability</li>
                            <li><i class="bi bi-gear text-primary me-2"></i> Configure port settings and parameters</li>
                            <li><i class="bi bi-arrow-clockwise text-success me-2"></i> Refresh port status and information</li>
                        </ul>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="bi bi-lightbulb me-2"></i>
                    <strong>Tip:</strong> Assign specific ports to users in the User Management section to control which ports each user can access for sending and receiving SMS messages.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Refresh all ports status
function refreshPorts() {
    // Show loading state
    const refreshBtn = document.querySelector('button[onclick="refreshPorts()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Refreshing...';
    refreshBtn.disabled = true;

    // Simulate refresh (in real implementation, this would call the backend)
    setTimeout(() => {
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;

        // Show success message
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show';
        alert.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>
            Port status refreshed successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.querySelector('.container-fluid').insertBefore(alert, document.querySelector('.row'));

        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);

        // Reload page to show updated data
        location.reload();
    }, 2000);
}

// Test specific port
function testPort(portNumber) {
    alert(`Testing port ${portNumber}...\n\nThis would send a test SMS and check connectivity.\n\n(Feature would be implemented in production)`);
}

// Configure specific port
function configurePort(portNumber) {
    alert(`Configuring port ${portNumber}...\n\nThis would open port configuration settings.\n\n(Feature would be implemented in production)`);
}

// Refresh specific port status
function refreshPortStatus(portNumber) {
    alert(`Refreshing status for port ${portNumber}...\n\nThis would update the port's status information.\n\n(Feature would be implemented in production)`);
}
</script>
{% endblock %}
