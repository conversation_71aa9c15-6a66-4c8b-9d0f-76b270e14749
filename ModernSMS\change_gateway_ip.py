#!/usr/bin/env python3
"""
Safe Gateway IP Change Tool
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def test_gateway_connection(ip, port, username, password):
    """Test if gateway is accessible"""
    try:
        print(f"🔍 Testing connection to {ip}:{port}...")
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((ip, port))
        
        # Test login
        initial = sock.recv(1024).decode()
        login_cmd = f"Action: Login\r\nUsername: {username}\r\nSecret: {password}\r\n\r\n"
        sock.send(login_cmd.encode())
        login_resp = sock.recv(1024).decode()
        
        sock.close()
        
        if "Response: Success" in login_resp:
            print(f"✅ Connection successful!")
            return True
        else:
            print(f"❌ Login failed: {login_resp.strip()}")
            return False
            
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return False

def test_sms_api_enabled(ip, port, username, password):
    """Test if SMS API is enabled on new IP"""
    try:
        print(f"🧪 Testing SMS API on {ip}...")
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((ip, port))
        
        # Login
        initial = sock.recv(1024).decode()
        login_cmd = f"Action: Login\r\nUsername: {username}\r\nSecret: {password}\r\n\r\n"
        sock.send(login_cmd.encode())
        login_resp = sock.recv(1024).decode()
        
        if "Response: Success" not in login_resp:
            print(f"❌ Login failed")
            sock.close()
            return False
        
        # Test SMS commands
        gsm_cmd = "Action: smscommand\r\ncommand: gsm show spans\r\n\r\n"
        sock.send(gsm_cmd.encode())
        gsm_resp = sock.recv(2048).decode()
        
        # Test events
        event_cmd = "Action: Events\r\n\r\n"
        sock.send(event_cmd.encode())
        event_resp = sock.recv(1024).decode()
        
        sock.close()
        
        sms_working = "GSM span" in gsm_resp and "Authentication accepted" not in gsm_resp
        events_working = "Events: On" in event_resp
        
        print(f"📱 SMS Commands: {'✅' if sms_working else '❌'}")
        print(f"📡 SMS Events: {'✅' if events_working else '❌'}")
        
        return sms_working and events_working
        
    except Exception as e:
        print(f"❌ API test failed: {str(e)}")
        return False

def change_gateway_ip(old_ip, new_ip):
    """Safely change gateway IP"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔄 SAFE GATEWAY IP CHANGE")
        print("="*60)
        
        # Get current gateway
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        if not gateway:
            print("❌ No primary gateway found!")
            return False
        
        print(f"📡 Current Gateway: {gateway.name}")
        print(f"   Old IP: {gateway.ip_address}")
        print(f"   New IP: {new_ip}")
        print(f"   Credentials: {gateway.username}/{gateway.password}")
        
        # Test old IP (current)
        print(f"\n1️⃣  Testing current IP ({old_ip})...")
        old_working = test_gateway_connection(old_ip, gateway.tg_sms_port, gateway.username, gateway.password)
        
        if old_working:
            print(f"✅ Current IP is working")
        else:
            print(f"⚠️  Current IP has issues")
        
        # Test new IP
        print(f"\n2️⃣  Testing new IP ({new_ip})...")
        new_accessible = test_gateway_connection(new_ip, gateway.tg_sms_port, gateway.username, gateway.password)
        
        if not new_accessible:
            print(f"❌ New IP is not accessible!")
            print(f"💡 Check:")
            print(f"   - Gateway is powered on")
            print(f"   - Network connectivity")
            print(f"   - IP address is correct")
            return False
        
        # Test SMS API on new IP
        print(f"\n3️⃣  Testing SMS API on new IP...")
        api_working = test_sms_api_enabled(new_ip, gateway.tg_sms_port, gateway.username, gateway.password)
        
        if not api_working:
            print(f"❌ SMS API not properly configured on new IP!")
            print(f"💡 Configure SMS API on new gateway:")
            print(f"   1. Go to http://{new_ip}")
            print(f"   2. SMS → API Settings")
            print(f"   3. Enable API")
            print(f"   4. Add allowed IP addresses")
            print(f"   5. Save and apply")
            
            response = input(f"\n🔧 Configure API and press Enter to continue (or 'q' to quit): ")
            if response.lower() == 'q':
                return False
            
            # Test again after configuration
            api_working = test_sms_api_enabled(new_ip, gateway.tg_sms_port, gateway.username, gateway.password)
            if not api_working:
                print(f"❌ API still not working after configuration")
                return False
        
        # Update gateway IP
        print(f"\n4️⃣  Updating gateway IP in database...")
        gateway.ip_address = new_ip
        from datetime import datetime
        gateway.updated_at = datetime.now()
        
        db.session.commit()
        print(f"✅ Gateway IP updated to {new_ip}")
        
        # Restart SMS receiver
        print(f"\n5️⃣  Restarting SMS receiver...")
        try:
            from app.sms.receiver import SMSReceiver
            
            # Stop existing receiver
            receiver = getattr(app, 'sms_receiver', None)
            if receiver:
                receiver.stop_receiver()
                time.sleep(3)
            
            # Start new receiver with new IP
            new_receiver = SMSReceiver(app)
            new_receiver.start_receiver()
            app.sms_receiver = new_receiver
            
            time.sleep(5)
            
            if new_receiver.running and new_receiver.connected:
                print(f"✅ SMS receiver connected to new IP!")
            else:
                print(f"⚠️  SMS receiver has connection issues")
            
        except Exception as e:
            print(f"❌ Error restarting SMS receiver: {str(e)}")
        
        print(f"\n🎉 Gateway IP change completed!")
        print(f"📱 Test SMS sending and receiving")
        
        return True

def main():
    """Main function"""
    if len(sys.argv) != 3:
        print("Usage: python change_gateway_ip.py <old_ip> <new_ip>")
        print("Example: python change_gateway_ip.py ************* *************")
        sys.exit(1)
    
    old_ip = sys.argv[1]
    new_ip = sys.argv[2]
    
    print(f"🔄 Changing gateway IP from {old_ip} to {new_ip}")
    
    success = change_gateway_ip(old_ip, new_ip)
    
    if success:
        print(f"\n✅ IP change successful!")
        print(f"🌐 Gateway now accessible at: http://{new_ip}")
        print(f"📱 SMS receiving should work on new IP")
    else:
        print(f"\n❌ IP change failed!")
        print(f"💡 Manual steps may be required")

if __name__ == '__main__':
    main()
