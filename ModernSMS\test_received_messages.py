#!/usr/bin/env python3
"""
Test received messages for dashboard
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import SMSMessage, SMSPort, User
from datetime import datetime

def test_received_messages():
    """Test received messages"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("📱 RECEIVED MESSAGES TEST")
        print("="*60)
        
        try:
            # Check existing received messages
            received_messages = SMSMessage.query.filter_by(direction='inbound').all()
            print(f"\n📥 Found {len(received_messages)} received messages:")
            for msg in received_messages:
                print(f"  - From: {msg.phone_number}")
                print(f"    Content: {msg.content[:50]}...")
                print(f"    Port: {msg.port_id}")
                print(f"    Created: {msg.created_at}")
                print()
            
            # Get available ports
            ports = SMSPort.query.filter_by(is_active=True).all()
            print(f"\n📡 Available ports: {len(ports)}")
            for port in ports:
                print(f"  - Port {port.id}: Gateway {port.gateway_id}, Port {port.port_number}")
            
            if not ports:
                print("❌ No active ports found!")
                return
            
            # Create test received messages
            print(f"\n🆕 Creating test received messages...")
            
            test_messages = [
                {
                    'phone_number': '+639123456789',
                    'content': 'Hello! This is a test received message.',
                    'port_id': ports[0].id
                },
                {
                    'phone_number': '+639987654321',
                    'content': 'Another test message from different number.',
                    'port_id': ports[0].id if len(ports) > 0 else ports[0].id
                },
                {
                    'phone_number': '+639555123456',
                    'content': 'Third test message for dashboard testing.',
                    'port_id': ports[-1].id
                }
            ]
            
            created_count = 0
            for msg_data in test_messages:
                # Check if message already exists
                existing = SMSMessage.query.filter_by(
                    phone_number=msg_data['phone_number'],
                    content=msg_data['content'],
                    direction='inbound'
                ).first()
                
                if not existing:
                    import uuid
                    message = SMSMessage(
                        message_id=str(uuid.uuid4()),
                        phone_number=msg_data['phone_number'],
                        content=msg_data['content'],
                        direction='inbound',
                        status='received',
                        port_id=msg_data['port_id'],
                        created_at=datetime.now()
                    )
                    db.session.add(message)
                    created_count += 1
                    print(f"  ✅ Created message from {msg_data['phone_number']}")
                else:
                    print(f"  ⚠️  Message from {msg_data['phone_number']} already exists")
            
            if created_count > 0:
                db.session.commit()
                print(f"\n✅ Created {created_count} new received messages")
            else:
                print(f"\n⚠️  No new messages created (all already exist)")
            
            # Test dashboard stats for different users
            print(f"\n📊 Testing dashboard stats...")
            
            # Test for admin user
            admin_user = User.query.filter_by(username='admin').first()
            if admin_user:
                print(f"\n👤 Admin user stats:")
                from app.main.routes import get_dashboard_stats
                from flask_login import login_user
                
                # Simulate login for admin
                with app.test_request_context():
                    login_user(admin_user)
                    stats = get_dashboard_stats()
                    print(f"  Total messages: {stats['total_messages']}")
                    print(f"  Sent messages: {stats['sent_messages']}")
                    print(f"  Received messages: {stats['received_messages']}")
                    print(f"  Today received: {stats['today_received']}")
            
            # Test for regular user
            test_user = User.query.filter_by(username='testuser').first()
            if test_user:
                print(f"\n👤 Test user stats:")
                with app.test_request_context():
                    login_user(test_user)
                    stats = get_dashboard_stats()
                    print(f"  Total messages: {stats['total_messages']}")
                    print(f"  Sent messages: {stats['sent_messages']}")
                    print(f"  Received messages: {stats['received_messages']}")
                    print(f"  Today received: {stats['today_received']}")
            
            # Test for apiuser
            api_user = User.query.filter_by(username='apiuser').first()
            if api_user:
                print(f"\n👤 API user stats:")
                with app.test_request_context():
                    login_user(api_user)
                    stats = get_dashboard_stats()
                    print(f"  Total messages: {stats['total_messages']}")
                    print(f"  Sent messages: {stats['sent_messages']}")
                    print(f"  Received messages: {stats['received_messages']}")
                    print(f"  Today received: {stats['today_received']}")
                    print(f"  Assigned ports: {api_user.assigned_ports}")
            
            print(f"\n✅ Test completed successfully!")
            
        except Exception as e:
            print(f"❌ Error during test: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_received_messages()
