import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Inbox, 
  Send, 
  Edit3, 
  Settings, 
  Menu,
  MessageSquare,
  BarChart3,
  Clock,
  AlertCircle
} from 'lucide-react';

const Sidebar = ({ isOpen, onToggle, stats }) => {
  const location = useLocation();

  const menuItems = [
    {
      path: '/',
      icon: Home,
      label: 'Dashboard',
      badge: null
    },
    {
      path: '/inbox',
      icon: Inbox,
      label: 'Inbox',
      badge: stats.total_received
    },
    {
      path: '/outbox',
      icon: Send,
      label: 'Outbox',
      badge: stats.total_sent
    },
    {
      path: '/compose',
      icon: Edit3,
      label: 'Compose',
      badge: null
    },
    {
      path: '/settings',
      icon: Settings,
      label: 'Settings',
      badge: null
    }
  ];

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <div className={`fixed left-0 top-0 h-full bg-white shadow-lg transition-all duration-300 z-50 ${
      isOpen ? 'w-64' : 'w-16'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className={`flex items-center space-x-3 ${isOpen ? '' : 'justify-center'}`}>
          <MessageSquare className="h-8 w-8 text-blue-600" />
          {isOpen && (
            <div>
              <h1 className="text-xl font-bold text-gray-800">SMS Manager</h1>
              <p className="text-sm text-gray-500">MyGoautodial</p>
            </div>
          )}
        </div>
        <button
          onClick={onToggle}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <Menu className="h-5 w-5 text-gray-600" />
        </button>
      </div>

      {/* Stats Summary */}
      {isOpen && (
        <div className="p-4 border-b bg-gray-50">
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">Total</span>
              </div>
              <p className="text-lg font-bold text-gray-900">{stats.total_messages}</p>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-gray-700">Pending</span>
              </div>
              <p className="text-lg font-bold text-gray-900">{stats.pending_messages}</p>
            </div>
          </div>
          {stats.failed_messages > 0 && (
            <div className="mt-3 bg-red-50 p-3 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-red-700">Failed</span>
                <span className="text-sm font-bold text-red-900">{stats.failed_messages}</span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Navigation Menu */}
      <nav className="mt-4">
        <ul className="space-y-2 px-3">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.path);
            
            return (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 ${
                    active
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'text-gray-700 hover:bg-gray-100'
                  } ${!isOpen ? 'justify-center' : ''}`}
                >
                  <Icon className={`h-5 w-5 ${active ? 'text-white' : 'text-gray-500'}`} />
                  {isOpen && (
                    <>
                      <span className="font-medium">{item.label}</span>
                      {item.badge !== null && item.badge > 0 && (
                        <span className={`ml-auto px-2 py-1 text-xs rounded-full ${
                          active 
                            ? 'bg-white text-blue-600' 
                            : 'bg-blue-100 text-blue-600'
                        }`}>
                          {item.badge}
                        </span>
                      )}
                    </>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      {isOpen && (
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t bg-gray-50">
          <div className="text-center">
            <p className="text-xs text-gray-500">
              SMS Management System
            </p>
            <p className="text-xs text-gray-400">
              v1.0.0
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
