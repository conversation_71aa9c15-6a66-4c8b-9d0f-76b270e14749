#!/usr/bin/env python3
"""
Test delete functionality in outbox and inbox
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSMessage, User
import uuid
from datetime import datetime
import requests

def create_test_messages():
    """Create test messages for delete testing"""
    with app.app_context():
        print("📱 Creating Test Messages for Delete Testing")
        print("=" * 50)
        
        # Create test outbound messages
        outbound_messages = []
        for i in range(3):
            message = SMSMessage(
                message_id=f'TEST_OUT_{uuid.uuid4().hex[:8]}',
                direction='outbound',
                phone_number=f'+639{i:02d}1234567',
                content=f'Test outbound message {i+1} for delete testing',
                status='sent',
                gsm_port='1',
                created_at=datetime.now()
            )
            db.session.add(message)
            outbound_messages.append(message)
        
        # Create test inbound messages
        inbound_messages = []
        for i in range(3):
            message = SMSMessage(
                message_id=f'TEST_IN_{uuid.uuid4().hex[:8]}',
                direction='inbound',
                phone_number=f'+639{i:02d}7654321',
                content=f'Test inbound message {i+1} for delete testing',
                status='received',
                gsm_port='2',
                created_at=datetime.now()
            )
            db.session.add(message)
            inbound_messages.append(message)
        
        db.session.commit()
        
        print(f"✅ Created {len(outbound_messages)} outbound test messages")
        print(f"✅ Created {len(inbound_messages)} inbound test messages")
        
        return outbound_messages, inbound_messages

def test_single_delete():
    """Test single message delete functionality"""
    print(f"\n🗑️ Testing Single Message Delete")
    print("=" * 35)
    
    with app.app_context():
        # Get a test message
        test_message = SMSMessage.query.filter(
            SMSMessage.message_id.like('TEST_%')
        ).first()
        
        if not test_message:
            print("❌ No test messages found")
            return False
        
        message_id = test_message.id
        direction = test_message.direction
        phone_number = test_message.phone_number
        
        print(f"📝 Testing delete of {direction} message ID {message_id}")
        print(f"   Phone: {phone_number}")
        print(f"   Content: {test_message.content[:30]}...")
        
        try:
            # Test the delete via web request
            session = requests.Session()
            
            # Login as admin
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
            
            if login_response.status_code == 200:
                print("✅ Admin login successful")
                
                # Test delete
                delete_response = session.post(f'http://127.0.0.1:5000/sms/{message_id}/delete')
                
                if delete_response.status_code == 200:
                    print("✅ Delete request successful")
                    
                    # Verify message was deleted
                    deleted_message = SMSMessage.query.get(message_id)
                    if deleted_message is None:
                        print("✅ Message successfully deleted from database")
                        return True
                    else:
                        print("❌ Message still exists in database")
                        return False
                else:
                    print(f"❌ Delete request failed: {delete_response.status_code}")
                    return False
            else:
                print(f"❌ Admin login failed: {login_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing delete: {str(e)}")
            return False

def test_bulk_delete():
    """Test bulk delete functionality"""
    print(f"\n🗑️ Testing Bulk Delete")
    print("=" * 25)
    
    with app.app_context():
        # Get multiple test messages
        test_messages = SMSMessage.query.filter(
            SMSMessage.message_id.like('TEST_%')
        ).limit(2).all()
        
        if len(test_messages) < 2:
            print("❌ Not enough test messages for bulk delete")
            return False
        
        message_ids = [str(msg.id) for msg in test_messages]
        
        print(f"📝 Testing bulk delete of {len(message_ids)} messages")
        for msg in test_messages:
            print(f"   - {msg.direction} message ID {msg.id}: {msg.content[:30]}...")
        
        try:
            # Test the bulk delete via web request
            session = requests.Session()
            
            # Login as admin
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
            
            if login_response.status_code == 200:
                print("✅ Admin login successful")
                
                # Test bulk delete
                bulk_delete_data = {
                    'message_ids': message_ids
                }
                delete_response = session.post('http://127.0.0.1:5000/sms/bulk_delete', data=bulk_delete_data)
                
                if delete_response.status_code == 200:
                    print("✅ Bulk delete request successful")
                    
                    # Verify messages were deleted
                    remaining_messages = SMSMessage.query.filter(
                        SMSMessage.id.in_([int(mid) for mid in message_ids])
                    ).all()
                    
                    if len(remaining_messages) == 0:
                        print("✅ All messages successfully deleted from database")
                        return True
                    else:
                        print(f"❌ {len(remaining_messages)} messages still exist in database")
                        return False
                else:
                    print(f"❌ Bulk delete request failed: {delete_response.status_code}")
                    return False
            else:
                print(f"❌ Admin login failed: {login_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing bulk delete: {str(e)}")
            return False

def test_permission_restrictions():
    """Test delete permission restrictions"""
    print(f"\n🔒 Testing Permission Restrictions")
    print("=" * 35)
    
    with app.app_context():
        # Get a test message
        test_message = SMSMessage.query.filter(
            SMSMessage.message_id.like('TEST_%')
        ).first()
        
        if not test_message:
            print("❌ No test messages found")
            return False
        
        message_id = test_message.id
        
        print(f"📝 Testing permission restrictions for message ID {message_id}")
        
        try:
            # Test with sales_team user (limited permissions)
            session = requests.Session()
            
            # Login as sales_team
            login_data = {
                'username': 'sales_team',
                'password': 'sales123'
            }
            login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
            
            if login_response.status_code == 200:
                print("✅ Sales team login successful")
                
                # Test delete (should work if user has permissions)
                delete_response = session.post(f'http://127.0.0.1:5000/sms/{message_id}/delete')
                
                if delete_response.status_code == 200:
                    print("✅ Delete allowed for sales_team user")
                    return True
                else:
                    print(f"⚠️ Delete restricted for sales_team user: {delete_response.status_code}")
                    return True  # This is expected behavior
            else:
                print(f"❌ Sales team login failed: {login_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing permissions: {str(e)}")
            return False

def cleanup_test_messages():
    """Clean up test messages"""
    with app.app_context():
        print(f"\n🧹 Cleaning Up Test Messages")
        print("=" * 30)
        
        # Delete all test messages
        test_messages = SMSMessage.query.filter(
            SMSMessage.message_id.like('TEST_%')
        ).all()
        
        for message in test_messages:
            db.session.delete(message)
        
        db.session.commit()
        
        print(f"✅ Cleaned up {len(test_messages)} test messages")

def show_current_messages():
    """Show current messages in database"""
    with app.app_context():
        print(f"\n📊 Current Messages in Database")
        print("=" * 35)
        
        total_messages = SMSMessage.query.count()
        inbound_count = SMSMessage.query.filter_by(direction='inbound').count()
        outbound_count = SMSMessage.query.filter_by(direction='outbound').count()
        test_count = SMSMessage.query.filter(SMSMessage.message_id.like('TEST_%')).count()
        
        print(f"   Total Messages: {total_messages}")
        print(f"   Inbound: {inbound_count}")
        print(f"   Outbound: {outbound_count}")
        print(f"   Test Messages: {test_count}")
        
        # Show recent messages
        recent_messages = SMSMessage.query.order_by(SMSMessage.created_at.desc()).limit(5).all()
        
        if recent_messages:
            print(f"\n   Recent Messages:")
            for msg in recent_messages:
                print(f"     - {msg.direction} from {msg.phone_number}: {msg.content[:30]}...")

if __name__ == "__main__":
    print("🧪 DELETE FUNCTIONALITY TEST SUITE")
    print("=" * 40)
    
    # Show current state
    show_current_messages()
    
    # Create test messages
    outbound_msgs, inbound_msgs = create_test_messages()
    
    # Test single delete
    single_delete_success = test_single_delete()
    
    # Test bulk delete
    bulk_delete_success = test_bulk_delete()
    
    # Test permissions
    permission_test_success = test_permission_restrictions()
    
    # Show final state
    show_current_messages()
    
    # Clean up
    cleanup_test_messages()
    
    # Summary
    print(f"\n🎯 TEST RESULTS SUMMARY:")
    print("=" * 25)
    print(f"   Single Delete: {'✅ PASS' if single_delete_success else '❌ FAIL'}")
    print(f"   Bulk Delete: {'✅ PASS' if bulk_delete_success else '❌ FAIL'}")
    print(f"   Permissions: {'✅ PASS' if permission_test_success else '❌ FAIL'}")
    
    if all([single_delete_success, bulk_delete_success, permission_test_success]):
        print(f"\n🎉 ALL TESTS PASSED! Delete functionality is working correctly.")
    else:
        print(f"\n⚠️ Some tests failed. Check the output above for details.")
