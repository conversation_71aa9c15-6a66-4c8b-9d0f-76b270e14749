#!/usr/bin/env python3
"""
Force Fix Display Issues
"""

import sys
import os
import requests
import time
sys.path.append('backend')

def force_fix_display():
    """Force fix the display issues by ensuring correct data"""
    print("🔧 Force Fix Display Issues")
    print("=" * 35)
    
    try:
        from app import app, db, GSMGateway, SMSPort, SMS_API_CONFIG, TG_SMS_CONFIG
        
        with app.app_context():
            print("📋 Ensuring Correct Database State:")
            
            # Ensure we have exactly one gateway with 8 ports
            gateways = GSMGateway.query.all()
            
            if not gateways:
                print("   Creating main gateway...")
                main_gateway = GSMGateway(
                    name='Main Gateway',
                    ip_address=SMS_API_CONFIG['ip'],
                    api_port='80',
                    tg_sms_port=str(TG_SMS_CONFIG['port']),
                    username=SMS_API_CONFIG['account'],
                    password=SMS_API_CONFIG['password'],
                    max_ports=8,
                    status='active',
                    location='Main Office',
                    description='Primary GSM gateway for SMS operations',
                    is_primary=True
                )
                db.session.add(main_gateway)
                db.session.flush()
                gateway = main_gateway
            else:
                gateway = gateways[0]
                print(f"   Using existing gateway: {gateway.name}")
            
            # Force update gateway to have 8 ports
            gateway.max_ports = 8
            gateway.status = 'active'
            gateway.is_primary = True
            
            # Ensure we have exactly 8 ports
            print("   Ensuring 8 ports exist...")
            for i in range(1, 9):
                port = SMSPort.query.filter_by(port_number=str(i)).first()
                if not port:
                    port = SMSPort(
                        port_number=str(i),
                        status='detected',
                        is_active=True,
                        gateway_id=gateway.id,
                        network_name='Unknown'
                    )
                    db.session.add(port)
                    print(f"      Created port {i}")
                else:
                    port.is_active = True
                    port.gateway_id = gateway.id
                    port.status = 'detected'
                    print(f"      Updated port {i}")
            
            db.session.commit()
            
            # Verify the final state
            print(f"\n✅ Final Database State:")
            final_gateway = GSMGateway.query.first()
            final_ports = SMSPort.query.filter_by(is_active=True).all()
            
            print(f"   Gateway: {final_gateway.name}")
            print(f"   Max Ports: {final_gateway.max_ports}")
            print(f"   Active Ports: {len(final_ports)}")
            print(f"   Port Numbers: {sorted([p.port_number for p in final_ports])}")
            
            # Test the to_dict method
            gateway_dict = final_gateway.to_dict()
            print(f"   to_dict() max_ports: {gateway_dict['max_ports']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error fixing database: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_web_interface():
    """Test the web interface after fixing database"""
    print("\n🌐 Testing Web Interface:")
    
    try:
        # Wait for Flask app
        time.sleep(2)
        
        # Create session and login
        session = requests.Session()
        
        # Login as admin
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data, allow_redirects=True)
        
        if login_response.status_code == 200:
            print("✅ Successfully logged in")
            
            # Test GSM Gateway page
            print("\n🔧 Testing GSM Gateway Page:")
            gsm_response = session.get('http://127.0.0.1:5000/gsm_gateways')
            
            if gsm_response.status_code == 200:
                content = gsm_response.text
                
                # Check for 8 ports
                if "Ports (8):" in content:
                    print("✅ GSM Gateway shows 'Ports (8):' - FIXED!")
                elif "Ports (4):" in content:
                    print("❌ GSM Gateway still shows 'Ports (4):'")
                else:
                    print("❓ No port count pattern found")
                
                # Count individual ports
                port_count = 0
                for i in range(1, 9):
                    if f"Port {i}" in content:
                        port_count += 1
                
                print(f"   Individual ports found: {port_count}/8")
                
                # Check Add Gateway button
                if 'Add Gateway' in content:
                    print("✅ Add Gateway button is present")
                
            # Test User Creation page
            print("\n👤 Testing User Creation Page:")
            user_response = session.get('http://127.0.0.1:5000/users/create')
            
            if user_response.status_code == 200:
                content = user_response.text
                
                # Check for gateway info
                if "Main Gateway" in content:
                    print("✅ User creation shows 'Main Gateway'")
                else:
                    print("❌ User creation doesn't show Main Gateway")
                
                # Check for warning message
                if "No GSM gateway configured yet" in content:
                    print("❌ User creation shows 'No GSM gateway configured yet'")
                else:
                    print("✅ No 'gateway not configured' warning")
                
                # Count port checkboxes
                port_checkboxes = 0
                for i in range(1, 9):
                    if f'value="{i}"' in content and 'assigned_ports' in content:
                        port_checkboxes += 1
                
                print(f"   Port checkboxes found: {port_checkboxes}/8")
            
            return True
            
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web interface test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Force Fix Process...")
    
    # Step 1: Fix database
    if force_fix_display():
        print("\n✅ Database fixed successfully!")
        
        # Step 2: Test web interface
        test_web_interface()
        
        print("\n🎉 Force fix process completed!")
        print("\n📝 Next Steps:")
        print("   1. Hard refresh your browser (Ctrl+Shift+R)")
        print("   2. Navigate to GSM Gateways page")
        print("   3. Try creating a new user")
        print("   4. Check if all 8 ports are displayed")
        
    else:
        print("\n❌ Failed to fix database")
