{% extends "base.html" %}

{% block title %}Profile - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">User Profile</h1>
        <p class="text-muted">Manage your account settings and preferences</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person-circle"></i> Profile Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.update_profile') }}">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   value="{{ current_user.first_name or '' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   value="{{ current_user.last_name or '' }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="{{ current_user.email }}" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update Profile
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-lock"></i> Change Password</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.change_password') }}">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-key"></i> Change Password
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Account Information</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Username:</strong><br>
                    <span class="text-muted">{{ current_user.username }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Role:</strong><br>
                    <span class="badge bg-primary">{{ current_user.role.name if current_user.role else 'User' }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Account Status:</strong><br>
                    <span class="badge bg-success">Active</span>
                </div>
                
                <div class="mb-3">
                    <strong>Member Since:</strong><br>
                    <span class="text-muted">{{ current_user.created_at.strftime('%B %d, %Y') if current_user.created_at else 'Unknown' }}</span>
                </div>
                
                {% if current_user.last_login %}
                <div class="mb-3">
                    <strong>Last Login:</strong><br>
                    <span class="text-muted">{{ current_user.last_login.strftime('%B %d, %Y at %I:%M %p') }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-gear"></i> Permissions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">Send SMS:</small><br>
                        {% if current_user.can_send_sms %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-danger">No</span>
                        {% endif %}
                    </div>
                    <div class="col-6">
                        <small class="text-muted">View Inbox:</small><br>
                        {% if current_user.can_view_inbox %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-danger">No</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted">Manage Gateways:</small><br>
                        {% if current_user.can_manage_gateways %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-danger">No</span>
                        {% endif %}
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Manage Users:</small><br>
                        {% if current_user.can_manage_users %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-danger">No</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
