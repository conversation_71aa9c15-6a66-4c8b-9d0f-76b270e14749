#!/usr/bin/env python3
"""
Verify 8 ports are working in web interface
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSPort
import requests

def verify_8ports_web():
    with app.app_context():
        print("🔍 Verifying 8-Port Configuration in Web Interface")
        print("=" * 55)
        
        # Check database
        ports = SMSPort.query.order_by(SMSPort.port_number.asc()).all()
        print(f"📊 Database Status:")
        print(f"   Total ports in database: {len(ports)}")
        
        for port in ports:
            status_icon = "✅" if port.is_active else "❌"
            print(f"   {status_icon} Port {port.port_number}: {port.status} (Active: {port.is_active})")
        
        # Test web interface
        print(f"\n🌐 Testing Web Interface:")
        
        try:
            # Test login
            session = requests.Session()
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
            print(f"   Login Status: {login_response.status_code}")
            
            if login_response.status_code == 200:
                # Test compose page
                compose_response = session.get('http://127.0.0.1:5000/compose')
                print(f"   Compose Page Status: {compose_response.status_code}")
                
                if compose_response.status_code == 200:
                    # Count port options in HTML
                    html_content = compose_response.text
                    port_count = 0
                    
                    for i in range(1, 9):
                        if f'value="{i}"' in html_content:
                            port_count += 1
                            print(f"   ✅ Port {i} found in compose dropdown")
                        else:
                            print(f"   ❌ Port {i} NOT found in compose dropdown")
                    
                    print(f"\n📊 Summary:")
                    print(f"   Ports in database: {len(ports)}")
                    print(f"   Ports in web interface: {port_count}")
                    
                    if port_count == 8:
                        print(f"   🎉 SUCCESS: All 8 ports are available in web interface!")
                    else:
                        print(f"   ⚠️ ISSUE: Only {port_count} ports available in web interface")
                        
                        # Show what's actually in the HTML
                        print(f"\n🔍 Port options found in HTML:")
                        import re
                        port_options = re.findall(r'<option value="(\d+)"', html_content)
                        for port in port_options:
                            print(f"     - Port {port}")
                
        except Exception as e:
            print(f"   ❌ Error testing web interface: {str(e)}")

if __name__ == "__main__":
    verify_8ports_web()
