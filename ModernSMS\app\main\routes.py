"""
Main application routes
"""
from flask import render_template, current_app, request, jsonify
from flask_login import login_required, current_user
from app.main import bp
from app.models import SMSMessage, GSMGateway, SMSPort, User
from app import db
from sqlalchemy import func, desc
from datetime import datetime, timedelta

@bp.route('/')
@bp.route('/dashboard')
@login_required
def dashboard():
    """Main dashboard"""
    try:
        # Get statistics
        stats = get_dashboard_stats()
        
        # Get recent messages
        recent_messages = SMSMessage.query.order_by(desc(SMSMessage.created_at)).limit(10).all()
        
        # Get gateway status
        gateways = GSMGateway.query.all()
        
        # Get active ports
        active_ports = SMSPort.query.filter_by(is_active=True).count()
        
        return render_template('dashboard.html',
                             stats=stats,
                             recent_messages=recent_messages,
                             gateways=gateways,
                             active_ports=active_ports)
    except Exception as e:
        current_app.logger.error(f"Dashboard error: {str(e)}")
        return render_template('dashboard.html',
                             stats={},
                             recent_messages=[],
                             gateways=[],
                             active_ports=0)

@bp.route('/api/dashboard/stats')
@login_required
def dashboard_stats_api():
    """API endpoint for dashboard statistics"""
    try:
        stats = get_dashboard_stats()
        return jsonify({'success': True, 'stats': stats})
    except Exception as e:
        current_app.logger.error(f"Dashboard stats API error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

def get_dashboard_stats():
    """Get dashboard statistics"""
    try:
        # Base queries
        total_messages = SMSMessage.query.count()
        sent_messages = SMSMessage.query.filter_by(direction='outbound').count()
        received_messages = SMSMessage.query.filter_by(direction='inbound').count()
        
        # Today's statistics
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        
        today_sent = SMSMessage.query.filter(
            SMSMessage.direction == 'outbound',
            SMSMessage.created_at >= today_start
        ).count()
        
        today_received = SMSMessage.query.filter(
            SMSMessage.direction == 'inbound',
            SMSMessage.created_at >= today_start
        ).count()
        
        # Failed messages
        failed_messages = SMSMessage.query.filter_by(status='failed').count()
        
        # Pending messages
        pending_messages = SMSMessage.query.filter_by(status='pending').count()
        
        # Gateway statistics
        total_gateways = GSMGateway.query.count()
        active_gateways = GSMGateway.query.filter_by(status='active').count()
        
        # Port statistics
        total_ports = SMSPort.query.count()
        active_ports = SMSPort.query.filter_by(status='active').count()
        
        # User statistics
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        
        # Weekly message trend
        week_ago = datetime.now() - timedelta(days=7)
        weekly_messages = db.session.query(
            func.date(SMSMessage.created_at).label('date'),
            func.count(SMSMessage.id).label('count')
        ).filter(
            SMSMessage.created_at >= week_ago
        ).group_by(
            func.date(SMSMessage.created_at)
        ).all()
        
        return {
            'total_messages': total_messages,
            'sent_messages': sent_messages,
            'received_messages': received_messages,
            'today_sent': today_sent,
            'today_received': today_received,
            'failed_messages': failed_messages,
            'pending_messages': pending_messages,
            'total_gateways': total_gateways,
            'active_gateways': active_gateways,
            'total_ports': total_ports,
            'active_ports': active_ports,
            'total_users': total_users,
            'active_users': active_users,
            'weekly_trend': [{'date': str(row.date), 'count': row.count} for row in weekly_messages]
        }
    except Exception as e:
        current_app.logger.error(f"Error getting dashboard stats: {str(e)}")
        return {
            'total_messages': 0,
            'sent_messages': 0,
            'received_messages': 0,
            'today_sent': 0,
            'today_received': 0,
            'failed_messages': 0,
            'pending_messages': 0,
            'total_gateways': 0,
            'active_gateways': 0,
            'total_ports': 0,
            'active_ports': 0,
            'total_users': 0,
            'active_users': 0,
            'weekly_trend': []
        }

@bp.route('/health')
def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        db.session.execute('SELECT 1')
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': current_app.config.get('APP_VERSION', '1.0.0')
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
