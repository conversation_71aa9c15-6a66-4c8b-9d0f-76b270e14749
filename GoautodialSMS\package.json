{"name": "goautodial-sms", "version": "1.0.0", "description": "SMS Management System for MyGoautodial - Python Backend with HTML/CSS Frontend", "main": "backend/app.py", "scripts": {"start": "cd backend && python app.py", "dev": "cd backend && python app.py", "install": "pip install -r requirements.txt", "setup": "pip install -r requirements.txt"}, "keywords": ["sms", "goautodial", "messaging", "communication", "flask", "python"], "author": "MyGoautodial Team", "license": "MIT", "dependencies": {}, "devDependencies": {}}