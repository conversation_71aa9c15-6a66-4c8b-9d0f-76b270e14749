{% extends "base.html" %}

{% block title %}Dashboard - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-speedometer2"></i> SMS Dashboard
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Messages</h5>
                        <h2 class="mb-0">{{ stats.total_messages or 0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-chat-dots fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Received</h5>
                        <h2 class="mb-0">{{ stats.total_received or 0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-inbox fs-1"></i>
                    </div>
                </div>
                <small>
                    <a href="{{ url_for('inbox') }}" class="text-white text-decoration-none">
                        View Inbox <i class="bi bi-arrow-right"></i>
                    </a>
                </small>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Sent</h5>
                        <h2 class="mb-0">{{ stats.total_sent or 0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-send fs-1"></i>
                    </div>
                </div>
                <small>
                    <a href="{{ url_for('outbox') }}" class="text-white text-decoration-none">
                        View Outbox <i class="bi bi-arrow-right"></i>
                    </a>
                </small>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Pending</h5>
                        <h2 class="mb-0">{{ stats.pending_messages or 0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Failed Messages Alert -->
{% if stats.failed_messages and stats.failed_messages > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>{{ stats.failed_messages }} message(s) failed to send.</strong>
            <a href="{{ url_for('outbox') }}" class="alert-link">Check your outbox for details.</a>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('compose') }}" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-pencil-square"></i> Compose SMS
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('inbox') }}" class="btn btn-outline-success btn-lg w-100">
                            <i class="bi bi-inbox"></i> View Inbox
                        </a>
                    </div>
                    {% if current_user.has_permission('can_export') %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('export_page') }}" class="btn btn-outline-info btn-lg w-100">
                            <i class="bi bi-download"></i> Export Data
                        </a>
                    </div>
                    {% endif %}
                    <div class="col-md-3 mb-3">
                        <button onclick="location.reload()" class="btn btn-outline-secondary btn-lg w-100">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Messages -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i> Recent Messages
                </h5>
                <small class="text-muted">Last 10 messages</small>
            </div>
            <div class="card-body">
                {% if recent_messages %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Phone Number</th>
                                    <th>Message</th>
                                    <th>Status</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for message in recent_messages %}
                                <tr>
                                    <td>
                                        {% if message.direction == 'inbound' %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-inbox"></i> Received
                                            </span>
                                        {% else %}
                                            <span class="badge bg-primary">
                                                <i class="bi bi-send"></i> Sent
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ message.phone_number }}</strong>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 300px;">
                                            {{ message.content }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if message.status == 'sent' %}
                                            <span class="badge bg-success">Sent</span>
                                        {% elif message.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif message.status == 'failed' %}
                                            <span class="badge bg-danger">Failed</span>
                                        {% elif message.status == 'received' %}
                                            <span class="badge bg-info">Received</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ message.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ message.created_at.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-3">No messages yet</h5>
                        <p class="text-muted">Start by sending your first SMS message.</p>
                        <a href="{{ url_for('compose') }}" class="btn btn-primary">
                            <i class="bi bi-pencil-square"></i> Compose SMS
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh dashboard every 30 seconds
setInterval(function() {
    // Only refresh if user is on dashboard and page is visible
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 30000);
</script>
{% endblock %}
