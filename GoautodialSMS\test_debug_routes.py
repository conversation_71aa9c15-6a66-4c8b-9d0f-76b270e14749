#!/usr/bin/env python3
"""
Test debug routes
"""

import requests

BASE_URL = "http://127.0.0.1:5000"

def test_debug_routes():
    session = requests.Session()
    
    # Login as admin first
    login_data = {'username': 'admin', 'password': 'admin123'}
    login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=True)
    print(f"Admin login status: {login_response.status_code}")
    
    # Test different debug routes
    debug_routes = [
        '/debug/user_permissions',
        '/debug/check_user_permissions', 
        '/debug/database_check',
        '/debug/test_inbox_query'
    ]
    
    for route in debug_routes:
        print(f"\nTesting {route}:")
        response = session.get(f"{BASE_URL}{route}")
        print(f"  Status: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                if 'user_info' in data:
                    print(f"  User: {data['user_info'].get('username', 'Unknown')}")
                    print(f"  Role: {data['user_info'].get('role', 'Unknown')}")
                    if 'permissions' in data['user_info']:
                        perms = data['user_info']['permissions']
                        print(f"  Permissions: {list(perms.keys())}")
                elif 'success' in data:
                    print(f"  Success: {data['success']}")
                else:
                    print(f"  Response keys: {list(data.keys())}")
            except:
                print(f"  Response (first 100 chars): {response.text[:100]}")
        else:
            print(f"  Error: {response.text[:100]}")

if __name__ == "__main__":
    test_debug_routes()
