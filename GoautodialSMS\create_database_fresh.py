#!/usr/bin/env python3
"""
Create fresh database with correct schema
"""

import sys
import os
sys.path.append('backend')

from app import app, db

def create_fresh_database():
    print("🗄️ Creating Fresh Database with Dynamic Port Management")
    print("=" * 60)
    
    with app.app_context():
        try:
            # Drop all tables and recreate
            print("Dropping existing tables...")
            db.drop_all()
            
            print("Creating new tables with updated schema...")
            db.create_all()
            
            print("✅ Database tables created successfully!")
            
            # Verify the schema
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            
            # Check sms_ports table
            if 'sms_ports' in inspector.get_table_names():
                columns = inspector.get_columns('sms_ports')
                print(f"\nSMS Ports table columns:")
                for col in columns:
                    print(f"  - {col['name']}: {col['type']}")
            
            # Check users table
            if 'users' in inspector.get_table_names():
                columns = inspector.get_columns('users')
                print(f"\nUsers table columns:")
                for col in columns:
                    print(f"  - {col['name']}: {col['type']}")
            
            print(f"\n🎉 Fresh database created successfully!")
            
        except Exception as e:
            print(f"❌ Error creating database: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    create_fresh_database()
