#!/usr/bin/env python3
"""
Test password verification directly
"""

import sys
import os
sys.path.append('backend')

from app import app, db, User

def test_password_verification():
    with app.app_context():
        # Test admin password
        admin = User.query.filter_by(username='admin').first()
        if admin:
            print(f"👤 Admin user found")
            print(f"   Active: {admin.is_active}")
            print(f"   Password check 'admin123': {admin.check_password('admin123')}")
            print(f"   Password hash: {admin.password_hash[:50]}...")
        
        # Test test1 password
        test1 = User.query.filter_by(username='test1').first()
        if test1:
            print(f"\n👤 Test1 user found")
            print(f"   Active: {test1.is_active}")
            print(f"   Password check 'test123': {test1.check_password('test123')}")
            print(f"   Password hash: {test1.password_hash[:50]}...")
            
            # Try setting password again
            print(f"\n🔧 Resetting test1 password...")
            test1.set_password('test123')
            db.session.commit()
            print(f"   Password check after reset: {test1.check_password('test123')}")
        
        # Test diet password
        diet = User.query.filter_by(username='diet').first()
        if diet:
            print(f"\n👤 Diet user found")
            print(f"   Active: {diet.is_active}")
            print(f"   Password check 'test123': {diet.check_password('test123')}")
            print(f"   Password hash: {diet.password_hash[:50]}...")
            
            # Try setting password again
            print(f"\n🔧 Resetting diet password...")
            diet.set_password('test123')
            db.session.commit()
            print(f"   Password check after reset: {diet.check_password('test123')}")

if __name__ == "__main__":
    test_password_verification()
