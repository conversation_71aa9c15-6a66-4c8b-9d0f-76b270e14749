#!/usr/bin/env python3
"""
Check SMS receiver status and fix if needed
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def check_receiver_status():
    """Check current SMS receiver status"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔍 SMS RECEIVER STATUS CHECK")
        print("="*60)
        
        # Check if receiver exists
        receiver = getattr(app, 'sms_receiver', None)
        
        print(f"📱 SMS Receiver Instance: {'✅ Exists' if receiver else '❌ Not found'}")
        
        if receiver:
            print(f"   Running: {'✅ Yes' if receiver.running else '❌ No'}")
            print(f"   Connected: {'✅ Yes' if receiver.connected else '❌ No'}")
            print(f"   Thread alive: {'✅ Yes' if (receiver.thread and receiver.thread.is_alive()) else '❌ No'}")
            
            if not receiver.running or not receiver.connected:
                print("⚠️  SMS receiver is not properly running!")
                return False
            else:
                print("✅ SMS receiver appears to be running")
                return True
        else:
            print("❌ No SMS receiver found!")
            return False

def test_gateway_connection():
    """Test direct connection to gateway"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔍 GATEWAY CONNECTION TEST")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        if not gateway:
            print("❌ No primary gateway found!")
            return False
        
        print(f"📡 Gateway: {gateway.name}")
        print(f"   IP: {gateway.ip_address}:{gateway.tg_sms_port}")
        print(f"   Username: {gateway.username}")
        
        try:
            # Test basic connection
            print(f"\n🌐 Testing basic connection...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((gateway.ip_address, gateway.tg_sms_port))
            
            if result != 0:
                print(f"❌ Cannot connect to {gateway.ip_address}:{gateway.tg_sms_port}")
                print(f"   Error code: {result}")
                sock.close()
                return False
            
            print("✅ Basic connection successful")
            
            # Test TG SMS protocol
            print(f"\n🔐 Testing TG SMS authentication...")
            
            # Read initial response
            initial_response = sock.recv(1024).decode()
            print(f"📥 Initial response: {initial_response.strip()}")
            
            # Send login
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            # Read login response
            login_response = sock.recv(1024).decode()
            print(f"📥 Login response: {login_response.strip()}")
            
            if "Response: Success" in login_response:
                print("✅ TG SMS authentication successful")
                
                # Test event subscription
                print(f"\n📡 Testing event subscription...")
                event_cmd = "Action: Events\r\nEventMask: sms\r\n\r\n"
                sock.send(event_cmd.encode())
                
                event_response = sock.recv(1024).decode()
                print(f"📥 Event response: {event_response.strip()}")
                
                sock.close()
                return True
            else:
                print("❌ TG SMS authentication failed!")
                print("   Check username/password in gateway settings")
                sock.close()
                return False
                
        except Exception as e:
            print(f"❌ Connection test failed: {str(e)}")
            return False

def restart_receiver():
    """Restart the SMS receiver"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔄 RESTARTING SMS RECEIVER")
        print("="*60)
        
        # Stop current receiver
        receiver = getattr(app, 'sms_receiver', None)
        if receiver:
            print("🛑 Stopping current receiver...")
            try:
                receiver.stop_receiver()
                print("✅ Receiver stopped")
            except Exception as e:
                print(f"⚠️  Error stopping receiver: {str(e)}")
            
            time.sleep(2)
        
        # Start new receiver
        print("🚀 Starting new receiver...")
        try:
            from app.sms.receiver import SMSReceiver
            new_receiver = SMSReceiver(app)
            new_receiver.start_receiver()
            app.sms_receiver = new_receiver
            
            print("✅ New receiver started")
            
            # Wait and check status
            time.sleep(3)
            print(f"\n📊 New receiver status:")
            print(f"   Running: {'✅ Yes' if new_receiver.running else '❌ No'}")
            print(f"   Connected: {'✅ Yes' if new_receiver.connected else '❌ No'}")
            print(f"   Thread alive: {'✅ Yes' if (new_receiver.thread and new_receiver.thread.is_alive()) else '❌ No'}")
            
            return new_receiver.running and new_receiver.connected
            
        except Exception as e:
            print(f"❌ Failed to start receiver: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    print("🚀 SMS Receiver Diagnostic Tool")
    
    # Check current status
    receiver_ok = check_receiver_status()
    
    # Test gateway connection
    gateway_ok = test_gateway_connection()
    
    if not receiver_ok or not gateway_ok:
        print(f"\n⚠️  Issues detected! Attempting to restart receiver...")
        
        if gateway_ok:  # Only restart if gateway is reachable
            success = restart_receiver()
            
            if success:
                print(f"\n🎉 SMS receiver restart successful!")
                print(f"📱 Try sending an SMS to test receiving")
            else:
                print(f"\n❌ SMS receiver restart failed!")
                print(f"🔧 Manual intervention may be required")
        else:
            print(f"\n❌ Gateway connection failed - cannot restart receiver")
            print(f"🔧 Fix gateway connection first")
    else:
        print(f"\n✅ SMS receiver is working properly!")
        print(f"📱 Ready to receive SMS messages")
    
    print("\n" + "="*60)
