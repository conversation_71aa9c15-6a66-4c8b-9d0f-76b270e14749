# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

from cryptography.hazmat.primitives.asymmetric import dh

MIN_MODULUS_SIZE: int

class DHPrivateKey: ...
class DHPublicKey: ...
class DHParameters: ...

def generate_parameters(generator: int, key_size: int) -> dh.DHParameters: ...
def private_key_from_ptr(ptr: int) -> dh.DHPrivateKey: ...
def public_key_from_ptr(ptr: int) -> dh.DHPublicKey: ...
def from_pem_parameters(data: bytes) -> dh.DHParameters: ...
def from_der_parameters(data: bytes) -> dh.DHParameters: ...
def from_private_numbers(numbers: dh.DHPrivateNumbers) -> dh.DHPrivateKey: ...
def from_public_numbers(numbers: dh.DHPublicNumbers) -> dh.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: ...
def from_parameter_numbers(
    numbers: dh.DHParameterNumbers,
) -> dh.DHParameters: ...
