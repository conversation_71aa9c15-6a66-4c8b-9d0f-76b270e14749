{% extends "base.html" %}

{% block title %}Edit {{ gateway.name }} - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Edit Gateway</h1>
        <p class="text-muted">Update {{ gateway.name }} configuration</p>
    </div>
    <div>
        <a href="{{ url_for('gateways.detail', gateway_id=gateway.id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Details
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-pencil-square"></i> Gateway Configuration</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label fw-semibold">Gateway Name *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ gateway.name }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="ip_address" class="form-label fw-semibold">IP Address *</label>
                            <input type="text" class="form-control" id="ip_address" name="ip_address" 
                                   value="{{ gateway.ip_address }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="api_port" class="form-label fw-semibold">API Port</label>
                            <input type="number" class="form-control" id="api_port" name="api_port" 
                                   value="{{ gateway.api_port }}" min="1" max="65535">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="tg_sms_port" class="form-label fw-semibold">TG SMS Port</label>
                            <input type="number" class="form-control" id="tg_sms_port" name="tg_sms_port" 
                                   value="{{ gateway.tg_sms_port }}" min="1" max="65535">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label fw-semibold">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="{{ gateway.username }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label fw-semibold">Password *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       value="{{ gateway.password }}" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="bi bi-eye" id="passwordIcon"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_ports" class="form-label fw-semibold">Maximum Ports</label>
                            <input type="number" class="form-control" id="max_ports" name="max_ports" 
                                   value="{{ gateway.max_ports }}" min="1" max="32">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label fw-semibold">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" {{ 'selected' if gateway.status == 'active' }}>Active</option>
                                <option value="inactive" {{ 'selected' if gateway.status == 'inactive' }}>Inactive</option>
                                <option value="maintenance" {{ 'selected' if gateway.status == 'maintenance' }}>Maintenance</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="location" class="form-label fw-semibold">Location</label>
                        <input type="text" class="form-control" id="location" name="location" 
                               value="{{ gateway.location or '' }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label fw-semibold">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ gateway.description or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_primary" name="is_primary" 
                                   {{ 'checked' if gateway.is_primary }}>
                            <label class="form-check-label fw-semibold" for="is_primary">
                                Set as Primary Gateway
                            </label>
                            <div class="form-text">Primary gateway is used as default for sending SMS</div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Gateway
                        </button>
                        
                        <button type="button" class="btn btn-outline-info" onclick="testConnection()">
                            <i class="bi bi-wifi"></i> Test Connection
                        </button>
                        
                        <a href="{{ url_for('gateways.detail', gateway_id=gateway.id) }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Current Status</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Current Status:</strong><br>
                    {% if gateway.status == 'active' %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-danger">{{ gateway.status.title() }}</span>
                    {% endif %}
                    {% if gateway.is_primary %}
                        <span class="badge bg-primary ms-1">Primary</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <span class="text-muted">{{ gateway.created_at.strftime('%B %d, %Y') if gateway.created_at else 'Unknown' }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <span class="text-muted">{{ gateway.updated_at.strftime('%B %d, %Y at %I:%M %p') if gateway.updated_at else 'Never' }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Total Ports:</strong><br>
                    <span class="text-muted">{{ gateway.max_ports }}</span>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Important Notes</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="bi bi-info-circle text-info"></i> Changing IP address will affect connectivity</li>
                    <li><i class="bi bi-info-circle text-info"></i> Only one gateway can be primary</li>
                    <li><i class="bi bi-info-circle text-info"></i> Test connection before saving changes</li>
                    <li><i class="bi bi-info-circle text-info"></i> Port changes require gateway restart</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('passwordIcon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'bi bi-eye';
        }
    }

    function testConnection() {
        const form = document.querySelector('form');
        const formData = new FormData(form);
        
        // Show loading
        const btn = event.target;
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<span class="loading-spinner"></span> Testing...';
        btn.disabled = true;
        
        // Test with current form values
        fetch(`/gateways/{{ gateway.id }}/test`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Gateway connection successful!', 'success');
            } else {
                showToast('Gateway connection failed: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Error testing gateway: ' + error.message, 'error');
        })
        .finally(() => {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        });
    }

    // Auto-format IP address
    document.getElementById('ip_address').addEventListener('input', function(e) {
        let value = e.target.value;
        value = value.replace(/[^\d.]/g, '');
        e.target.value = value;
    });
</script>
{% endblock %}
