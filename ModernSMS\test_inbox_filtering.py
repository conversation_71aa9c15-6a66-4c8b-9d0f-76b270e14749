#!/usr/bin/env python3
"""
Test inbox filtering for users
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import SMSMessage, SMSPort, User
from datetime import datetime

def test_inbox_filtering():
    """Test inbox filtering for different users"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("📥 INBOX FILTERING TEST")
        print("="*60)
        
        try:
            # Get all users
            users = User.query.all()
            print(f"\n👥 Found {len(users)} users:")
            for user in users:
                print(f"  - {user.username}: {user.assigned_ports}")
            
            # Get all received messages
            all_received = SMSMessage.query.filter_by(direction='inbound').all()
            print(f"\n📥 Total received messages: {len(all_received)}")
            for msg in all_received:
                print(f"  - From {msg.phone_number} via Port {msg.port_id}: {msg.content[:30]}...")
            
            # Test filtering for each user
            print(f"\n🔍 Testing inbox filtering for each user:")
            
            for user in users:
                print(f"\n👤 User: {user.username}")
                print(f"   Assigned ports: {user.assigned_ports}")
                print(f"   Is admin: {user.has_permission('manage_users')}")
                
                if user.has_permission('manage_users'):
                    # Admin should see all messages
                    visible_messages = all_received
                    print(f"   Admin - should see all {len(all_received)} messages")
                else:
                    # Regular user - filter by assigned ports
                    visible_messages = []
                    assigned_ports = user.get_assigned_ports()
                    
                    if assigned_ports:
                        # Convert assignments to actual port IDs
                        port_ids = []
                        for assignment in assigned_ports:
                            gateway_id = assignment.get('gateway_id')
                            port_number = assignment.get('port')
                            
                            if gateway_id and port_number:
                                # Find port by gateway_id and port_number
                                port = SMSPort.query.filter_by(
                                    gateway_id=gateway_id,
                                    port_number=str(port_number),
                                    is_active=True
                                ).first()
                                if port:
                                    port_ids.append(port.id)
                                    print(f"   Found port ID {port.id} for Gateway {gateway_id}, Port {port_number}")
                        
                        print(f"   Port IDs to filter by: {port_ids}")
                        
                        # Filter messages by port IDs
                        for msg in all_received:
                            if msg.port_id in port_ids:
                                visible_messages.append(msg)
                                print(f"   ✅ Can see message from {msg.phone_number} (Port {msg.port_id})")
                            else:
                                print(f"   ❌ Cannot see message from {msg.phone_number} (Port {msg.port_id})")
                    else:
                        print(f"   No assigned ports - should see 0 messages")
                
                print(f"   Result: Should see {len(visible_messages)} messages")
                
                # Show which messages they should see
                if visible_messages:
                    for msg in visible_messages:
                        print(f"     - From {msg.phone_number}: {msg.content[:30]}...")
            
            print(f"\n✅ Inbox filtering test completed!")
            
            # Test specific scenarios
            print(f"\n🎯 Specific Test Scenarios:")
            
            # API user should only see messages from Port 4
            apiuser = User.query.filter_by(username='apiuser').first()
            if apiuser:
                port4_messages = SMSMessage.query.filter_by(direction='inbound', port_id=4).all()
                print(f"\n📱 API User (assigned to Port 4):")
                print(f"   Messages from Port 4: {len(port4_messages)}")
                for msg in port4_messages:
                    print(f"     - From {msg.phone_number}: {msg.content[:30]}...")
                
                # Messages from other ports (should NOT see)
                other_messages = SMSMessage.query.filter(
                    SMSMessage.direction == 'inbound',
                    SMSMessage.port_id != 4
                ).all()
                print(f"   Messages from other ports (should NOT see): {len(other_messages)}")
                for msg in other_messages:
                    print(f"     - From {msg.phone_number} (Port {msg.port_id}): {msg.content[:30]}...")
            
            # Test user should only see messages from Ports 1 and 2
            testuser = User.query.filter_by(username='testuser').first()
            if testuser:
                port12_messages = SMSMessage.query.filter(
                    SMSMessage.direction == 'inbound',
                    SMSMessage.port_id.in_([1, 2])
                ).all()
                print(f"\n📱 Test User (assigned to Ports 1 & 2):")
                print(f"   Messages from Ports 1 & 2: {len(port12_messages)}")
                for msg in port12_messages:
                    print(f"     - From {msg.phone_number} (Port {msg.port_id}): {msg.content[:30]}...")
            
        except Exception as e:
            print(f"❌ Error during test: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_inbox_filtering()
