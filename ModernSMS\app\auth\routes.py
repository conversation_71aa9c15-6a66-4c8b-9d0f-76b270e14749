"""
Authentication routes
"""
from flask import render_template, request, redirect, url_for, flash, current_app
from flask_login import login_user, logout_user, login_required, current_user
from app.auth import bp
from app.models import User
from app import db
from datetime import datetime, timedelta

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember_me = bool(request.form.get('remember_me'))
        
        if not username or not password:
            flash('Please enter both username and password.', 'error')
            return render_template('auth/login.html')
        
        user = User.query.filter_by(username=username).first()
        
        if user is None:
            flash('Invalid username or password.', 'error')
            return render_template('auth/login.html')
        
        # Check if account is locked
        if user.locked_until and user.locked_until > datetime.now():
            flash('Account is temporarily locked. Please try again later.', 'error')
            return render_template('auth/login.html')
        
        # Check if account is active
        if not user.is_active:
            flash('Account is disabled. Please contact administrator.', 'error')
            return render_template('auth/login.html')
        
        # Verify password
        if not user.check_password(password):
            # Increment login attempts
            user.login_attempts += 1
            
            # Lock account after max attempts
            max_attempts = current_app.config.get('MAX_LOGIN_ATTEMPTS', 5)
            if user.login_attempts >= max_attempts:
                user.locked_until = datetime.now() + timedelta(minutes=30)
                flash('Too many failed attempts. Account locked for 30 minutes.', 'error')
            else:
                remaining = max_attempts - user.login_attempts
                flash(f'Invalid username or password. {remaining} attempts remaining.', 'error')
            
            db.session.commit()
            return render_template('auth/login.html')
        
        # Successful login
        user.login_attempts = 0
        user.locked_until = None
        user.last_login = datetime.now()
        db.session.commit()
        
        login_user(user, remember=remember_me)
        
        # Redirect to next page or dashboard
        next_page = request.args.get('next')
        if next_page:
            return redirect(next_page)
        
        flash(f'Welcome back, {user.full_name}!', 'success')
        return redirect(url_for('main.dashboard'))
    
    return render_template('auth/login.html')

@bp.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('auth.login'))

@bp.route('/profile')
@login_required
def profile():
    """User profile"""
    return render_template('auth/profile.html', user=current_user)

@bp.route('/profile/update', methods=['POST'])
@login_required
def update_profile():
    """Update user profile"""
    try:
        current_user.first_name = request.form.get('first_name', '').strip()
        current_user.last_name = request.form.get('last_name', '').strip()
        current_user.email = request.form.get('email', '').strip()
        
        # Validate email
        if not current_user.email:
            flash('Email is required.', 'error')
            return redirect(url_for('auth.profile'))
        
        # Check if email is already taken by another user
        existing_user = User.query.filter(
            User.email == current_user.email,
            User.id != current_user.id
        ).first()
        
        if existing_user:
            flash('Email is already taken by another user.', 'error')
            return redirect(url_for('auth.profile'))
        
        db.session.commit()
        flash('Profile updated successfully.', 'success')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Profile update error: {str(e)}")
        flash('Error updating profile.', 'error')
    
    return redirect(url_for('auth.profile'))

@bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """Change user password"""
    try:
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        # Validate inputs
        if not all([current_password, new_password, confirm_password]):
            flash('All password fields are required.', 'error')
            return redirect(url_for('auth.profile'))
        
        # Check current password
        if not current_user.check_password(current_password):
            flash('Current password is incorrect.', 'error')
            return redirect(url_for('auth.profile'))
        
        # Check password confirmation
        if new_password != confirm_password:
            flash('New passwords do not match.', 'error')
            return redirect(url_for('auth.profile'))
        
        # Validate password strength
        if len(new_password) < 6:
            flash('Password must be at least 6 characters long.', 'error')
            return redirect(url_for('auth.profile'))
        
        # Update password
        current_user.set_password(new_password)
        db.session.commit()
        
        flash('Password changed successfully.', 'success')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Password change error: {str(e)}")
        flash('Error changing password.', 'error')
    
    return redirect(url_for('auth.profile'))
