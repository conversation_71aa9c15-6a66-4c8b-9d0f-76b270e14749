{% extends "base.html" %}

{% block title %}User Management - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">User Management</h1>
        <p class="text-muted">Manage user accounts and permissions</p>
    </div>
    <div>
        <a href="{{ url_for('users.add') }}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> Add User
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <h3>{{ stats.total_users or 0 }}</h3>
            <p><i class="bi bi-people"></i> Total Users</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #059669 0%, #10b981 100%);">
            <h3>{{ stats.active_users or 0 }}</h3>
            <p><i class="bi bi-person-check"></i> Active</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);">
            <h3>{{ stats.admin_users or 0 }}</h3>
            <p><i class="bi bi-shield-check"></i> Admins</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);">
            <h3>{{ stats.regular_users or 0 }}</h3>
            <p><i class="bi bi-person"></i> Regular Users</p>
        </div>
    </div>
</div>

<!-- Users List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-list"></i> Users</h5>
        <button class="btn btn-sm btn-outline-primary" onclick="refreshUsers()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
    <div class="card-body">
        {% if users %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Role</th>
                            <th>Permissions</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-2">
                                        {{ user.username[0].upper() }}
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ user.full_name }}</div>
                                        <small class="text-muted">{{ user.username }}</small><br>
                                        <small class="text-muted">{{ user.email }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if user.role %}
                                    <span class="badge bg-primary">{{ user.role.name.title() }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">User</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex flex-wrap gap-1">
                                    {% if user.can_send_sms %}
                                        <span class="badge bg-success" title="Can Send SMS">SMS</span>
                                    {% endif %}
                                    {% if user.can_view_inbox %}
                                        <span class="badge bg-info" title="Can View Inbox">Inbox</span>
                                    {% endif %}
                                    {% if user.can_manage_gateways %}
                                        <span class="badge bg-warning" title="Can Manage Gateways">Gateways</span>
                                    {% endif %}
                                    {% if user.can_manage_users %}
                                        <span class="badge bg-danger" title="Can Manage Users">Users</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if user.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.last_login %}
                                    <small class="text-muted">
                                        {{ user.last_login.strftime('%m/%d/%Y %H:%M') }}
                                    </small>
                                {% else %}
                                    <small class="text-muted">Never</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('users.detail', user_id=user.id) }}" 
                                       class="btn btn-outline-primary" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url_for('users.edit', user_id=user.id) }}" 
                                       class="btn btn-outline-secondary" title="Edit User">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    {% if user.id != current_user.id %}
                                    <button class="btn btn-outline-danger" 
                                            onclick="toggleUserStatus({{ user.id }}, '{{ user.username }}', {{ user.is_active|lower }})"
                                            title="{{ 'Deactivate' if user.is_active else 'Activate' }} User">
                                        <i class="bi bi-{{ 'person-x' if user.is_active else 'person-check' }}"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">No Users Found</h4>
                <p class="text-muted">Create your first user account to get started.</p>
                <a href="{{ url_for('users.add') }}" class="btn btn-primary">
                    <i class="bi bi-person-plus"></i> Add Your First User
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), #3b82f6);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .badge {
        font-size: 0.7rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function refreshUsers() {
        location.reload();
    }

    function toggleUserStatus(userId, username, isActive) {
        const action = isActive ? 'deactivate' : 'activate';
        const message = `Are you sure you want to ${action} user "${username}"?`;
        
        if (confirm(message)) {
            // This would be implemented with an AJAX call to toggle user status
            showToast(`User ${action} functionality would be implemented here`, 'info');
        }
    }
</script>
{% endblock %}
