{% extends "base.html" %}

{% block title %}Conversation with {{ phone_number }} - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Conversation</h1>
        <p class="text-muted">{{ phone_number }}</p>
    </div>
    <div>
        <a href="{{ url_for('sms.inbox') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Inbox
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-chat-dots"></i> Messages</h5>
                <span class="badge bg-primary">{{ messages|length }} messages</span>
            </div>
            <div class="card-body" style="height: 500px; overflow-y: auto;">
                {% if messages %}
                    <div class="conversation-messages">
                        {% for message in messages %}
                        <div class="message-bubble {{ 'outbound' if message.direction == 'outbound' else 'inbound' }} mb-3">
                            <div class="message-content">
                                <div class="message-text">{{ message.content }}</div>
                                <div class="message-meta">
                                    <small class="text-muted">
                                        {{ message.created_at.strftime('%m/%d/%Y %H:%M') }}
                                        {% if message.direction == 'outbound' %}
                                            - 
                                            {% if message.status == 'sent' %}
                                                <span class="text-success">Sent</span>
                                            {% elif message.status == 'delivered' %}
                                                <span class="text-success">Delivered</span>
                                            {% elif message.status == 'failed' %}
                                                <span class="text-danger">Failed</span>
                                            {% elif message.status == 'pending' %}
                                                <span class="text-warning">Pending</span>
                                            {% endif %}
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-2">No messages in this conversation</h6>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Reply -->
        {% if current_user.can_send_sms %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-reply"></i> Quick Reply</h6>
            </div>
            <div class="card-body">
                <form id="replyForm">
                    <div class="row">
                        <div class="col-md-8">
                            <textarea class="form-control" id="replyMessage" placeholder="Type your reply..." rows="2" maxlength="160"></textarea>
                            <div class="form-text">
                                <span id="replyCharCount">0</span>/160 characters
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select mb-2" id="replyPort">
                                <option value="">Select port...</option>
                                {% for port in ports %}
                                <option value="{{ port.id }}">
                                    Gateway {{ port.gateway_id }} - Port {{ port.port_number }}
                                </option>
                                {% endfor %}
                            </select>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-send"></i> Send Reply
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Conversation Info</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Phone Number:</strong><br>
                    <span class="text-muted">{{ phone_number }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Total Messages:</strong><br>
                    <span class="text-muted">{{ stats.total_messages }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Sent:</strong><br>
                    <span class="text-muted">{{ stats.sent_count }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Received:</strong><br>
                    <span class="text-muted">{{ stats.received_count }}</span>
                </div>
                
                {% if stats.last_message_time %}
                <div class="mb-3">
                    <strong>Last Message:</strong><br>
                    <span class="text-muted">{{ stats.last_message_time.strftime('%m/%d/%Y %H:%M') }}</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .conversation-messages {
        display: flex;
        flex-direction: column;
    }
    
    .message-bubble {
        max-width: 70%;
        margin-bottom: 10px;
    }
    
    .message-bubble.outbound {
        align-self: flex-end;
    }
    
    .message-bubble.inbound {
        align-self: flex-start;
    }
    
    .message-content {
        padding: 12px 16px;
        border-radius: 18px;
        position: relative;
    }
    
    .message-bubble.outbound .message-content {
        background: linear-gradient(135deg, var(--primary-color), #3b82f6);
        color: white;
    }
    
    .message-bubble.inbound .message-content {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #e9ecef;
    }
    
    .message-text {
        margin-bottom: 4px;
        word-wrap: break-word;
    }
    
    .message-meta {
        font-size: 0.75rem;
        opacity: 0.8;
    }
    
    .conversation-messages {
        scroll-behavior: smooth;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Character counter for reply
    document.getElementById('replyMessage').addEventListener('input', function() {
        const charCount = this.value.length;
        document.getElementById('replyCharCount').textContent = charCount;
        
        if (charCount > 160) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });

    // Handle reply form
    document.getElementById('replyForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const message = document.getElementById('replyMessage').value.trim();
        const portId = document.getElementById('replyPort').value;
        
        if (!message) {
            showToast('Please enter a message', 'error');
            return;
        }
        
        if (!portId) {
            showToast('Please select a port', 'error');
            return;
        }
        
        const formData = new FormData();
        formData.append('phone_number', '{{ phone_number }}');
        formData.append('message', message);
        formData.append('port_id', portId);
        
        fetch('{{ url_for("sms.send_sms") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Reply sent successfully!', 'success');
                document.getElementById('replyMessage').value = '';
                document.getElementById('replyCharCount').textContent = '0';
                // Reload page to show new message
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Error: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Failed to send reply: ' + error.message, 'error');
        });
    });

    // Auto-scroll to bottom of conversation
    document.addEventListener('DOMContentLoaded', function() {
        const messagesContainer = document.querySelector('.card-body');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    });
</script>
{% endblock %}
