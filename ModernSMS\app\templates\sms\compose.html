{% extends "base.html" %}

{% block title %}Compose SMS - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Compose SMS</h1>
        <p class="text-muted">Send a new SMS message</p>
    </div>
    <div>
        <a href="{{ url_for('sms.outbox') }}" class="btn btn-outline-secondary">
            <i class="bi bi-send"></i> View Outbox
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-pencil-square"></i> New Message</h5>
            </div>
            <div class="card-body">
                <form id="smsForm">
                    <div class="mb-3">
                        <label for="phone_number" class="form-label">Phone Number *</label>
                        <input type="tel" class="form-control" id="phone_number" name="phone_number"
                               placeholder="09123456789" required>
                        <div class="form-text">Enter phone number in any format (e.g., 09123456789, +639123456789, ************)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="port_id" class="form-label">Select Port *</label>
                        <select class="form-select" id="port_id" name="port_id" required>
                            <option value="">Choose a port...</option>
                            {% for port in ports %}
                            <option value="{{ port.id }}">
                                Gateway {{ port.gateway_id }} - Port {{ port.port_number }}
                                {% if port.status == 'active' %}
                                    ({{ port.network_name or 'Active' }})
                                {% else %}
                                    ({{ port.status.title() }})
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Select which port to send the message from</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="message" class="form-label">Message *</label>
                        <textarea class="form-control" id="message" name="message" rows="4" 
                                  placeholder="Type your message here..." required maxlength="160"></textarea>
                        <div class="form-text">
                            <span id="charCount">0</span>/160 characters
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="sendBtn">
                            <i class="bi bi-send"></i>
                            <span class="btn-text">Send Message</span>
                            <span class="loading-spinner d-none"></span>
                        </button>
                        
                        <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                            <i class="bi bi-x-circle"></i> Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Quick Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="bi bi-check text-success"></i> Phone numbers accept any format</li>
                    <li><i class="bi bi-check text-success"></i> Keep messages under 160 characters</li>
                    <li><i class="bi bi-check text-success"></i> Choose an active port for best delivery</li>
                    <li><i class="bi bi-check text-success"></i> Check port status before sending</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-diagram-3"></i> Available Ports</h6>
            </div>
            <div class="card-body">
                {% if ports %}
                    {% for port in ports %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>Port {{ port.port_number }}</strong><br>
                            <small class="text-muted">Gateway {{ port.gateway_id }}</small>
                        </div>
                        <div>
                            {% if port.status == 'active' %}
                                <span class="badge bg-success">Active</span>
                            {% elif port.status == 'sim_only' %}
                                <span class="badge bg-warning">SIM Only</span>
                            {% elif port.status == 'no_sim' %}
                                <span class="badge bg-danger">No SIM</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ port.status.title() }}</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No ports available. Please configure a gateway first.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Character counter
    document.getElementById('message').addEventListener('input', function() {
        const charCount = this.value.length;
        document.getElementById('charCount').textContent = charCount;
        
        if (charCount > 160) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });

    // Form submission
    document.getElementById('smsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const sendBtn = document.getElementById('sendBtn');
        const btnText = sendBtn.querySelector('.btn-text');
        const spinner = sendBtn.querySelector('.loading-spinner');
        
        // Show loading state
        btnText.classList.add('d-none');
        spinner.classList.remove('d-none');
        sendBtn.disabled = true;
        
        const formData = new FormData(this);
        
        fetch('{{ url_for("sms.send_sms") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('SMS sent successfully!', 'success');
                clearForm();
            } else {
                showToast('Error: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Failed to send SMS: ' + error.message, 'error');
        })
        .finally(() => {
            // Restore button state
            btnText.classList.remove('d-none');
            spinner.classList.add('d-none');
            sendBtn.disabled = false;
        });
    });

    function clearForm() {
        document.getElementById('smsForm').reset();
        document.getElementById('charCount').textContent = '0';
        document.getElementById('message').classList.remove('is-invalid');
    }

    // Phone number validation (basic)
    document.getElementById('phone_number').addEventListener('input', function(e) {
        let value = e.target.value;

        // Allow digits, spaces, dashes, parentheses, and + symbol
        value = value.replace(/[^\d\s\-\(\)\+]/g, '');

        e.target.value = value;

        // Basic validation feedback
        const cleaned = value.replace(/[^\d]/g, '');
        if (cleaned.length >= 7 && cleaned.length <= 15) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (cleaned.length > 0) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
</script>
{% endblock %}
