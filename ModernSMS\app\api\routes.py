"""
API routes for external integrations and AJAX calls
"""
from flask import request, jsonify, current_app
from flask_login import login_required, current_user
from app.api import bp
from app.models import SMSMessage, GSMGateway, SMSPort, User
from app.sms.services import SMSService, GSMPortService
from app import db
from datetime import datetime, timedelta
import uuid

@bp.route('/messages', methods=['GET'])
@login_required
def get_messages():
    """Get messages with pagination and filtering"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        direction = request.args.get('direction')
        phone_number = request.args.get('phone_number')
        status = request.args.get('status')
        
        # Build query
        query = SMSMessage.query
        
        # Apply filters
        if direction:
            query = query.filter_by(direction=direction)
        if phone_number:
            query = query.filter_by(phone_number=phone_number)
        if status:
            query = query.filter_by(status=status)
        
        # Apply user permissions
        if not current_user.has_permission('manage_users'):
            if direction == 'outbound':
                query = query.filter_by(user_id=current_user.id)
            elif direction == 'inbound':
                assigned_ports = current_user.get_assigned_ports()
                if assigned_ports:
                    port_ids = [p.get('port_id') for p in assigned_ports if p.get('port_id')]
                    if port_ids:
                        query = query.filter(SMSMessage.port_id.in_(port_ids))
        
        # Paginate
        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'success': True,
            'messages': [msg.to_dict() for msg in messages.items],
            'pagination': {
                'page': messages.page,
                'pages': messages.pages,
                'per_page': messages.per_page,
                'total': messages.total,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"API get messages error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/messages/send', methods=['POST'])
@login_required
def send_message():
    """Send SMS via API"""
    try:
        data = request.get_json()
        
        phone_number = data.get('phone_number', '').strip()
        message = data.get('message', '').strip()
        port_id = data.get('port_id', type=int)
        
        # Validate inputs
        if not phone_number:
            return jsonify({'success': False, 'error': 'Phone number is required'}), 400
        
        if not message:
            return jsonify({'success': False, 'error': 'Message is required'}), 400
        
        if not port_id:
            return jsonify({'success': False, 'error': 'Port ID is required'}), 400
        
        # Validate phone number
        if not SMSService.validate_phone_number(phone_number):
            return jsonify({'success': False, 'error': 'Invalid phone number format'}), 400
        
        # Get port and validate permissions
        port = SMSPort.query.get(port_id)
        if not port:
            return jsonify({'success': False, 'error': 'Invalid port ID'}), 400
        
        if not current_user.can_use_port(port.port_number, port.gateway_id):
            return jsonify({'success': False, 'error': 'Permission denied for this port'}), 403
        
        # Send SMS
        result = SMSService.send_sms(
            phone_number=phone_number,
            message=message,
            port=port,
            user=current_user
        )
        
        if result['success']:
            return jsonify({
                'success': True,
                'message_id': result['message_id'],
                'message': 'SMS sent successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"API send message error: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@bp.route('/gateways', methods=['GET'])
@login_required
def get_gateways():
    """Get gateways list"""
    try:
        if not current_user.has_permission('manage_gateways'):
            return jsonify({'success': False, 'error': 'Permission denied'}), 403
        
        gateways = GSMGateway.query.all()
        
        gateways_data = []
        for gw in gateways:
            gw_dict = gw.to_dict()
            # Add port summary
            gw_dict['port_summary'] = {'total': gw.max_ports}
            gateways_data.append(gw_dict)

        return jsonify({
            'success': True,
            'gateways': gateways_data
        })
        
    except Exception as e:
        current_app.logger.error(f"API get gateways error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/gateways/<int:gateway_id>/ports', methods=['GET'])
@login_required
def get_gateway_ports(gateway_id):
    """Get ports for specific gateway"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)
        ports = gateway.ports.all()
        
        return jsonify({
            'success': True,
            'gateway_id': gateway_id,
            'gateway_name': gateway.name,
            'ports': [port.to_dict() for port in ports]
        })
        
    except Exception as e:
        current_app.logger.error(f"API get gateway ports error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/ports/refresh', methods=['POST'])
@login_required
def refresh_all_ports():
    """Refresh all port statuses"""
    try:
        if not current_user.has_permission('manage_gateways'):
            return jsonify({'success': False, 'error': 'Permission denied'}), 403
        
        result = GSMPortService.refresh_all_ports()
        
        return jsonify(result)
        
    except Exception as e:
        current_app.logger.error(f"API refresh ports error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/stats', methods=['GET'])
@login_required
def get_stats():
    """Get system statistics"""
    try:
        from app.main.routes import get_dashboard_stats
        stats = get_dashboard_stats()
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        current_app.logger.error(f"API get stats error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/conversations', methods=['GET'])
@login_required
def get_conversations():
    """Get conversation list"""
    try:
        # Get unique phone numbers with message counts
        conversations = db.session.query(
            SMSMessage.phone_number,
            db.func.count(SMSMessage.id).label('message_count'),
            db.func.max(SMSMessage.created_at).label('last_message')
        ).group_by(SMSMessage.phone_number)\
         .order_by(db.func.max(SMSMessage.created_at).desc())\
         .limit(50).all()
        
        conversation_list = []
        for conv in conversations:
            # Get last message
            last_msg = SMSMessage.query.filter_by(phone_number=conv.phone_number)\
                                     .order_by(SMSMessage.created_at.desc()).first()
            
            conversation_list.append({
                'phone_number': conv.phone_number,
                'message_count': conv.message_count,
                'last_message_time': conv.last_message.isoformat() if conv.last_message else None,
                'last_message_content': last_msg.content[:50] + '...' if last_msg and len(last_msg.content) > 50 else last_msg.content if last_msg else '',
                'last_message_direction': last_msg.direction if last_msg else None
            })
        
        return jsonify({
            'success': True,
            'conversations': conversation_list
        })
        
    except Exception as e:
        current_app.logger.error(f"API get conversations error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/conversations/<phone_number>/messages', methods=['GET'])
@login_required
def get_conversation_messages(phone_number):
    """Get messages for specific conversation"""
    try:
        messages = SMSMessage.query.filter_by(phone_number=phone_number)\
                                  .order_by(SMSMessage.created_at.asc()).all()
        
        # Apply user permissions
        if not current_user.has_permission('manage_users'):
            filtered_messages = []
            assigned_ports = current_user.get_assigned_ports()
            port_ids = [p.get('port_id') for p in assigned_ports if p.get('port_id')]
            
            for msg in messages:
                if (msg.direction == 'outbound' and msg.user_id == current_user.id) or \
                   (msg.direction == 'inbound' and (not port_ids or msg.port_id in port_ids)):
                    filtered_messages.append(msg)
            
            messages = filtered_messages
        
        return jsonify({
            'success': True,
            'phone_number': phone_number,
            'messages': [msg.to_dict() for msg in messages]
        })
        
    except Exception as e:
        current_app.logger.error(f"API get conversation messages error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/simulate/receive-sms', methods=['POST'])
@login_required
def simulate_receive_sms():
    """Simulate receiving an SMS (development mode only)"""
    try:
        if not current_app.config.get('FLASK_ENV') == 'development':
            return jsonify({'success': False, 'error': 'Only available in development mode'}), 403

        data = request.get_json()
        phone_number = data.get('phone_number', '+1234567890')
        message = data.get('message', 'Test received message')
        port_id = data.get('port_id', 1)

        # Get port and gateway
        port = SMSPort.query.get(port_id)
        if not port:
            # Create a default port if none exists
            gateway = GSMGateway.query.first()
            if not gateway:
                return jsonify({'success': False, 'error': 'No gateway available'}), 400

            port = SMSPort.query.filter_by(gateway_id=gateway.id).first()
            if not port:
                return jsonify({'success': False, 'error': 'No port available'}), 400

        # Create received message
        import uuid
        message_id = str(uuid.uuid4())

        sms_message = SMSMessage(
            message_id=message_id,
            direction='inbound',
            phone_number=phone_number,
            content=message,
            status='received',
            gateway_id=port.gateway_id,
            port_id=port.id,
            created_at=datetime.now()
        )

        db.session.add(sms_message)
        db.session.commit()

        current_app.logger.info(f"DEV MODE: Simulated received SMS from {phone_number}: {message}")

        return jsonify({
            'success': True,
            'message': 'SMS received successfully (simulated)',
            'message_id': message_id
        })

    except Exception as e:
        current_app.logger.error(f"Simulate receive SMS error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/webhook/receive-sms', methods=['POST'])
def webhook_receive_sms():
    """Webhook endpoint for GSM gateway to send received SMS"""
    try:
        # This endpoint can be called by the GSM gateway when SMS is received
        # Format depends on your gateway's webhook configuration

        data = request.get_json() or request.form.to_dict()
        current_app.logger.info(f"SMS webhook received: {data}")

        # Extract SMS data (adjust fields based on your gateway's format)
        phone_number = data.get('from') or data.get('sender') or data.get('phone_number')
        message = data.get('message') or data.get('content') or data.get('text')
        port_number = data.get('port') or data.get('gsm_port') or '1'
        message_id = data.get('id') or data.get('message_id') or str(uuid.uuid4())

        if not phone_number or not message:
            return jsonify({'success': False, 'error': 'Missing phone_number or message'}), 400

        # Find or create port
        port = SMSPort.query.filter_by(port_number=str(port_number)).first()
        if not port:
            # Get first available gateway
            gateway = GSMGateway.query.filter_by(status='active').first()
            if gateway:
                port = SMSPort(
                    port_number=str(port_number),
                    gateway_id=gateway.id,
                    status='active',
                    is_active=True
                )
                db.session.add(port)
                db.session.flush()

        # Create received message
        sms_message = SMSMessage(
            message_id=message_id,
            direction='inbound',
            phone_number=phone_number,
            content=message,
            status='received',
            gateway_id=port.gateway_id if port else None,
            port_id=port.id if port else None,
            created_at=datetime.now()
        )

        db.session.add(sms_message)
        db.session.commit()

        current_app.logger.info(f"SMS received via webhook: {phone_number} -> {message}")

        return jsonify({
            'success': True,
            'message': 'SMS received successfully',
            'message_id': message_id
        })

    except Exception as e:
        current_app.logger.error(f"Webhook receive SMS error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/test-receiver', methods=['POST'])
@login_required
def test_receiver():
    """Test SMS receiver functionality"""
    try:
        # Get receiver from app instance
        receiver = getattr(current_app, 'sms_receiver', None)

        if not receiver:
            return jsonify({
                'success': False,
                'error': 'SMS receiver not initialized'
            }), 500

        # Check receiver status
        status = {
            'running': receiver.running,
            'connected': receiver.connected,
            'thread_alive': receiver.thread.is_alive() if receiver.thread else False
        }

        # Get gateway info
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        gateway_info = None
        if gateway:
            gateway_info = {
                'name': gateway.name,
                'ip': gateway.ip_address,
                'tg_port': gateway.tg_sms_port
            }

        return jsonify({
            'success': True,
            'receiver_status': status,
            'gateway_info': gateway_info,
            'message': 'SMS receiver status retrieved'
        })

    except Exception as e:
        current_app.logger.error(f"Test receiver error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/restart-receiver', methods=['POST'])
@login_required
def restart_receiver():
    """Restart SMS receiver with current gateway settings"""
    try:
        current_app.logger.info("🔄 Manual SMS receiver restart requested")

        # Get current receiver
        receiver = getattr(current_app, 'sms_receiver', None)

        # Stop current receiver if exists
        if receiver:
            receiver.stop_receiver()
            current_app.logger.info("🛑 Stopped current SMS receiver")

            # Wait for cleanup
            import time
            time.sleep(2)

        # Get primary gateway
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        if not gateway:
            return jsonify({
                'success': False,
                'error': 'No active primary gateway found'
            }), 400

        # Start new receiver
        from app.sms.receiver import SMSReceiver
        new_receiver = SMSReceiver(current_app._get_current_object())
        new_receiver.start_receiver()
        current_app.sms_receiver = new_receiver

        current_app.logger.info(f"✅ SMS receiver restarted with gateway: {gateway.ip_address}:{gateway.tg_sms_port}")

        return jsonify({
            'success': True,
            'message': f'SMS receiver restarted successfully',
            'gateway': {
                'name': gateway.name,
                'ip_address': gateway.ip_address,
                'tg_sms_port': gateway.tg_sms_port
            }
        })

    except Exception as e:
        current_app.logger.error(f"Restart receiver error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/export-messages', methods=['GET'])
@login_required
def export_messages():
    """Export messages to CSV"""
    try:
        # Check export permission
        if not current_user.has_permission('export_data'):
            return jsonify({'success': False, 'error': 'Permission denied'}), 403

        # Get filter parameters
        message_type = request.args.get('type', 'all')  # all, inbox, outbox
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # Build query
        query = SMSMessage.query

        # Filter by message type
        if message_type == 'inbox':
            query = query.filter_by(direction='inbound')
        elif message_type == 'outbox':
            query = query.filter_by(direction='outbound')

        # Filter by date range
        if date_from:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(SMSMessage.created_at >= date_from_obj)

        if date_to:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(SMSMessage.created_at <= date_to_obj)

        # Apply user permissions for non-admin users
        if not current_user.has_permission('manage_users'):
            if message_type in ['all', 'inbox']:
                # Filter received messages by assigned ports
                assigned_ports = current_user.get_assigned_ports()
                if assigned_ports:
                    port_ids = []
                    for assignment in assigned_ports:
                        gateway_id = assignment.get('gateway_id')
                        port_number = assignment.get('port')
                        if gateway_id and port_number:
                            port = SMSPort.query.filter_by(
                                gateway_id=gateway_id,
                                port_number=str(port_number),
                                is_active=True
                            ).first()
                            if port:
                                port_ids.append(port.id)

                    if message_type == 'inbox':
                        query = query.filter(SMSMessage.port_id.in_(port_ids))
                    else:  # all
                        # For 'all', show user's sent messages + received from assigned ports
                        query = query.filter(
                            db.or_(
                                db.and_(SMSMessage.direction == 'outbound', SMSMessage.user_id == current_user.id),
                                db.and_(SMSMessage.direction == 'inbound', SMSMessage.port_id.in_(port_ids))
                            )
                        )
                else:
                    # No assigned ports, only show sent messages
                    if message_type == 'inbox':
                        query = query.filter(SMSMessage.id == -1)  # No results
                    else:  # all
                        query = query.filter_by(direction='outbound', user_id=current_user.id)

            if message_type in ['all', 'outbox']:
                # Filter sent messages to user's own messages
                if message_type == 'outbox':
                    query = query.filter_by(user_id=current_user.id)

        # Get messages
        messages = query.order_by(SMSMessage.created_at.desc()).all()

        # Generate CSV data
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'Date/Time', 'Direction', 'Phone Number', 'Message', 'Status',
            'Gateway', 'Port', 'User', 'Message ID'
        ])

        # Write data
        for msg in messages:
            writer.writerow([
                msg.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                msg.direction.title(),
                msg.phone_number,
                msg.content,
                msg.status.title(),
                msg.gateway.name if msg.gateway else 'N/A',
                f"Port {msg.port.port_number}" if msg.port else 'N/A',
                msg.user.username if msg.user else 'System',
                msg.message_id
            ])

        csv_data = output.getvalue()
        output.close()

        # Generate filename
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"sms_messages_{message_type}_{timestamp}.csv"

        return jsonify({
            'success': True,
            'csv_data': csv_data,
            'filename': filename,
            'count': len(messages)
        })

    except Exception as e:
        current_app.logger.error(f"Export messages error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/delete-message/<int:message_id>', methods=['DELETE'])
@login_required
def delete_message(message_id):
    """Delete a single message"""
    try:
        # Check delete permission
        if not current_user.has_permission('delete_messages'):
            return jsonify({'success': False, 'error': 'Permission denied'}), 403

        # Get message
        message = SMSMessage.query.get_or_404(message_id)

        # Check if user can access this message
        if not current_user.has_permission('manage_users'):
            # Regular user can only delete their own sent messages or received messages from assigned ports
            if message.direction == 'outbound':
                if message.user_id != current_user.id:
                    return jsonify({'success': False, 'error': 'Access denied'}), 403
            elif message.direction == 'inbound':
                # Check if message is from user's assigned ports
                assigned_ports = current_user.get_assigned_ports()
                port_ids = []
                if assigned_ports:
                    for assignment in assigned_ports:
                        gateway_id = assignment.get('gateway_id')
                        port_number = assignment.get('port')
                        if gateway_id and port_number:
                            port = SMSPort.query.filter_by(
                                gateway_id=gateway_id,
                                port_number=str(port_number),
                                is_active=True
                            ).first()
                            if port:
                                port_ids.append(port.id)

                if not port_ids or message.port_id not in port_ids:
                    return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Delete message
        db.session.delete(message)
        db.session.commit()

        current_app.logger.info(f"Message {message_id} deleted by user {current_user.username}")

        return jsonify({
            'success': True,
            'message': 'Message deleted successfully'
        })

    except Exception as e:
        current_app.logger.error(f"Delete message error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/delete-messages', methods=['DELETE'])
@login_required
def delete_multiple_messages():
    """Delete multiple messages"""
    try:
        # Check delete permission
        if not current_user.has_permission('delete_messages'):
            return jsonify({'success': False, 'error': 'Permission denied'}), 403

        # Get message IDs from request
        data = request.get_json()
        message_ids = data.get('message_ids', [])

        if not message_ids:
            return jsonify({'success': False, 'error': 'No message IDs provided'}), 400

        # Get messages
        messages = SMSMessage.query.filter(SMSMessage.id.in_(message_ids)).all()

        if not messages:
            return jsonify({'success': False, 'error': 'No messages found'}), 404

        # Check permissions for each message
        allowed_messages = []
        for message in messages:
            if current_user.has_permission('manage_users'):
                # Admin can delete any message
                allowed_messages.append(message)
            else:
                # Regular user permission check
                if message.direction == 'outbound' and message.user_id == current_user.id:
                    allowed_messages.append(message)
                elif message.direction == 'inbound':
                    # Check if message is from user's assigned ports
                    assigned_ports = current_user.get_assigned_ports()
                    port_ids = []
                    if assigned_ports:
                        for assignment in assigned_ports:
                            gateway_id = assignment.get('gateway_id')
                            port_number = assignment.get('port')
                            if gateway_id and port_number:
                                port = SMSPort.query.filter_by(
                                    gateway_id=gateway_id,
                                    port_number=str(port_number),
                                    is_active=True
                                ).first()
                                if port:
                                    port_ids.append(port.id)

                    if port_ids and message.port_id in port_ids:
                        allowed_messages.append(message)

        if not allowed_messages:
            return jsonify({'success': False, 'error': 'No messages can be deleted (permission denied)'}), 403

        # Delete allowed messages
        deleted_count = 0
        for message in allowed_messages:
            db.session.delete(message)
            deleted_count += 1

        db.session.commit()

        current_app.logger.info(f"{deleted_count} messages deleted by user {current_user.username}")

        return jsonify({
            'success': True,
            'message': f'{deleted_count} message(s) deleted successfully',
            'deleted_count': deleted_count,
            'requested_count': len(message_ids)
        })

    except Exception as e:
        current_app.logger.error(f"Delete multiple messages error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
