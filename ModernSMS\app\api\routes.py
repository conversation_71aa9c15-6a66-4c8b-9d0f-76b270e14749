"""
API routes for external integrations and AJAX calls
"""
from flask import request, jsonify, current_app
from flask_login import login_required, current_user
from app.api import bp
from app.models import SMSMessage, GSMGateway, SMSPort, User
from app.sms.services import SMSService, GSMPortService
from app import db
from datetime import datetime, timedelta

@bp.route('/messages', methods=['GET'])
@login_required
def get_messages():
    """Get messages with pagination and filtering"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        direction = request.args.get('direction')
        phone_number = request.args.get('phone_number')
        status = request.args.get('status')
        
        # Build query
        query = SMSMessage.query
        
        # Apply filters
        if direction:
            query = query.filter_by(direction=direction)
        if phone_number:
            query = query.filter_by(phone_number=phone_number)
        if status:
            query = query.filter_by(status=status)
        
        # Apply user permissions
        if not current_user.has_permission('manage_users'):
            if direction == 'outbound':
                query = query.filter_by(user_id=current_user.id)
            elif direction == 'inbound':
                assigned_ports = current_user.get_assigned_ports()
                if assigned_ports:
                    port_ids = [p.get('port_id') for p in assigned_ports if p.get('port_id')]
                    if port_ids:
                        query = query.filter(SMSMessage.port_id.in_(port_ids))
        
        # Paginate
        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'success': True,
            'messages': [msg.to_dict() for msg in messages.items],
            'pagination': {
                'page': messages.page,
                'pages': messages.pages,
                'per_page': messages.per_page,
                'total': messages.total,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"API get messages error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/messages/send', methods=['POST'])
@login_required
def send_message():
    """Send SMS via API"""
    try:
        data = request.get_json()
        
        phone_number = data.get('phone_number', '').strip()
        message = data.get('message', '').strip()
        port_id = data.get('port_id', type=int)
        
        # Validate inputs
        if not phone_number:
            return jsonify({'success': False, 'error': 'Phone number is required'}), 400
        
        if not message:
            return jsonify({'success': False, 'error': 'Message is required'}), 400
        
        if not port_id:
            return jsonify({'success': False, 'error': 'Port ID is required'}), 400
        
        # Validate phone number
        if not SMSService.validate_phone_number(phone_number):
            return jsonify({'success': False, 'error': 'Invalid phone number format'}), 400
        
        # Get port and validate permissions
        port = SMSPort.query.get(port_id)
        if not port:
            return jsonify({'success': False, 'error': 'Invalid port ID'}), 400
        
        if not current_user.can_use_port(port.port_number, port.gateway_id):
            return jsonify({'success': False, 'error': 'Permission denied for this port'}), 403
        
        # Send SMS
        result = SMSService.send_sms(
            phone_number=phone_number,
            message=message,
            port=port,
            user=current_user
        )
        
        if result['success']:
            return jsonify({
                'success': True,
                'message_id': result['message_id'],
                'message': 'SMS sent successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"API send message error: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@bp.route('/gateways', methods=['GET'])
@login_required
def get_gateways():
    """Get gateways list"""
    try:
        if not current_user.has_permission('manage_gateways'):
            return jsonify({'success': False, 'error': 'Permission denied'}), 403
        
        gateways = GSMGateway.query.all()
        
        gateways_data = []
        for gw in gateways:
            gw_dict = gw.to_dict()
            # Add port summary
            gw_dict['port_summary'] = {'total': gw.max_ports}
            gateways_data.append(gw_dict)

        return jsonify({
            'success': True,
            'gateways': gateways_data
        })
        
    except Exception as e:
        current_app.logger.error(f"API get gateways error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/gateways/<int:gateway_id>/ports', methods=['GET'])
@login_required
def get_gateway_ports(gateway_id):
    """Get ports for specific gateway"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)
        ports = gateway.ports.all()
        
        return jsonify({
            'success': True,
            'gateway_id': gateway_id,
            'gateway_name': gateway.name,
            'ports': [port.to_dict() for port in ports]
        })
        
    except Exception as e:
        current_app.logger.error(f"API get gateway ports error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/ports/refresh', methods=['POST'])
@login_required
def refresh_all_ports():
    """Refresh all port statuses"""
    try:
        if not current_user.has_permission('manage_gateways'):
            return jsonify({'success': False, 'error': 'Permission denied'}), 403
        
        result = GSMPortService.refresh_all_ports()
        
        return jsonify(result)
        
    except Exception as e:
        current_app.logger.error(f"API refresh ports error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/stats', methods=['GET'])
@login_required
def get_stats():
    """Get system statistics"""
    try:
        from app.main.routes import get_dashboard_stats
        stats = get_dashboard_stats()
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        current_app.logger.error(f"API get stats error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/conversations', methods=['GET'])
@login_required
def get_conversations():
    """Get conversation list"""
    try:
        # Get unique phone numbers with message counts
        conversations = db.session.query(
            SMSMessage.phone_number,
            db.func.count(SMSMessage.id).label('message_count'),
            db.func.max(SMSMessage.created_at).label('last_message')
        ).group_by(SMSMessage.phone_number)\
         .order_by(db.func.max(SMSMessage.created_at).desc())\
         .limit(50).all()
        
        conversation_list = []
        for conv in conversations:
            # Get last message
            last_msg = SMSMessage.query.filter_by(phone_number=conv.phone_number)\
                                     .order_by(SMSMessage.created_at.desc()).first()
            
            conversation_list.append({
                'phone_number': conv.phone_number,
                'message_count': conv.message_count,
                'last_message_time': conv.last_message.isoformat() if conv.last_message else None,
                'last_message_content': last_msg.content[:50] + '...' if last_msg and len(last_msg.content) > 50 else last_msg.content if last_msg else '',
                'last_message_direction': last_msg.direction if last_msg else None
            })
        
        return jsonify({
            'success': True,
            'conversations': conversation_list
        })
        
    except Exception as e:
        current_app.logger.error(f"API get conversations error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/conversations/<phone_number>/messages', methods=['GET'])
@login_required
def get_conversation_messages(phone_number):
    """Get messages for specific conversation"""
    try:
        messages = SMSMessage.query.filter_by(phone_number=phone_number)\
                                  .order_by(SMSMessage.created_at.asc()).all()
        
        # Apply user permissions
        if not current_user.has_permission('manage_users'):
            filtered_messages = []
            assigned_ports = current_user.get_assigned_ports()
            port_ids = [p.get('port_id') for p in assigned_ports if p.get('port_id')]
            
            for msg in messages:
                if (msg.direction == 'outbound' and msg.user_id == current_user.id) or \
                   (msg.direction == 'inbound' and (not port_ids or msg.port_id in port_ids)):
                    filtered_messages.append(msg)
            
            messages = filtered_messages
        
        return jsonify({
            'success': True,
            'phone_number': phone_number,
            'messages': [msg.to_dict() for msg in messages]
        })
        
    except Exception as e:
        current_app.logger.error(f"API get conversation messages error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/simulate/receive-sms', methods=['POST'])
@login_required
def simulate_receive_sms():
    """Simulate receiving an SMS (development mode only)"""
    try:
        if not current_app.config.get('FLASK_ENV') == 'development':
            return jsonify({'success': False, 'error': 'Only available in development mode'}), 403

        data = request.get_json()
        phone_number = data.get('phone_number', '+1234567890')
        message = data.get('message', 'Test received message')
        port_id = data.get('port_id', 1)

        # Get port and gateway
        port = SMSPort.query.get(port_id)
        if not port:
            # Create a default port if none exists
            gateway = GSMGateway.query.first()
            if not gateway:
                return jsonify({'success': False, 'error': 'No gateway available'}), 400

            port = SMSPort.query.filter_by(gateway_id=gateway.id).first()
            if not port:
                return jsonify({'success': False, 'error': 'No port available'}), 400

        # Create received message
        import uuid
        message_id = str(uuid.uuid4())

        sms_message = SMSMessage(
            message_id=message_id,
            direction='inbound',
            phone_number=phone_number,
            content=message,
            status='received',
            gateway_id=port.gateway_id,
            port_id=port.id,
            created_at=datetime.now()
        )

        db.session.add(sms_message)
        db.session.commit()

        current_app.logger.info(f"DEV MODE: Simulated received SMS from {phone_number}: {message}")

        return jsonify({
            'success': True,
            'message': 'SMS received successfully (simulated)',
            'message_id': message_id
        })

    except Exception as e:
        current_app.logger.error(f"Simulate receive SMS error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
