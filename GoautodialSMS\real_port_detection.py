#!/usr/bin/env python3
"""
Real GSM Port Detection Methods
Shows different ways to detect actual GSM hardware and SIM card status
"""

import requests
import json
import time
import socket
from typing import Dict, List, Optional

class RealGSMPortDetector:
    """Real GSM hardware detection methods"""
    
    def __init__(self, gateway_ip: str, username: str = 'apiuser', password: str = 'apipass'):
        self.gateway_ip = gateway_ip
        self.username = username
        self.password = password
        self.base_url = f"http://{gateway_ip}"
    
    def method_1_http_api_detection(self) -> Dict:
        """
        Method 1: Use HTTP API to query GSM module status
        Most reliable method for Yeastar and similar GSM gateways
        """
        try:
            print(f"🔍 Method 1: HTTP API Detection for {self.gateway_ip}")
            
            # Common API endpoints for GSM status
            api_endpoints = [
                "/cgi/WebCGI?1500201=",  # GSM status query
                "/api/gsm/status",        # REST API endpoint
                "/cgi/status.cgi",        # Status CGI
                "/api/v1/gsm/ports",      # Modern API
            ]
            
            for endpoint in api_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}account={self.username}&password={self.password}"
                    print(f"   Trying: {url}")
                    
                    response = requests.get(url, timeout=10)
                    if response.status_code == 200:
                        print(f"   ✅ Success: {response.text[:100]}...")
                        
                        # Parse response for port information
                        ports_info = self.parse_gsm_status_response(response.text)
                        return {
                            'success': True,
                            'method': 'HTTP API',
                            'endpoint': endpoint,
                            'ports': ports_info
                        }
                        
                except requests.RequestException as e:
                    print(f"   ❌ Failed: {str(e)}")
                    continue
            
            return {'success': False, 'error': 'No working API endpoints found'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def method_2_sim_card_detection(self, port_number: int) -> Dict:
        """
        Method 2: Test SIM card presence by sending AT commands
        """
        try:
            print(f"🔍 Method 2: SIM Card Detection for Port {port_number}")
            
            # Try to send a test SMS query to detect SIM card
            test_url = (
                f"{self.base_url}/cgi/WebCGI?1500101="
                f"account={self.username}&password={self.password}&"
                f"port={port_number}&destination=**********&content=TEST"
            )
            
            response = requests.get(test_url, timeout=10)
            
            # Analyze response to determine SIM status
            if response.status_code == 200:
                response_text = response.text.lower()
                
                # Common error messages indicating no SIM card
                no_sim_indicators = [
                    'no sim card',
                    'sim not ready',
                    'sim card error',
                    'no network',
                    'sim failure',
                    'error: 302',  # Common Yeastar error for no SIM
                    'error: 303',  # Network registration failed
                ]
                
                has_sim = not any(indicator in response_text for indicator in no_sim_indicators)
                
                return {
                    'success': True,
                    'port': port_number,
                    'has_sim': has_sim,
                    'status': 'ready' if has_sim else 'no_sim',
                    'response': response_text,
                    'network': self.extract_network_info(response_text) if has_sim else 'None'
                }
            
            return {
                'success': False,
                'port': port_number,
                'error': f'HTTP {response.status_code}: {response.text}'
            }
            
        except Exception as e:
            return {
                'success': False,
                'port': port_number,
                'error': str(e)
            }
    
    def method_3_network_registration_check(self, port_number: int) -> Dict:
        """
        Method 3: Check network registration status
        """
        try:
            print(f"🔍 Method 3: Network Registration Check for Port {port_number}")
            
            # Use network status query API
            status_url = (
                f"{self.base_url}/cgi/WebCGI?1500202="
                f"account={self.username}&password={self.password}&port={port_number}"
            )
            
            response = requests.get(status_url, timeout=10)
            
            if response.status_code == 200:
                # Parse network registration status
                network_info = self.parse_network_status(response.text)
                
                return {
                    'success': True,
                    'port': port_number,
                    'network_registered': network_info.get('registered', False),
                    'signal_strength': network_info.get('signal', 'Unknown'),
                    'operator': network_info.get('operator', 'Unknown'),
                    'technology': network_info.get('technology', 'Unknown')
                }
            
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def method_4_ping_test(self) -> Dict:
        """
        Method 4: Simple ping test to check if gateway is reachable
        """
        try:
            print(f"🔍 Method 4: Ping Test for {self.gateway_ip}")
            
            import subprocess
            import platform
            
            # Use ping command based on OS
            ping_cmd = ['ping', '-n', '1'] if platform.system().lower() == 'windows' else ['ping', '-c', '1']
            ping_cmd.append(self.gateway_ip)
            
            result = subprocess.run(ping_cmd, capture_output=True, text=True, timeout=10)
            
            return {
                'success': result.returncode == 0,
                'reachable': result.returncode == 0,
                'response_time': self.extract_ping_time(result.stdout) if result.returncode == 0 else None
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def detect_all_ports(self, max_ports: int = 8) -> List[Dict]:
        """
        Comprehensive port detection using all methods
        """
        print(f"\n🚀 Comprehensive Port Detection for {self.gateway_ip}")
        print("=" * 60)
        
        results = []
        
        # First, check if gateway is reachable
        ping_result = self.method_4_ping_test()
        if not ping_result['success']:
            print(f"❌ Gateway {self.gateway_ip} is not reachable")
            return []
        
        print(f"✅ Gateway {self.gateway_ip} is reachable")
        
        # Try HTTP API detection first
        api_result = self.method_1_http_api_detection()
        if api_result['success']:
            print(f"✅ HTTP API detection successful")
            return api_result['ports']
        
        # Fallback to individual port testing
        print(f"⚠️ HTTP API failed, testing individual ports...")
        
        for port_num in range(1, max_ports + 1):
            print(f"\n📱 Testing Port {port_num}:")
            
            # Test SIM card presence
            sim_result = self.method_2_sim_card_detection(port_num)
            
            # Test network registration
            network_result = self.method_3_network_registration_check(port_num)
            
            # Combine results
            port_info = {
                'port_number': port_num,
                'has_sim': sim_result.get('has_sim', False),
                'status': sim_result.get('status', 'unknown'),
                'network': sim_result.get('network', 'Unknown'),
                'network_registered': network_result.get('network_registered', False),
                'signal_strength': network_result.get('signal_strength', 'Unknown'),
                'operator': network_result.get('operator', 'Unknown'),
                'is_active': sim_result.get('has_sim', False) and network_result.get('network_registered', False)
            }
            
            results.append(port_info)
            
            # Print results
            status_icon = "✅" if port_info['is_active'] else "❌"
            print(f"   {status_icon} Port {port_num}: {port_info['status']} - {port_info['network']}")
        
        return results
    
    def parse_gsm_status_response(self, response_text: str) -> List[Dict]:
        """Parse GSM status API response"""
        # This would need to be customized based on your gateway's API format
        # Example parsing logic:
        ports = []
        
        try:
            # Try JSON parsing first
            if response_text.strip().startswith('{'):
                data = json.loads(response_text)
                # Parse JSON structure based on your gateway's format
                
            # Try XML parsing
            elif '<' in response_text:
                # Parse XML response
                pass
                
            # Try plain text parsing
            else:
                # Parse plain text response
                lines = response_text.split('\n')
                for line in lines:
                    if 'port' in line.lower() and 'sim' in line.lower():
                        # Extract port information
                        pass
                        
        except Exception as e:
            print(f"Error parsing response: {e}")
        
        return ports
    
    def parse_network_status(self, response_text: str) -> Dict:
        """Parse network status response"""
        # Implement based on your gateway's response format
        return {
            'registered': 'registered' in response_text.lower(),
            'signal': 'unknown',
            'operator': 'unknown',
            'technology': 'unknown'
        }
    
    def extract_network_info(self, response_text: str) -> str:
        """Extract network operator information"""
        # Implement based on response format
        return "Unknown"
    
    def extract_ping_time(self, ping_output: str) -> Optional[float]:
        """Extract ping response time"""
        try:
            import re
            match = re.search(r'time[<=](\d+\.?\d*)ms', ping_output)
            return float(match.group(1)) if match else None
        except:
            return None

def test_real_detection():
    """Test real GSM detection"""
    print("🧪 Testing Real GSM Port Detection")
    print("=" * 50)
    
    # Test with your actual GSM gateway
    detector = RealGSMPortDetector('192.168.1.127')
    
    # Detect all ports
    ports = detector.detect_all_ports(max_ports=4)
    
    print(f"\n📊 Detection Results:")
    print(f"   Total Ports Tested: {len(ports)}")
    
    active_ports = [p for p in ports if p['is_active']]
    print(f"   Active Ports: {len(active_ports)}")
    
    for port in ports:
        status = "🟢 ACTIVE" if port['is_active'] else "🔴 INACTIVE"
        print(f"   Port {port['port_number']}: {status} - {port['status']} ({port['network']})")

if __name__ == "__main__":
    test_real_detection()
