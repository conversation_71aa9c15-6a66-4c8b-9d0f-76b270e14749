#!/usr/bin/env python3
"""
Debug TG SMS events and fix event subscription
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def test_event_subscription():
    """Test different event subscription methods"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔍 DEBUGGING TG SMS EVENT SUBSCRIPTION")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        if not gateway:
            print("❌ No gateway found!")
            return
        
        print(f"📡 Gateway: {gateway.ip_address}:{gateway.tg_sms_port}")
        
        try:
            # Connect
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Read initial
            initial = sock.recv(1024).decode()
            print(f"📥 Initial: {initial.strip()}")
            
            # Login
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            login_resp = sock.recv(1024).decode()
            print(f"📥 Login: {login_resp.strip()}")
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed!")
                return
            
            # Try different event subscription methods
            event_methods = [
                "Action: Events\r\nEventMask: sms\r\n\r\n",
                "Action: Events\r\nEventMask: all\r\n\r\n", 
                "Action: Events\r\nEventMask: message\r\n\r\n",
                "Action: Events\r\nEventMask: *\r\n\r\n"
            ]
            
            for i, event_cmd in enumerate(event_methods, 1):
                print(f"\n🧪 Testing event method {i}: {event_cmd.strip()}")
                
                sock.send(event_cmd.encode())
                event_resp = sock.recv(1024).decode()
                print(f"📥 Response: {event_resp.strip()}")
                
                if "Events: On" in event_resp or "Success" in event_resp:
                    print(f"✅ Method {i} might work!")
                    
                    # Listen for events for 10 seconds
                    print(f"👂 Listening for 10 seconds... Send SMS now!")
                    
                    sock.settimeout(1)
                    start_time = time.time()
                    
                    while time.time() - start_time < 10:
                        try:
                            data = sock.recv(4096).decode()
                            if data and "Event:" in data:
                                print(f"\n🎉 EVENT RECEIVED!")
                                print(f"📨 {data}")
                                break
                        except socket.timeout:
                            print(".", end="", flush=True)
                            continue
                    
                    print(f"\n⏰ Test {i} completed")
                    break
                else:
                    print(f"❌ Method {i} failed")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def check_gateway_sms_config():
    """Check if gateway has SMS events enabled"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔍 CHECKING GATEWAY SMS CONFIGURATION")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Read initial and login
            initial = sock.recv(1024).decode()
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            login_resp = sock.recv(1024).decode()
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed!")
                return
            
            # Check GSM status
            print("📱 Checking GSM spans...")
            gsm_cmd = "Action: Command\r\nCommand: gsm show spans\r\n\r\n"
            sock.send(gsm_cmd.encode())
            gsm_resp = sock.recv(2048).decode()
            print(f"📥 GSM Spans: {gsm_resp}")
            
            # Check SMS configuration
            print("\n📱 Checking SMS configuration...")
            sms_cmd = "Action: Command\r\nCommand: sms show config\r\n\r\n"
            sock.send(sms_cmd.encode())
            sms_resp = sock.recv(2048).decode()
            print(f"📥 SMS Config: {sms_resp}")
            
            # Check if there are any active channels
            print("\n📱 Checking active channels...")
            chan_cmd = "Action: Command\r\nCommand: core show channels\r\n\r\n"
            sock.send(chan_cmd.encode())
            chan_resp = sock.recv(2048).decode()
            print(f"📥 Channels: {chan_resp}")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def test_manual_sms_simulation():
    """Test manual SMS simulation"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🧪 TESTING MANUAL SMS SIMULATION")
        print("="*60)
        
        try:
            from app.sms.receiver import SMSReceiver
            
            # Create receiver instance
            receiver = SMSReceiver(app)
            
            # Create a test SMS event in the format we expect
            test_event = """Event: ReceivedSMS
Sender: +639123456789
Content: Test%20message%20from%20manual%20simulation
GsmPort: 1
ID: test123"""
            
            print(f"📨 Simulating SMS event:")
            print(f"   {test_event}")
            
            # Process the event
            receiver._process_received_sms(test_event)
            
            print("✅ Manual SMS simulation completed")
            
            # Check if message was saved
            from app.models import SMSMessage
            test_msg = SMSMessage.query.filter_by(message_id='test123').first()
            
            if test_msg:
                print(f"✅ Message saved to database!")
                print(f"   From: {test_msg.phone_number}")
                print(f"   Content: {test_msg.content}")
            else:
                print("❌ Message not found in database")
                
        except Exception as e:
            print(f"❌ Manual simulation failed: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    print("🚀 TG SMS Event Debugging Tool")
    
    # Test event subscription
    test_event_subscription()
    
    # Check gateway configuration
    check_gateway_sms_config()
    
    # Test manual simulation
    test_manual_sms_simulation()
    
    print("\n" + "="*60)
    print("🎯 NEXT STEPS:")
    print("="*60)
    print("1. Check if SMS events are enabled in gateway web interface")
    print("2. Verify GSM spans are active and have SIM cards")
    print("3. Try sending SMS during the event listening test")
    print("4. Check gateway documentation for SMS event configuration")
    print("="*60)
