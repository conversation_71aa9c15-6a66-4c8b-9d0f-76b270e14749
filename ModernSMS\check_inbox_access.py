#!/usr/bin/env python3
"""
Check inbox access and user permissions
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, SMSMessage, SMSPort, GSMGateway

def check_user_inbox_access():
    """Check user inbox access and permissions"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔍 INBOX ACCESS DIAGNOSTICS")
        print("="*60)
        
        # Get admin user
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("❌ Admin user not found!")
            return
        
        print(f"👤 User: {admin_user.username}")
        print(f"   Email: {admin_user.email}")
        print(f"   Role: {admin_user.role.name if admin_user.role else 'No role'}")
        print(f"   Can manage users: {admin_user.can_manage_users}")
        print(f"   Can view inbox: {admin_user.can_view_inbox}")
        print(f"   Can export data: {admin_user.can_export_data}")
        print(f"   Can delete messages: {admin_user.can_delete_messages}")
        
        # Check assigned ports
        assigned_ports = admin_user.get_assigned_ports()
        print(f"\n📱 Assigned Ports: {assigned_ports}")
        
        # Get all received messages
        all_received = SMSMessage.query.filter_by(direction='inbound').all()
        print(f"\n📥 All Received Messages: {len(all_received)}")
        
        for msg in all_received:
            print(f"   - ID {msg.id}: From {msg.phone_number} via Port {msg.port.port_number if msg.port else 'Unknown'} ({msg.created_at})")
        
        # Check what messages admin user should see
        print(f"\n🔍 Messages Admin User Should See:")
        
        if admin_user.can_manage_users:
            print("   ✅ Admin can see ALL messages (manage_users permission)")
            visible_messages = all_received
        else:
            print("   ⚠️  Admin is restricted by port assignments")
            if assigned_ports:
                port_ids = []
                for assignment in assigned_ports:
                    gateway_id = assignment.get('gateway_id')
                    port_number = assignment.get('port')
                    if gateway_id and port_number:
                        port = SMSPort.query.filter_by(
                            gateway_id=gateway_id,
                            port_number=str(port_number),
                            is_active=True
                        ).first()
                        if port:
                            port_ids.append(port.id)
                            print(f"     - Port {port.port_number} (ID: {port.id})")
                
                visible_messages = [msg for msg in all_received if msg.port_id in port_ids]
            else:
                print("     - No assigned ports = No visible messages")
                visible_messages = []
        
        print(f"\n✅ Visible Messages for Admin: {len(visible_messages)}")
        for msg in visible_messages:
            print(f"   - From {msg.phone_number}: {msg.content[:50]}...")
        
        # Check ports and their assignments
        print(f"\n📱 Available Ports:")
        all_ports = SMSPort.query.all()
        for port in all_ports:
            print(f"   - Port {port.port_number} (Gateway: {port.gateway.name}, Active: {port.is_active})")
        
        # Check if messages have proper port assignments
        print(f"\n🔗 Message Port Assignments:")
        messages_without_ports = SMSMessage.query.filter_by(direction='inbound', port_id=None).count()
        print(f"   - Messages without port assignment: {messages_without_ports}")
        
        if messages_without_ports > 0:
            print("   ⚠️  Some messages don't have port assignments!")
            print("   This could cause them to not appear in user inboxes")

def fix_admin_permissions():
    """Fix admin user permissions"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔧 FIXING ADMIN PERMISSIONS:")
        print("="*60)
        
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("❌ Admin user not found!")
            return
        
        # Ensure admin has all permissions
        admin_user.can_manage_users = True
        admin_user.can_view_inbox = True
        admin_user.can_view_outbox = True
        admin_user.can_export_data = True
        admin_user.can_delete_messages = True
        admin_user.can_manage_gateways = True
        admin_user.can_view_reports = True
        
        db.session.commit()
        
        print("✅ Admin user permissions updated:")
        print(f"   - Can manage users: {admin_user.can_manage_users}")
        print(f"   - Can view inbox: {admin_user.can_view_inbox}")
        print(f"   - Can export data: {admin_user.can_export_data}")
        print(f"   - Can delete messages: {admin_user.can_delete_messages}")

if __name__ == '__main__':
    check_user_inbox_access()
    fix_admin_permissions()
    
    print("\n" + "="*60)
    print("🎯 SUMMARY:")
    print("="*60)
    print("✅ SMS receiving is working - 9 messages found in database")
    print("✅ Admin permissions have been updated")
    print("✅ Try refreshing the inbox page in your browser")
    print("✅ All received messages should now be visible")
    print("="*60)
