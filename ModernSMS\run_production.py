#!/usr/bin/env python3
"""
Run Modern SMS Manager in PRODUCTION mode
"""
import os

# Force production mode
os.environ['FLASK_ENV'] = 'production'

# Import and run the app
from app import create_app

if __name__ == '__main__':
    app = create_app()
    print("="*60)
    print("🚀 MODERN SMS MANAGER - PRODUCTION MODE")
    print("="*60)
    print("⚠️  PRODUCTION MODE ENABLED")
    print("📡 SMS will be sent to REAL GSM hardware")
    print("🌐 Web interface: http://127.0.0.1:5000")
    print("👤 Login: admin / admin123")
    print("="*60)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,  # Disable debug in production
        threaded=True
    )
