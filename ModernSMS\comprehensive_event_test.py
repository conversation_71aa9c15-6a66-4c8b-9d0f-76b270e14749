#!/usr/bin/env python3
"""
Comprehensive test of all event subscription methods
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def test_all_event_methods():
    """Test every possible event subscription method"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🧪 COMPREHENSIVE EVENT SUBSCRIPTION TEST")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        # All possible event subscription methods to try
        event_methods = [
            ("Method 1", "Action: Events\r\n\r\n"),
            ("Method 2", "Action: Events\r\nEventMask: all\r\n\r\n"),
            ("Method 3", "Action: Events\r\nEventMask: sms\r\n\r\n"),
            ("Method 4", "Action: Events\r\nEventMask: *\r\n\r\n"),
            ("Method 5", "Action: Events\r\nEventMask: message\r\n\r\n"),
            ("Method 6", "Action: Events\r\nEventMask: call,sms\r\n\r\n"),
            ("Method 7", "Action: Events\r\nEvents: on\r\n\r\n"),
            ("Method 8", "Action: Events\r\nEventMask: all\r\nEvents: on\r\n\r\n"),
        ]
        
        for method_name, event_cmd in event_methods:
            print(f"\n🔧 {method_name}: {event_cmd.strip()}")
            
            try:
                # Fresh connection for each test
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                sock.connect((gateway.ip_address, gateway.tg_sms_port))
                
                # Login
                initial = sock.recv(1024).decode()
                login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
                sock.send(login_cmd.encode())
                login_resp = sock.recv(1024).decode()
                
                if "Response: Success" not in login_resp:
                    print(f"   ❌ Login failed")
                    sock.close()
                    continue
                
                # Try the event subscription method
                sock.send(event_cmd.encode())
                event_resp = sock.recv(1024).decode()
                print(f"   📥 Response: {event_resp.strip()}")
                
                if "Events: On" in event_resp:
                    print(f"   🎉 SUCCESS! {method_name} enabled events!")
                    
                    # Test by listening for a few seconds
                    print(f"   👂 Testing event reception for 5 seconds...")
                    sock.settimeout(1)
                    start_time = time.time()
                    
                    while time.time() - start_time < 5:
                        try:
                            data = sock.recv(1024).decode()
                            if data and "Event:" in data:
                                print(f"   📨 Event received: {data.strip()}")
                        except socket.timeout:
                            print(".", end="", flush=True)
                            continue
                    
                    print(f"\n   ✅ {method_name} is working!")
                    sock.close()
                    return method_name, event_cmd
                elif "Success" in event_resp and "Events: Off" not in event_resp:
                    print(f"   ⚠️  Success but unclear event status")
                else:
                    print(f"   ❌ {method_name} failed")
                
                sock.close()
                
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        return None, None

def test_sms_commands():
    """Test if SMS commands work (alternative approach)"""
    app = create_app()
    
    with app.app_context():
        print(f"\n📱 TESTING SMS COMMANDS")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Login
            initial = sock.recv(1024).decode()
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            login_resp = sock.recv(1024).decode()
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed")
                return False
            
            # Test SMS commands
            print("📱 Testing GSM spans...")
            gsm_cmd = "Action: smscommand\r\ncommand: gsm show spans\r\n\r\n"
            sock.send(gsm_cmd.encode())
            gsm_resp = sock.recv(2048).decode()
            print(f"📥 GSM Response: {gsm_resp}")
            
            if "GSM span" in gsm_resp or "Power on" in gsm_resp:
                print("✅ SMS commands are working!")
                print("💡 This means TG SMS API is functional")
                sock.close()
                return True
            else:
                print("❌ SMS commands not working")
                sock.close()
                return False
            
        except Exception as e:
            print(f"❌ Error testing SMS commands: {str(e)}")
            return False

def try_manual_event_enable():
    """Try to manually enable events using TG SMS specific commands"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔧 TRYING MANUAL EVENT ENABLE")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        # TG SMS specific commands to try
        enable_commands = [
            "Action: smscommand\r\ncommand: events enable\r\n\r\n",
            "Action: smscommand\r\ncommand: set events on\r\n\r\n",
            "Action: Command\r\nCommand: events set on\r\n\r\n",
            "Action: Command\r\nCommand: manager set events on\r\n\r\n"
        ]
        
        for i, cmd in enumerate(enable_commands, 1):
            print(f"\n🧪 Command {i}: {cmd.strip()}")
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((gateway.ip_address, gateway.tg_sms_port))
                
                # Login
                initial = sock.recv(1024).decode()
                login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
                sock.send(login_cmd.encode())
                login_resp = sock.recv(1024).decode()
                
                if "Response: Success" not in login_resp:
                    print(f"   ❌ Login failed")
                    sock.close()
                    continue
                
                # Try the enable command
                sock.send(cmd.encode())
                resp = sock.recv(1024).decode()
                print(f"   📥 Response: {resp.strip()}")
                
                # Now try event subscription
                event_cmd = "Action: Events\r\n\r\n"
                sock.send(event_cmd.encode())
                event_resp = sock.recv(1024).decode()
                print(f"   📥 Events after command: {event_resp.strip()}")
                
                if "Events: On" in event_resp:
                    print(f"   🎉 Command {i} worked!")
                    sock.close()
                    return True
                
                sock.close()
                
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        return False

if __name__ == '__main__':
    print("🚀 Comprehensive Event Subscription Test")
    print("🎯 Finding ANY method that enables SMS events")
    
    # Test all event methods
    working_method, working_cmd = test_all_event_methods()
    
    if working_method:
        print(f"\n🎉 FOUND WORKING METHOD: {working_method}")
        print(f"📝 Command: {working_cmd.strip()}")
        print(f"💡 Will update SMS receiver to use this method")
    else:
        print(f"\n❌ No event subscription method worked")
        
        # Test SMS commands as alternative
        sms_commands_work = test_sms_commands()
        
        if sms_commands_work:
            print(f"\n✅ SMS commands work - API is functional")
            
            # Try manual event enable
            manual_success = try_manual_event_enable()
            
            if manual_success:
                print(f"\n🎉 Manual event enable worked!")
            else:
                print(f"\n❌ Could not enable events manually")
        else:
            print(f"\n❌ SMS commands also don't work")
            print(f"💡 Check TG SMS API configuration")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"1. Verify TG SMS service is running on gateway")
    print(f"2. Check if gateway needs restart after API changes")
    print(f"3. Verify user permissions in TG SMS settings")
    print(f"4. Try restarting TG SMS service on gateway")
    
    print("\n" + "="*60)
