#!/usr/bin/env python3
"""
Focused SMS Receiving Test
Tests only the receiving functionality to identify the exact issue
"""

import socket
import urllib.parse
import time
import threading

# Configuration
YEASTAR_IP = '*************'
YEASTAR_PORT = 5038
USERNAME = 'apiuser'
PASSWORD = 'apipass'

def test_port_5038():
    """Test if port 5038 is accessible"""
    print("🔌 Testing port 5038 accessibility...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((YEASTAR_IP, YEASTAR_PORT))
        sock.close()
        
        if result == 0:
            print(f"✅ Port 5038 is OPEN on {YEASTAR_IP}")
            return True
        else:
            print(f"❌ Port 5038 is CLOSED on {YEASTAR_IP}")
            print("   This means TG SMS service is not running or not listening on port 5038")
            return False
    except Exception as e:
        print(f"❌ Error testing port 5038: {e}")
        return False

def test_raw_connection():
    """Test raw connection to see what happens"""
    print("🔍 Testing raw connection to TG SMS server...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        print(f"Connecting to {YEASTAR_IP}:{YEASTAR_PORT}...")
        sock.connect((YEASTAR_IP, YEASTAR_PORT))
        print("✅ Connected successfully!")
        
        # Check for any welcome message
        print("Waiting for welcome message...")
        sock.settimeout(3)
        try:
            welcome = sock.recv(1024).decode(errors='ignore')
            if welcome:
                print(f"📨 Welcome message: {repr(welcome)}")
            else:
                print("📭 No welcome message")
        except socket.timeout:
            print("📭 No welcome message (timeout)")
        
        # Send login command
        print("Sending login command...")
        login_cmd = f"Action: Login\r\nUsername: {USERNAME}\r\nSecret: {PASSWORD}\r\n\r\n"
        print(f"Command: {repr(login_cmd)}")
        
        sock.send(login_cmd.encode())
        
        # Wait for response
        print("Waiting for login response...")
        sock.settimeout(10)
        response_data = b""
        
        while True:
            try:
                chunk = sock.recv(1024)
                if not chunk:
                    break
                response_data += chunk
                print(f"📨 Received chunk: {repr(chunk)}")
                
                # Check if we have a complete response
                if b"Response:" in response_data or b"--END" in response_data:
                    break
                    
            except socket.timeout:
                print("⏰ Timeout waiting for response")
                break
        
        response = response_data.decode(errors='ignore')
        print(f"📨 Full response: {repr(response)}")
        
        if "Response: Success" in response:
            print("✅ Login successful!")
            return sock, True
        else:
            print("❌ Login failed!")
            print(f"Response was: {response}")
            return sock, False
            
    except socket.timeout:
        print("❌ Connection timeout")
        return None, False
    except ConnectionRefusedError:
        print("❌ Connection refused - TG SMS service not running")
        return None, False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None, False

def test_sms_listening(sock):
    """Test SMS event listening"""
    print("📱 Testing SMS event listening...")
    print("Now send an SMS to your GSM number to test receiving...")
    print("Waiting for SMS events (press Ctrl+C to stop)...")
    print("-" * 50)
    
    buffer = ""
    
    try:
        while True:
            # Set a reasonable timeout
            sock.settimeout(1.0)
            
            try:
                data = sock.recv(4096).decode(errors='ignore')
                if not data:
                    print("❌ Connection closed by server")
                    break
                
                buffer += data
                print(f"📨 Raw data received: {repr(data)}")
                
                # Process complete events
                while "--END SMS EVENT--" in buffer:
                    event, buffer = buffer.split("--END SMS EVENT--", 1)
                    print(f"📋 Complete event received: {repr(event)}")
                    
                    if "Event: ReceivedSMS" in event:
                        print("🎉 SMS EVENT FOUND!")
                        parse_and_display_sms(event)
                    else:
                        print(f"📄 Other event: {event[:100]}...")
                
            except socket.timeout:
                # Normal timeout, continue listening
                print(".", end="", flush=True)
                continue
                
    except KeyboardInterrupt:
        print("\n🛑 Stopped by user")
    except Exception as e:
        print(f"❌ Error during listening: {e}")

def parse_and_display_sms(event):
    """Parse and display SMS event"""
    print("\n" + "="*50)
    print("📨 SMS RECEIVED!")
    print("="*50)
    
    lines = event.strip().splitlines()
    sms_info = {}
    
    for line in lines:
        if ": " in line:
            key, val = line.split(": ", 1)
            sms_info[key.strip()] = val.strip()
    
    print("Raw SMS data:")
    for key, value in sms_info.items():
        print(f"  {key}: {value}")
    
    # Extract and decode content
    sender = sms_info.get('Sender', 'Unknown')
    content_encoded = sms_info.get('Content', '')
    content = urllib.parse.unquote(content_encoded)
    recvtime = sms_info.get('Recvtime', 'Unknown')
    
    print(f"\nDecoded SMS:")
    print(f"  From: {sender}")
    print(f"  Time: {recvtime}")
    print(f"  Message: {content}")
    print("="*50)

def check_yeastar_config():
    """Check Yeastar configuration requirements"""
    print("📋 Yeastar Configuration Checklist:")
    print("   Please verify these settings on your Yeastar system:")
    print()
    print("1. TG SMS Service:")
    print("   ✓ Go to Yeastar web interface")
    print("   ✓ Check if TG SMS service is enabled and running")
    print("   ✓ Verify it's listening on port 5038")
    print()
    print("2. API User:")
    print("   ✓ User 'apiuser' exists")
    print("   ✓ Password is 'apipass'")
    print("   ✓ User has SMS permissions")
    print()
    print("3. SMS Events:")
    print("   ✓ SMS event notifications are enabled")
    print("   ✓ Events are sent to connected clients")
    print()
    print("4. GSM Module:")
    print("   ✓ GSM module is working")
    print("   ✓ SIM card is inserted and active")
    print("   ✓ Can receive SMS manually via Yeastar interface")
    print()

def main():
    """Main test function"""
    print("SMS Receiving Test")
    print("=" * 30)
    print(f"Target: {YEASTAR_IP}:{YEASTAR_PORT}")
    print(f"Credentials: {USERNAME} / {'*' * len(PASSWORD)}")
    print()
    
    # Step 1: Test port accessibility
    if not test_port_5038():
        print("\n❌ CRITICAL: Port 5038 is not accessible!")
        print("   TG SMS service is not running on Yeastar")
        check_yeastar_config()
        return
    
    print()
    
    # Step 2: Test connection and login
    sock, login_success = test_raw_connection()
    
    if not sock:
        print("\n❌ CRITICAL: Cannot connect to TG SMS server!")
        check_yeastar_config()
        return
    
    if not login_success:
        print("\n❌ CRITICAL: Login failed!")
        print("   Check username/password and user permissions")
        sock.close()
        return
    
    print()
    
    # Step 3: Test SMS listening
    try:
        test_sms_listening(sock)
    finally:
        sock.close()

if __name__ == "__main__":
    main()
