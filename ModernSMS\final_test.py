#!/usr/bin/env python3
"""
Final test after adding IP to allowed list
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def final_sms_test():
    """Final test to verify SMS receiving is working"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🎯 FINAL SMS RECEIVING TEST")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        print(f"📡 Gateway: {gateway.ip_address}:{gateway.tg_sms_port}")
        print(f"🔐 Credentials: {gateway.username}/{gateway.password}")
        
        try:
            # Connect
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            local_ip = sock.getsockname()[0]
            print(f"📍 Connecting from: {local_ip}")
            
            # Login
            initial = sock.recv(1024).decode()
            print(f"📥 Initial: {initial.strip()}")
            
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            login_resp = sock.recv(1024).decode()
            print(f"📥 Login: {login_resp.strip()}")
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed!")
                return False
            
            print("✅ Login successful!")
            
            # Test event subscription (method that worked before)
            print(f"\n📡 Testing event subscription...")
            event_cmd = "Action: Events\r\n\r\n"
            sock.send(event_cmd.encode())
            
            event_resp = sock.recv(1024).decode()
            print(f"📥 Event response: {event_resp.strip()}")
            
            if "Events: On" in event_resp:
                print("🎉 EVENTS ARE ENABLED! SMS receiving should work!")
                
                # Listen for SMS events
                print(f"\n👂 Listening for SMS events for 20 seconds...")
                print(f"📱 SEND AN SMS TO YOUR GATEWAY NUMBER NOW!")
                
                sock.settimeout(1)
                start_time = time.time()
                event_count = 0
                
                while time.time() - start_time < 20:
                    try:
                        data = sock.recv(4096).decode()
                        if data and "Event:" in data:
                            event_count += 1
                            print(f"\n📨 SMS EVENT {event_count} RECEIVED!")
                            print(f"📄 Event data:")
                            print(f"   {data}")
                            
                            if "ReceivedSMS" in data:
                                print(f"🎉 SMS RECEIVING IS WORKING!")
                                
                                # Parse SMS details
                                lines = data.split('\n')
                                sender = None
                                content = None
                                port = None
                                
                                for line in lines:
                                    if 'Sender:' in line:
                                        sender = line.split(':', 1)[1].strip()
                                    elif 'Content:' in line:
                                        content = line.split(':', 1)[1].strip()
                                    elif 'GsmPort:' in line:
                                        port = line.split(':', 1)[1].strip()
                                
                                print(f"📞 From: {sender}")
                                print(f"💬 Message: {content}")
                                print(f"📱 Port: {port}")
                                
                                sock.close()
                                return True
                                
                    except socket.timeout:
                        print(".", end="", flush=True)
                        continue
                    except Exception as e:
                        print(f"\n❌ Error reading events: {str(e)}")
                        break
                
                print(f"\n📊 Total events received: {event_count}")
                
                if event_count == 0:
                    print(f"⚠️  No SMS events received during test")
                    print(f"💡 Try sending an SMS and run this test again")
                
                sock.close()
                return event_count > 0
                
            else:
                print(f"❌ Events still not enabled")
                print(f"💡 Check TG SMS API settings:")
                print(f"   1. API is enabled")
                print(f"   2. IP {local_ip} is in allowed list")
                print(f"   3. Settings are saved and applied")
                sock.close()
                return False
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False

def restart_sms_receiver_final():
    """Restart SMS receiver for final test"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔄 FINAL SMS RECEIVER RESTART")
        print("="*60)
        
        try:
            from app.sms.receiver import SMSReceiver
            
            # Stop existing
            receiver = getattr(app, 'sms_receiver', None)
            if receiver:
                receiver.stop_receiver()
                time.sleep(3)
            
            # Start new
            new_receiver = SMSReceiver(app)
            new_receiver.start_receiver()
            app.sms_receiver = new_receiver
            
            time.sleep(5)
            
            print(f"📊 SMS Receiver Status:")
            print(f"   Running: {'✅' if new_receiver.running else '❌'}")
            print(f"   Connected: {'✅' if new_receiver.connected else '❌'}")
            
            if new_receiver.running and new_receiver.connected:
                print(f"✅ SMS receiver is ready!")
                return True
            else:
                print(f"❌ SMS receiver has issues")
                return False
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False

if __name__ == '__main__':
    print("🚀 Final SMS Receiving Test")
    print("📋 Run this AFTER adding your IP to TG SMS allowed list")
    
    # Test events
    events_working = final_sms_test()
    
    if events_working:
        print(f"\n🎉 SMS EVENTS ARE WORKING!")
        
        # Restart receiver
        receiver_ok = restart_sms_receiver_final()
        
        if receiver_ok:
            print(f"\n🎉 SUCCESS! SMS RECEIVING IS FULLY OPERATIONAL!")
            print(f"📱 Send an SMS to test receiving")
            print(f"🌐 Check your inbox in the web interface")
        else:
            print(f"\n⚠️  Events work but receiver needs attention")
    else:
        print(f"\n❌ SMS events still not working")
        print(f"💡 Add your IP address to TG SMS allowed list:")
        print(f"   IP to add: *************")
        print(f"   Or use: 0.0.0.0/0 for testing")
    
    print("\n" + "="*60)
