#!/usr/bin/env python3
"""
Test login functionality directly without HTTP
"""

import sys
import os
sys.path.append('backend')

from app import app, db, User
from flask_login import login_user, current_user

def test_direct_login():
    with app.app_context():
        print("🧪 Testing direct login functionality...")
        
        # Test admin user
        print("\n1️⃣ Testing admin user:")
        admin = User.query.filter_by(username='admin').first()
        if admin:
            print(f"   User found: {admin.username}")
            print(f"   Active: {admin.is_active}")
            print(f"   Password check: {admin.check_password('admin123')}")
            print(f"   Role: {admin.role}")
            
            # Test login_user function
            try:
                with app.test_request_context():
                    login_user(admin)
                    print(f"   login_user() successful: {current_user.is_authenticated}")
                    print(f"   Current user: {current_user.username}")
            except Exception as e:
                print(f"   login_user() error: {e}")
        
        # Test test1 user
        print("\n2️⃣ Testing test1 user:")
        test1 = User.query.filter_by(username='test1').first()
        if test1:
            print(f"   User found: {test1.username}")
            print(f"   Active: {test1.is_active}")
            print(f"   Password check: {test1.check_password('test123')}")
            print(f"   Role: {test1.role}")
            print(f"   Assigned ports: {test1.get_assigned_ports()}")
            
            # Test permissions
            print(f"   Permissions:")
            print(f"     can_send_sms: {test1.can_send_sms}")
            print(f"     can_view_inbox: {test1.can_view_inbox}")
            print(f"     can_view_outbox: {test1.can_view_outbox}")
            print(f"     has_permission('can_send_sms'): {test1.has_permission('can_send_sms')}")
            print(f"     has_permission('can_view_inbox'): {test1.has_permission('can_view_inbox')}")
            
            # Test login_user function
            try:
                with app.test_request_context():
                    login_user(test1)
                    print(f"   login_user() successful: {current_user.is_authenticated}")
                    print(f"   Current user: {current_user.username}")
            except Exception as e:
                print(f"   login_user() error: {e}")
        
        # Test the exact login logic from the route
        print("\n3️⃣ Testing exact login logic:")
        username = 'test1'
        password = 'test123'
        
        user = User.query.filter_by(username=username).first()
        if user:
            print(f"   User found: {user.username}")
            password_valid = user.check_password(password)
            print(f"   Password valid: {password_valid}")
            print(f"   User active: {user.is_active}")
            
            if password_valid and user.is_active:
                print(f"   ✅ Login should succeed!")
                try:
                    with app.test_request_context():
                        login_user(user)
                        print(f"   login_user() worked: {current_user.is_authenticated}")
                except Exception as e:
                    print(f"   ❌ login_user() failed: {e}")
            else:
                print(f"   ❌ Login should fail!")
                if not password_valid:
                    print(f"     Reason: Invalid password")
                if not user.is_active:
                    print(f"     Reason: User not active")

if __name__ == "__main__":
    test_direct_login()
