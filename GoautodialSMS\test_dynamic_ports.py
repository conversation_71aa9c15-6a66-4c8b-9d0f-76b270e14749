#!/usr/bin/env python3
"""
Test dynamic port management system
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSPort, GSMPortManager, SMS_API_CONFIG

def test_dynamic_ports():
    with app.app_context():
        print("🔌 Testing Dynamic Port Management System")
        print("=" * 50)
        
        # Test 1: Show current configuration
        print(f"\n1️⃣ Current Configuration:")
        print(f"   Max Ports: {SMS_API_CONFIG.get('max_ports', 8)}")
        
        current_ports = SMSPort.query.all()
        print(f"   Current Ports in DB: {len(current_ports)}")
        for port in current_ports:
            print(f"     Port {port.port_number}: {port.status} (Active: {port.is_active})")
        
        # Test 2: Test port detection with different configurations
        print(f"\n2️⃣ Testing Port Detection:")
        
        # Test with 8 ports (default)
        print(f"   Testing with 8 ports (default):")
        SMS_API_CONFIG['max_ports'] = 8
        detected_8 = GSMPortManager.detect_available_ports()
        print(f"     Detected: {detected_8}")
        
        # Test with 16 ports
        print(f"   Testing with 16 ports:")
        SMS_API_CONFIG['max_ports'] = 16
        detected_16 = GSMPortManager.detect_available_ports()
        print(f"     Detected: {detected_16}")
        
        # Test with 4 ports (smaller system)
        print(f"   Testing with 4 ports:")
        SMS_API_CONFIG['max_ports'] = 4
        detected_4 = GSMPortManager.detect_available_ports()
        print(f"     Detected: {detected_4}")
        
        # Test 3: Initialize ports with different configurations
        print(f"\n3️⃣ Testing Port Initialization:")
        
        # Initialize with 16 ports
        print(f"   Initializing with 16 ports:")
        SMS_API_CONFIG['max_ports'] = 16
        initialized_ports = GSMPortManager.initialize_ports()
        print(f"     Initialized: {initialized_ports}")
        
        # Check database
        all_ports = SMSPort.query.order_by(SMSPort.port_number.asc()).all()
        active_ports = SMSPort.query.filter_by(is_active=True).order_by(SMSPort.port_number.asc()).all()
        
        print(f"   Database Status:")
        print(f"     Total ports in DB: {len(all_ports)}")
        print(f"     Active ports: {len(active_ports)}")
        print(f"     Active port numbers: {[p.port_number for p in active_ports]}")
        
        # Test 4: Test port activation/deactivation
        print(f"\n4️⃣ Testing Port Management:")
        
        # Get active ports
        active_port_mgr = GSMPortManager.get_active_ports()
        print(f"   Active ports from manager: {active_port_mgr}")
        
        # Test port status update
        if active_ports:
            test_port = active_ports[0]
            print(f"   Testing status update for port {test_port.port_number}")
            
            status_data = {
                'status': 'online',
                'network_name': 'Test Network',
                'signal_quality': '85%',
                'sim_imsi': '123456789012345'
            }
            
            GSMPortManager.update_port_status(test_port.port_number, status_data)
            
            # Check updated port
            updated_port = SMSPort.query.filter_by(port_number=test_port.port_number).first()
            print(f"     Updated status: {updated_port.status}")
            print(f"     Network: {updated_port.network_name}")
            print(f"     Signal: {updated_port.signal_quality}")
        
        # Test 5: Demonstrate scalability
        print(f"\n5️⃣ Demonstrating Scalability:")
        
        configurations = [4, 8, 16, 32]
        for max_ports in configurations:
            SMS_API_CONFIG['max_ports'] = max_ports
            detected = GSMPortManager.detect_available_ports()
            print(f"   {max_ports} ports configured → {len(detected)} ports detected: {detected[:5]}{'...' if len(detected) > 5 else ''}")
        
        # Reset to default
        SMS_API_CONFIG['max_ports'] = 8
        
        print(f"\n✅ Dynamic Port Management Test Complete!")
        print(f"   The system can dynamically handle 1-32 GSM ports")
        print(f"   Port detection adapts to hardware configuration")
        print(f"   Database automatically updates with detected ports")

def test_port_filtering_with_16_ports():
    """Test port filtering with 16 ports"""
    with app.app_context():
        print(f"\n🔒 Testing Port Filtering with 16 Ports")
        print("=" * 40)
        
        # Set up 16 ports
        SMS_API_CONFIG['max_ports'] = 16
        GSMPortManager.initialize_ports()
        
        # Get all active ports
        active_ports = GSMPortManager.get_active_ports()
        print(f"Active ports: {active_ports}")
        
        # Test user port assignments with more ports
        from app import User
        
        # Update test users with different port ranges
        test1 = User.query.filter_by(username='test1').first()
        diet = User.query.filter_by(username='diet').first()
        
        if test1:
            # Assign ports 1-4 to test1
            test1.assigned_ports = '1,2,3,4'
            print(f"test1 assigned ports: {test1.get_assigned_ports()}")
        
        if diet:
            # Assign ports 5-8 to diet
            diet.assigned_ports = '5,6,7,8'
            print(f"diet assigned ports: {diet.get_assigned_ports()}")
        
        db.session.commit()
        
        # Test port usage permissions
        print(f"\nPort usage permissions:")
        for user in [test1, diet]:
            if user:
                print(f"  {user.username}:")
                for port in ['1', '5', '9', '16']:
                    can_use = user.can_use_port(port)
                    print(f"    Port {port}: {'✅' if can_use else '❌'}")

if __name__ == "__main__":
    test_dynamic_ports()
    test_port_filtering_with_16_ports()
