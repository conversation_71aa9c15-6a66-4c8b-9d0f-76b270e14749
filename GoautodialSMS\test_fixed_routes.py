#!/usr/bin/env python3
"""
Test Fixed Routes
"""

import requests
import time

def test_fixed_routes():
    """Test the fixed routes to verify they're working"""
    print("🔍 Testing Fixed Routes")
    print("=" * 30)
    
    try:
        # Wait for Flask app
        time.sleep(2)
        
        # Create session and login
        session = requests.Session()
        
        # Login as admin
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data, allow_redirects=True)
        
        if login_response.status_code == 200:
            print("✅ Successfully logged in")
            
            # Test GSM Gateway route
            print("\n🔧 Testing GSM Gateway Route:")
            gsm_response = session.get('http://127.0.0.1:5000/gsm_gateways')
            
            if gsm_response.status_code == 200:
                content = gsm_response.text
                
                # Check for 8 ports display
                if "Ports (8):" in content:
                    print("✅ GSM Gateway shows 'Ports (8):' - FIXED!")
                elif "Ports (4):" in content:
                    print("❌ GSM Gateway still shows 'Ports (4):'")
                else:
                    print("❓ No 'Ports (X):' pattern found")
                
                # Check for all 8 ports
                ports_found = []
                for i in range(1, 9):
                    if f"Port {i}" in content:
                        ports_found.append(i)
                
                print(f"   Ports displayed: {ports_found}")
                if len(ports_found) == 8:
                    print("✅ All 8 ports are displayed - FIXED!")
                else:
                    print(f"❌ Only {len(ports_found)} ports displayed")
                
                # Check Add Gateway button
                if 'id="addGatewayBtn"' in content or 'Add Gateway' in content:
                    print("✅ Add Gateway button is present")
                else:
                    print("❌ Add Gateway button not found")
            
            else:
                print(f"❌ GSM Gateway route failed: {gsm_response.status_code}")
            
            # Test User Creation route
            print("\n👤 Testing User Creation Route:")
            user_create_response = session.get('http://127.0.0.1:5000/users/create')
            
            if user_create_response.status_code == 200:
                content = user_create_response.text
                
                # Check for gateway info
                if "Main Gateway" in content:
                    print("✅ User creation shows 'Main Gateway' - FIXED!")
                else:
                    print("❌ User creation doesn't show Main Gateway")
                
                # Check for warning message
                if "No GSM gateway configured yet" in content:
                    print("❌ User creation still shows 'No GSM gateway configured yet'")
                else:
                    print("✅ No 'gateway not configured' warning - FIXED!")
                
                # Check for port checkboxes
                port_checkboxes = []
                for i in range(1, 9):
                    if f'name="assigned_ports" value="{i}"' in content:
                        port_checkboxes.append(i)
                
                print(f"   Port checkboxes found: {port_checkboxes}")
                if len(port_checkboxes) >= 8:
                    print("✅ All port checkboxes are available - FIXED!")
                else:
                    print(f"❌ Only {len(port_checkboxes)} port checkboxes found")
            
            else:
                print(f"❌ User creation route failed: {user_create_response.status_code}")
            
            return True
            
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_fixed_routes()
