#!/usr/bin/env python3
"""
Simple startup script for Modern SMS Manager
"""
import os
import sys
from flask import Flask, render_template_string, redirect, url_for, request, flash, session
from flask_login import <PERSON>gin<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

# Simple in-memory storage for demo
users_db = {
    'admin': {
        'password_hash': generate_password_hash('admin123'),
        'role': 'admin',
        'full_name': 'Administrator'
    }
}

gateways_db = []
messages_db = []

class User(UserMixin):
    def __init__(self, username):
        self.id = username
        self.username = username
        self.role = users_db[username]['role']
        self.full_name = users_db[username]['full_name']
    
    def check_password(self, password):
        return check_password_hash(users_db[self.username]['password_hash'], password)

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'demo-secret-key'

# Setup Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    if user_id in users_db:
        return User(user_id)
    return None

# Routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username in users_db:
            user = User(username)
            if user.check_password(password):
                login_user(user)
                flash(f'Welcome back, {user.full_name}!', 'success')
                return redirect(url_for('dashboard'))
        
        flash('Invalid username or password', 'error')
    
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>Modern SMS Manager - Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="bi bi-chat-square-dots text-primary" style="font-size: 3rem;"></i>
                            <h2 class="fw-bold text-dark mt-2">Modern SMS Manager</h2>
                            <p class="text-muted">Sign in to your account</p>
                        </div>
                        
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">Sign In</button>
                            </div>
                        </form>
                        
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="fw-bold text-muted mb-2">Demo Credentials</h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">Username:</small><br>
                                    <code>admin</code>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Password:</small><br>
                                    <code>admin123</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    ''')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('login'))

@app.route('/')
@app.route('/dashboard')
@login_required
def dashboard():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>Dashboard - Modern SMS Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            min-height: calc(100vh - 40px);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <div class="main-container">
            <div class="p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-1">🎉 Modern SMS Manager Demo</h1>
                        <p class="text-muted">Welcome back, {{ current_user.full_name }}!</p>
                    </div>
                    <div>
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </div>
                </div>
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <h3>{{ gateways|length }}</h3>
                            <p><i class="bi bi-hdd-network"></i> Gateways</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card" style="background: linear-gradient(135deg, #059669 0%, #10b981 100%);">
                            <h3>{{ messages|length }}</h3>
                            <p><i class="bi bi-chat-dots"></i> Messages</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card" style="background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);">
                            <h3>8</h3>
                            <p><i class="bi bi-diagram-3"></i> Ports</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);">
                            <h3>✅</h3>
                            <p><i class="bi bi-check-circle"></i> System OK</p>
                        </div>
                    </div>
                </div>
                
                <!-- Success Message -->
                <div class="card">
                    <div class="card-body text-center p-5">
                        <h2 class="text-success mb-4">🎉 Installation Successful!</h2>
                        <p class="lead">Modern SMS Manager is now running and ready to use!</p>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5>✅ What's Working:</h5>
                                <ul class="list-unstyled text-start">
                                    <li>✅ Python {{ python_version }}</li>
                                    <li>✅ Flask Web Framework</li>
                                    <li>✅ Bootstrap UI</li>
                                    <li>✅ User Authentication</li>
                                    <li>✅ Session Management</li>
                                    <li>✅ Modern Design</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5>🚀 Next Steps:</h5>
                                <ol class="text-start">
                                    <li>Stop this demo (Ctrl+C)</li>
                                    <li>Run: <code>python run.py</code></li>
                                    <li>Configure MySQL database</li>
                                    <li>Add GSM gateways</li>
                                    <li>Start sending SMS!</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-4">
                            <h6>📚 Full Application Features:</h6>
                            <p class="mb-0">The complete Modern SMS Manager includes MySQL database, real-time port detection, conversation threading, gateway management, and much more!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    ''', gateways=gateways_db, messages=messages_db, python_version=sys.version.split()[0])

if __name__ == '__main__':
    print("="*70)
    print("🎉 Modern SMS Manager - Demo Application")
    print("="*70)
    print("🌐 Starting demo server...")
    print("📍 URL: http://127.0.0.1:5000")
    print("🔐 Login: admin / admin123")
    print("🛑 Press Ctrl+C to stop")
    print("="*70)
    print()
    
    app.run(host='127.0.0.1', port=5000, debug=True, use_reloader=False)
