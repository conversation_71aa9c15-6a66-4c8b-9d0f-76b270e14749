#!/usr/bin/env python3
"""
Check IP access and event permissions
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def check_connection_details():
    """Check what IP we're connecting from and test different approaches"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔍 CHECKING CONNECTION DETAILS AND IP ACCESS")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        try:
            # Connect and check our local IP
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Get our local IP address
            local_ip = sock.getsockname()[0]
            print(f"📍 Our IP address: {local_ip}")
            print(f"📡 Gateway IP: {gateway.ip_address}")
            print(f"🔌 Connection: {local_ip} → {gateway.ip_address}:{gateway.tg_sms_port}")
            
            # Read initial and login
            initial = sock.recv(1024).decode()
            print(f"📥 Initial: {initial.strip()}")
            
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            login_resp = sock.recv(1024).decode()
            print(f"📥 Login: {login_resp.strip()}")
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed!")
                sock.close()
                return False
            
            print("✅ Login successful!")
            
            # Try different event subscription approaches
            print(f"\n🧪 TESTING DIFFERENT EVENT APPROACHES")
            print("-" * 50)
            
            # Method 1: Standard Events
            print(f"1️⃣  Testing standard Events command...")
            event_cmd1 = "Action: Events\r\nEventMask: all\r\n\r\n"
            sock.send(event_cmd1.encode())
            resp1 = sock.recv(1024).decode()
            print(f"   📥 Response: {resp1.strip()}")
            
            # Method 2: Try without EventMask
            print(f"\n2️⃣  Testing Events without EventMask...")
            event_cmd2 = "Action: Events\r\n\r\n"
            sock.send(event_cmd2.encode())
            resp2 = sock.recv(1024).decode()
            print(f"   📥 Response: {resp2.strip()}")
            
            # Method 3: Try with specific SMS mask
            print(f"\n3️⃣  Testing Events with SMS mask...")
            event_cmd3 = "Action: Events\r\nEventMask: sms\r\n\r\n"
            sock.send(event_cmd3.encode())
            resp3 = sock.recv(1024).decode()
            print(f"   📥 Response: {resp3.strip()}")
            
            # Method 4: Check if we can run SMS commands
            print(f"\n4️⃣  Testing SMS commands...")
            sms_cmd = "Action: smscommand\r\ncommand: gsm show spans\r\n\r\n"
            sock.send(sms_cmd.encode())
            sms_resp = sock.recv(2048).decode()
            print(f"   📥 SMS Command Response: {sms_resp.strip()}")
            
            # Check if any method enabled events
            if "Events: On" in resp1 or "Events: On" in resp2 or "Events: On" in resp3:
                print(f"\n🎉 Events are enabled! Testing SMS reception...")
                
                # Listen for events
                print(f"👂 Listening for SMS events for 10 seconds...")
                print(f"📱 Send an SMS now!")
                
                sock.settimeout(1)
                start_time = time.time()
                
                while time.time() - start_time < 10:
                    try:
                        data = sock.recv(4096).decode()
                        if data and "Event:" in data:
                            print(f"\n📨 EVENT RECEIVED: {data}")
                            if "ReceivedSMS" in data:
                                print(f"🎉 SMS EVENT DETECTED!")
                                sock.close()
                                return True
                    except socket.timeout:
                        print(".", end="", flush=True)
                        continue
                
                print(f"\n⏰ No SMS events received during test")
            else:
                print(f"\n❌ Events are still not enabled")
                print(f"💡 This suggests IP access restrictions")
            
            sock.close()
            return False
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False

def suggest_ip_settings():
    """Suggest IP settings for TG SMS API"""
    print(f"\n💡 IP ADDRESS SETTINGS FOR TG SMS API")
    print("="*60)
    print(f"In your TG gateway web interface (SMS → API Settings):")
    print(f"")
    print(f"✅ Enable API: [✓] Checked")
    print(f"")
    print(f"✅ Allowed IP addresses - try one of these:")
    print(f"   Option 1: 127.0.0.1 (localhost only)")
    print(f"   Option 2: ***********/24 (entire local network)")
    print(f"   Option 3: 0.0.0.0/0 (all IPs - less secure)")
    print(f"   Option 4: Your specific IP from the test above")
    print(f"")
    print(f"✅ Username: apiuser")
    print(f"✅ Password: apipass")
    print(f"")
    print(f"💾 Don't forget to SAVE and APPLY changes!")

def test_manual_event_enable():
    """Try to manually enable events using different methods"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔧 MANUAL EVENT ENABLE ATTEMPTS")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        # Commands to try enabling events
        enable_commands = [
            "Action: Events\r\nEventMask: all\r\nEvents: on\r\n\r\n",
            "Action: Events\r\nEventMask: *\r\n\r\n",
            "Action: Events\r\nEventMask: sms,message\r\n\r\n",
            "Action: Command\r\nCommand: events set on\r\n\r\n"
        ]
        
        for i, cmd in enumerate(enable_commands, 1):
            print(f"\n🧪 Attempt {i}: {cmd.strip()}")
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((gateway.ip_address, gateway.tg_sms_port))
                
                # Login
                initial = sock.recv(1024).decode()
                login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
                sock.send(login_cmd.encode())
                login_resp = sock.recv(1024).decode()
                
                if "Response: Success" not in login_resp:
                    print(f"   ❌ Login failed")
                    sock.close()
                    continue
                
                # Try the enable command
                sock.send(cmd.encode())
                resp = sock.recv(1024).decode()
                print(f"   📥 Response: {resp.strip()}")
                
                if "Events: On" in resp:
                    print(f"   🎉 SUCCESS! Method {i} enabled events!")
                    sock.close()
                    return True
                
                sock.close()
                
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        return False

if __name__ == '__main__':
    print("🚀 IP Access and Event Permission Checker")
    
    # Check connection details
    events_working = check_connection_details()
    
    if not events_working:
        # Suggest IP settings
        suggest_ip_settings()
        
        # Try manual event enable
        manual_success = test_manual_event_enable()
        
        if not manual_success:
            print(f"\n❌ Could not enable events automatically")
            print(f"")
            print(f"🔧 MANUAL STEPS NEEDED:")
            print(f"1. Go to your TG gateway web interface")
            print(f"2. Navigate to SMS → API Settings")
            print(f"3. Ensure 'Enable API' is checked")
            print(f"4. Add allowed IP addresses (try 0.0.0.0/0 for testing)")
            print(f"5. Save and apply changes")
            print(f"6. Restart TG SMS service if needed")
    else:
        print(f"\n🎉 Events are working! SMS receiving should be operational!")
    
    print("\n" + "="*60)
