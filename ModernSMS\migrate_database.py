#!/usr/bin/env python3
"""
Database migration script to add missing columns
"""
import os
import sys
import sqlite3

def migrate_database():
    """Add missing columns to the database"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'modern_sms.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    print(f"🔧 Migrating database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if can_delete_messages column exists
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        print(f"📋 Current user table columns: {columns}")
        
        if 'can_delete_messages' not in columns:
            print("➕ Adding can_delete_messages column...")
            cursor.execute("ALTER TABLE users ADD COLUMN can_delete_messages BOOLEAN DEFAULT 0")
            print("✅ Added can_delete_messages column")
        else:
            print("✅ can_delete_messages column already exists")
        
        # Update admin user to have delete permission
        print("🔧 Updating admin user permissions...")
        cursor.execute("""
            UPDATE users 
            SET can_delete_messages = 1 
            WHERE username = 'admin' OR can_manage_users = 1
        """)
        
        affected_rows = cursor.rowcount
        print(f"✅ Updated {affected_rows} admin users with delete permission")
        
        conn.commit()
        conn.close()
        
        print("✅ Database migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        return False

if __name__ == '__main__':
    print("="*60)
    print("🗄️  DATABASE MIGRATION")
    print("="*60)
    
    success = migrate_database()
    
    if success:
        print("\n🎉 Migration completed! You can now start the application.")
    else:
        print("\n❌ Migration failed! Please check the errors above.")
    
    print("="*60)
