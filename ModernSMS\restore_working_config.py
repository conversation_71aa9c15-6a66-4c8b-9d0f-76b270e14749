#!/usr/bin/env python3
"""
Restore working SMS configuration
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def test_both_gateways():
    """Test both gateways to see which one has working SMS events"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔍 TESTING BOTH GATEWAYS FOR SMS EVENTS")
        print("="*60)
        
        gateways = GSMGateway.query.all()
        
        for gateway in gateways:
            print(f"\n📡 Testing Gateway: {gateway.name}")
            print(f"   IP: {gateway.ip_address}:{gateway.tg_sms_port}")
            print(f"   Primary: {gateway.is_primary}")
            print(f"   Credentials: {gateway.username}/{gateway.password}")
            
            try:
                # Test connection
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((gateway.ip_address, gateway.tg_sms_port))
                
                if result != 0:
                    print(f"   ❌ Cannot connect to {gateway.ip_address}:{gateway.tg_sms_port}")
                    sock.close()
                    continue
                
                print(f"   ✅ Network connection successful")
                
                # Read initial response
                initial = sock.recv(1024).decode()
                print(f"   📥 Initial: {initial.strip()}")
                
                # Send login
                login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
                sock.send(login_cmd.encode())
                
                # Read login response
                login_resp = sock.recv(1024).decode()
                print(f"   📥 Login: {login_resp.strip()}")
                
                if "Response: Success" not in login_resp:
                    print(f"   ❌ Login failed")
                    sock.close()
                    continue
                
                print(f"   ✅ Login successful")
                
                # Test event subscription
                event_cmd = "Action: Events\r\nEventMask: all\r\n\r\n"
                sock.send(event_cmd.encode())
                
                event_resp = sock.recv(1024).decode()
                print(f"   📥 Events: {event_resp.strip()}")
                
                if "Events: On" in event_resp:
                    print(f"   🎉 SMS EVENTS ARE ENABLED ON THIS GATEWAY!")
                    
                    # This gateway has working SMS events
                    print(f"   👂 Listening for SMS events for 5 seconds...")
                    
                    sock.settimeout(1)
                    start_time = time.time()
                    
                    while time.time() - start_time < 5:
                        try:
                            data = sock.recv(4096).decode()
                            if data and "Event:" in data:
                                print(f"   📨 Event received: {data.strip()}")
                        except socket.timeout:
                            print(".", end="", flush=True)
                            continue
                    
                    print(f"\n   ✅ This gateway ({gateway.name}) has working SMS events!")
                    sock.close()
                    return gateway
                else:
                    print(f"   ⚠️  SMS events are disabled on this gateway")
                
                sock.close()
                
            except Exception as e:
                print(f"   ❌ Error testing gateway: {str(e)}")
        
        return None

def switch_to_working_gateway(working_gateway):
    """Switch to the working gateway as primary"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔄 SWITCHING TO WORKING GATEWAY")
        print("="*60)
        
        # Set all gateways to non-primary
        all_gateways = GSMGateway.query.all()
        for gw in all_gateways:
            gw.is_primary = False
        
        # Set working gateway as primary
        working_gateway.is_primary = True
        
        db.session.commit()
        
        print(f"✅ Set {working_gateway.name} ({working_gateway.ip_address}) as primary gateway")
        
        # Restart SMS receiver with new primary gateway
        print(f"\n🔄 Restarting SMS receiver...")
        
        try:
            from app.sms.receiver import SMSReceiver
            
            # Create new receiver
            new_receiver = SMSReceiver(app)
            new_receiver.start_receiver()
            app.sms_receiver = new_receiver
            
            # Wait and check status
            time.sleep(3)
            
            print(f"📊 SMS Receiver Status:")
            print(f"   Running: {'✅' if new_receiver.running else '❌'}")
            print(f"   Connected: {'✅' if new_receiver.connected else '❌'}")
            print(f"   Gateway: {working_gateway.ip_address}:{working_gateway.tg_sms_port}")
            
            if new_receiver.running and new_receiver.connected:
                print(f"🎉 SMS receiver successfully switched to working gateway!")
                return True
            else:
                print(f"❌ SMS receiver failed to connect to working gateway")
                return False
                
        except Exception as e:
            print(f"❌ Error restarting SMS receiver: {str(e)}")
            return False

def restore_original_credentials():
    """Try to restore original working credentials"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔧 TRYING DIFFERENT CREDENTIALS")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        # Try different credential combinations that might have worked before
        credential_sets = [
            ("apiuser", "apipass"),  # Current
            ("admin", "admin"),      # Common default
            ("admin", "password"),   # Common default
            ("manager", "manager"),  # Asterisk default
            ("tgsms", "tgsms"),     # TG SMS default
        ]
        
        for username, password in credential_sets:
            print(f"\n🔐 Testing credentials: {username}/{password}")
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((gateway.ip_address, gateway.tg_sms_port))
                
                # Read initial
                initial = sock.recv(1024).decode()
                
                # Login with test credentials
                login_cmd = f"Action: Login\r\nUsername: {username}\r\nSecret: {password}\r\n\r\n"
                sock.send(login_cmd.encode())
                
                login_resp = sock.recv(1024).decode()
                
                if "Response: Success" in login_resp:
                    print(f"   ✅ Login successful!")
                    
                    # Test events
                    event_cmd = "Action: Events\r\nEventMask: all\r\n\r\n"
                    sock.send(event_cmd.encode())
                    
                    event_resp = sock.recv(1024).decode()
                    
                    if "Events: On" in event_resp:
                        print(f"   🎉 SMS EVENTS WORK WITH THESE CREDENTIALS!")
                        
                        # Update gateway with working credentials
                        gateway.username = username
                        gateway.password = password
                        db.session.commit()
                        
                        print(f"   ✅ Updated gateway credentials")
                        sock.close()
                        return True
                    else:
                        print(f"   ⚠️  Events disabled with these credentials")
                else:
                    print(f"   ❌ Login failed")
                
                sock.close()
                
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        return False

if __name__ == '__main__':
    print("🚀 SMS Configuration Restoration Tool")
    print("🎯 Goal: Restore working SMS receiving like before")
    
    # Test both gateways to find working one
    working_gateway = test_both_gateways()
    
    if working_gateway:
        print(f"\n✅ Found working gateway: {working_gateway.name}")
        
        if not working_gateway.is_primary:
            # Switch to working gateway
            success = switch_to_working_gateway(working_gateway)
            if success:
                print(f"\n🎉 Successfully restored SMS receiving!")
            else:
                print(f"\n❌ Failed to switch to working gateway")
        else:
            print(f"\n✅ Working gateway is already primary")
    else:
        print(f"\n⚠️  No gateway found with working SMS events")
        print(f"🔧 Trying different credentials...")
        
        # Try different credentials
        success = restore_original_credentials()
        
        if success:
            print(f"\n🎉 Found working credentials! SMS receiving should work now.")
        else:
            print(f"\n❌ Could not find working credentials")
            print(f"\n💡 Manual steps needed:")
            print(f"   1. Check gateway web interface for SMS event settings")
            print(f"   2. Enable SMS events/notifications")
            print(f"   3. Verify TG SMS user has event permissions")
    
    print("\n" + "="*60)
