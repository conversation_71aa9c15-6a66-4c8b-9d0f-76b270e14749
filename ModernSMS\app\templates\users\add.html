{% extends "base.html" %}

{% block title %}Add User - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Add New User</h1>
        <p class="text-muted">Create a new user account</p>
    </div>
    <div>
        <a href="{{ url_for('users.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person-plus"></i> User Information</h5>
            </div>
            <div class="card-body">
                <form id="userForm" method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label fw-semibold">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="form-text">Unique username for login</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label fw-semibold">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="form-text">User's email address</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label fw-semibold">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label fw-semibold">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label fw-semibold">Password *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="bi bi-eye" id="passwordIcon"></i>
                                </button>
                            </div>
                            <div class="form-text">Minimum 6 characters</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="role_id" class="form-label fw-semibold">Role</label>
                            <select class="form-select" id="role_id" name="role_id">
                                <option value="">Select role...</option>
                                {% for role in roles %}
                                <option value="{{ role.id }}">{{ role.name.title() }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">User's role in the system</div>
                        </div>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Permissions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_send_sms" name="can_send_sms" checked>
                                        <label class="form-check-label" for="can_send_sms">
                                            <i class="bi bi-send text-primary"></i> Can Send SMS
                                        </label>
                                    </div>

                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_view_inbox" name="can_view_inbox" checked>
                                        <label class="form-check-label" for="can_view_inbox">
                                            <i class="bi bi-inbox text-info"></i> Can View Inbox
                                        </label>
                                    </div>

                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_view_outbox" name="can_view_outbox" checked>
                                        <label class="form-check-label" for="can_view_outbox">
                                            <i class="bi bi-send text-success"></i> Can View Outbox
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_manage_gateways" name="can_manage_gateways">
                                        <label class="form-check-label" for="can_manage_gateways">
                                            <i class="bi bi-hdd-network text-warning"></i> Can Manage Gateways
                                        </label>
                                    </div>

                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_manage_users" name="can_manage_users">
                                        <label class="form-check-label" for="can_manage_users">
                                            <i class="bi bi-people text-danger"></i> Can Manage Users
                                        </label>
                                    </div>

                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_view_reports" name="can_view_reports">
                                        <label class="form-check-label" for="can_view_reports">
                                            <i class="bi bi-graph-up text-secondary"></i> Can View Reports
                                        </label>
                                    </div>

                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="can_export_data" name="can_export_data">
                                        <label class="form-check-label" for="can_export_data">
                                            <i class="bi bi-download text-dark"></i> Can Export Data
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Port Assignments</h6>
                            <small class="text-muted">Leave empty for all ports access</small>
                        </div>
                        <div class="card-body">
                            <div id="portAssignments">
                                <p class="text-muted small mb-3">
                                    <i class="bi bi-info-circle"></i>
                                    Select specific ports this user can access. If none selected, user can access all available ports.
                                </p>

                                <div class="row" id="availablePorts">
                                    <!-- Ports will be loaded here via JavaScript -->
                                    <div class="col-12 text-center">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">Loading ports...</span>
                                        </div>
                                        <p class="text-muted mt-2">Loading available ports...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2 mt-4">
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="bi bi-person-plus"></i>
                            <span class="btn-text">Create User</span>
                            <span class="loading-spinner d-none"></span>
                        </button>
                        
                        <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                            <i class="bi bi-x-circle"></i> Clear
                        </button>
                        
                        <a href="{{ url_for('users.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> User Creation Tips</h6>
            </div>
            <div class="card-body">
                <h6 class="fw-bold">Required Fields</h6>
                <ul class="list-unstyled">
                    <li><i class="bi bi-check text-success"></i> Username</li>
                    <li><i class="bi bi-check text-success"></i> Email Address</li>
                    <li><i class="bi bi-check text-success"></i> Password</li>
                </ul>
                
                <hr>
                
                <h6 class="fw-bold">Default Permissions</h6>
                <ul class="small">
                    <li>Send SMS - Enabled</li>
                    <li>View Inbox - Enabled</li>
                    <li>View Outbox - Enabled</li>
                    <li>Management permissions - Disabled</li>
                </ul>
                
                <hr>
                
                <h6 class="fw-bold">Security Notes</h6>
                <ul class="small">
                    <li>Passwords must be at least 6 characters</li>
                    <li>Usernames must be unique</li>
                    <li>Email addresses must be unique</li>
                    <li>Users can change their own passwords</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-shield-check"></i> Role Information</h6>
            </div>
            <div class="card-body">
                {% if roles %}
                    {% for role in roles %}
                    <div class="mb-2">
                        <strong>{{ role.name.title() }}</strong><br>
                        <small class="text-muted">{{ role.description or 'No description' }}</small>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted small">No roles configured</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('passwordIcon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'bi bi-eye';
        }
    }

    function clearForm() {
        document.getElementById('userForm').reset();
        // Re-check default permissions
        document.getElementById('can_send_sms').checked = true;
        document.getElementById('can_view_inbox').checked = true;
        document.getElementById('can_view_outbox').checked = true;
    }

    // Form submission
    document.getElementById('userForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const saveBtn = document.getElementById('saveBtn');
        const btnText = saveBtn.querySelector('.btn-text');
        const spinner = saveBtn.querySelector('.loading-spinner');
        
        // Show loading state
        btnText.classList.add('d-none');
        spinner.classList.remove('d-none');
        saveBtn.disabled = true;
        
        const formData = new FormData(this);
        
        fetch(this.action || window.location.pathname, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (response.ok) {
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        // If not JSON, assume success and redirect
                        window.location.href = "{{ url_for('users.index') }}";
                        return null;
                    }
                });
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        })
        .then(data => {
            if (data && data.success) {
                showToast(data.message || 'User created successfully!', 'success');
                setTimeout(() => {
                    window.location.href = "{{ url_for('users.index') }}";
                }, 1000);
            } else if (data && !data.success) {
                showToast('Error: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Failed to create user: ' + error.message, 'error');
        })
        .finally(() => {
            // Restore button state
            btnText.classList.remove('d-none');
            spinner.classList.add('d-none');
            saveBtn.disabled = false;
        });
    });

    // Auto-set admin permissions when admin role is selected
    document.getElementById('role_id').addEventListener('change', function() {
        const selectedRole = this.options[this.selectedIndex].text.toLowerCase();

        if (selectedRole === 'admin') {
            // Check all permissions for admin
            document.querySelectorAll('input[type="checkbox"]:not([name^="port_"])').forEach(checkbox => {
                checkbox.checked = true;
            });
        }
    });

    // Load available ports
    function loadAvailablePorts() {
        fetch('/api/gateways')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.gateways) {
                    displayPorts(data.gateways);
                } else {
                    showNoPorts();
                }
            })
            .catch(error => {
                console.error('Error loading ports:', error);
                showNoPorts();
            });
    }

    function displayPorts(gateways) {
        const container = document.getElementById('availablePorts');

        if (gateways.length === 0) {
            showNoPorts();
            return;
        }

        let portsHtml = '';
        gateways.forEach(gateway => {
            if (gateway.port_summary && gateway.port_summary.total > 0) {
                portsHtml += `
                    <div class="col-12 mb-3">
                        <h6 class="fw-bold text-primary">${gateway.name}</h6>
                        <div class="row">
                `;

                // Generate ports for this gateway
                for (let i = 1; i <= gateway.max_ports; i++) {
                    portsHtml += `
                        <div class="col-md-4 col-sm-6 mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       id="port_${gateway.id}_${i}"
                                       name="assigned_ports"
                                       value="${gateway.id}:${i}">
                                <label class="form-check-label" for="port_${gateway.id}_${i}">
                                    <small>Port ${i}</small>
                                    <span class="badge bg-secondary ms-1">Gateway ${gateway.id}</span>
                                </label>
                            </div>
                        </div>
                    `;
                }

                portsHtml += `
                        </div>
                    </div>
                `;
            }
        });

        if (portsHtml) {
            container.innerHTML = portsHtml;
        } else {
            showNoPorts();
        }
    }

    function showNoPorts() {
        const container = document.getElementById('availablePorts');
        container.innerHTML = `
            <div class="col-12 text-center">
                <i class="bi bi-diagram-3 text-muted" style="font-size: 2rem;"></i>
                <p class="text-muted mt-2">No gateways configured yet.</p>
                <p class="text-muted small">Add a gateway first to assign ports to users.</p>
            </div>
        `;
    }

    // Load ports when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadAvailablePorts();
    });
</script>
{% endblock %}
