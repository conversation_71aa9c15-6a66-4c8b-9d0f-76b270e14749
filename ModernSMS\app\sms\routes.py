"""
SMS management routes
"""
from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app.sms import bp
from app.models import SMSMessage, GSMGateway, SMSPort, User
from app.sms.services import SMSService, GSMPortService
from app import db
from datetime import datetime
import uuid

@bp.route('/inbox')
@login_required
def inbox():
    """SMS Inbox"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
        
        # Build query with user permissions
        query = SMSMessage.query.filter_by(direction='inbound')
        
        # Filter by user's assigned ports if not admin
        if not current_user.has_permission('manage_users'):
            assigned_ports = current_user.get_assigned_ports()
            current_app.logger.info(f"=== INBOX DEBUG for user {current_user.username} ===")
            current_app.logger.info(f"User assigned_ports: {assigned_ports}")

            if assigned_ports:
                # Convert assignments to actual port IDs
                port_ids = []
                for assignment in assigned_ports:
                    gateway_id = assignment.get('gateway_id')
                    port_number = assignment.get('port')

                    if gateway_id and port_number:
                        # Find port by gateway_id and port_number
                        port = SMSPort.query.filter_by(
                            gateway_id=gateway_id,
                            port_number=str(port_number),
                            is_active=True
                        ).first()
                        if port:
                            port_ids.append(port.id)
                            current_app.logger.info(f"Found port ID {port.id} for Gateway {gateway_id}, Port {port_number}")

                current_app.logger.info(f"Final port_ids for filtering: {port_ids}")

                if port_ids:
                    query = query.filter(SMSMessage.port_id.in_(port_ids))
                else:
                    # If no valid ports found, show no messages
                    query = query.filter(SMSMessage.id == -1)  # Impossible condition
        
        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)
        
        return render_template('sms/inbox.html', messages=messages)
        
    except Exception as e:
        current_app.logger.error(f"Inbox error: {str(e)}")
        flash('Error loading inbox.', 'error')
        return render_template('sms/inbox.html', messages=None)

@bp.route('/outbox')
@login_required
def outbox():
    """SMS Outbox"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
        
        # Build query with user permissions
        query = SMSMessage.query.filter_by(direction='outbound')
        
        # Filter by user's messages if not admin
        if not current_user.has_permission('manage_users'):
            query = query.filter_by(user_id=current_user.id)
        
        messages = query.order_by(SMSMessage.created_at.desc())\
                       .paginate(page=page, per_page=per_page, error_out=False)
        
        return render_template('sms/outbox.html', messages=messages)
        
    except Exception as e:
        current_app.logger.error(f"Outbox error: {str(e)}")
        flash('Error loading outbox.', 'error')
        return render_template('sms/outbox.html', messages=None)

@bp.route('/compose')
@login_required
def compose():
    """Compose SMS"""
    try:
        # Debug: Log user assignments
        current_app.logger.info(f"=== COMPOSE DEBUG for user {current_user.username} ===")
        current_app.logger.info(f"User assigned_ports: {current_user.assigned_ports}")
        current_app.logger.info(f"User role: {current_user.role.name if current_user.role else 'None'}")
        current_app.logger.info(f"Has manage_users permission: {current_user.has_permission('manage_users')}")

        # Get available ports for user
        available_ports = GSMPortService.get_user_available_ports(current_user)
        current_app.logger.info(f"Available ports returned: {len(available_ports)}")
        for port in available_ports:
            current_app.logger.info(f"  - Port {port.id}: Gateway {port.gateway_id}, Port {port.port_number}")

        return render_template('sms/compose.html', ports=available_ports)

    except Exception as e:
        current_app.logger.error(f"Compose error: {str(e)}")
        import traceback
        current_app.logger.error(f"Traceback: {traceback.format_exc()}")
        flash('Error loading compose page.', 'error')
        return render_template('sms/compose.html', ports=[])

@bp.route('/send', methods=['POST'])
@login_required
def send_sms():
    """Send SMS"""
    try:
        phone_number = request.form.get('phone_number', '').strip()
        message = request.form.get('message', '').strip()
        port_id = request.form.get('port_id', type=int)
        
        # Validate inputs
        if not phone_number:
            return jsonify({'success': False, 'error': 'Phone number is required'})
        
        if not message:
            return jsonify({'success': False, 'error': 'Message is required'})
        
        if not port_id:
            return jsonify({'success': False, 'error': 'Port selection is required'})
        
        # Validate phone number format
        if not SMSService.validate_phone_number(phone_number):
            return jsonify({'success': False, 'error': 'Invalid phone number format'})
        
        # Check if user can use this port
        port = SMSPort.query.get(port_id)
        if not port:
            return jsonify({'success': False, 'error': 'Invalid port selected'})
        
        if not current_user.can_use_port(port.port_number, port.gateway_id):
            return jsonify({'success': False, 'error': 'You do not have permission to use this port'})
        
        # Send SMS
        result = SMSService.send_sms(
            phone_number=phone_number,
            message=message,
            port=port,
            user=current_user
        )
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': 'SMS sent successfully',
                'message_id': result['message_id']
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            })
            
    except Exception as e:
        current_app.logger.error(f"Send SMS error: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'})

@bp.route('/conversation/<phone_number>')
@login_required
def conversation(phone_number):
    """View conversation with specific number"""
    try:
        # Get all messages for this conversation
        messages = SMSMessage.query.filter_by(phone_number=phone_number)\
                                  .order_by(SMSMessage.created_at.asc()).all()
        
        # Filter by user permissions if not admin
        if not current_user.has_permission('manage_users'):
            # Filter messages based on user's assigned ports or own messages
            filtered_messages = []
            assigned_ports = current_user.get_assigned_ports()

            # Convert assignments to actual port IDs
            port_ids = []
            if assigned_ports:
                for assignment in assigned_ports:
                    gateway_id = assignment.get('gateway_id')
                    port_number = assignment.get('port')

                    if gateway_id and port_number:
                        # Find port by gateway_id and port_number
                        port = SMSPort.query.filter_by(
                            gateway_id=gateway_id,
                            port_number=str(port_number),
                            is_active=True
                        ).first()
                        if port:
                            port_ids.append(port.id)

            for msg in messages:
                if (msg.direction == 'outbound' and msg.user_id == current_user.id) or \
                   (msg.direction == 'inbound' and (not port_ids or msg.port_id in port_ids)):
                    filtered_messages.append(msg)

            messages = filtered_messages
        
        # Get conversation stats
        stats = {
            'total_messages': len(messages),
            'sent_count': len([m for m in messages if m.direction == 'outbound']),
            'received_count': len([m for m in messages if m.direction == 'inbound']),
            'last_message_time': messages[-1].created_at if messages else None
        }
        
        # Get available ports for reply
        available_ports = GSMPortService.get_user_available_ports(current_user)
        
        return render_template('sms/conversation.html',
                             phone_number=phone_number,
                             messages=messages,
                             stats=stats,
                             ports=available_ports)
        
    except Exception as e:
        current_app.logger.error(f"Conversation error: {str(e)}")
        flash('Error loading conversation.', 'error')
        return redirect(url_for('sms.inbox'))

@bp.route('/message/<int:message_id>')
@login_required
def message_detail(message_id):
    """View message details"""
    try:
        message = SMSMessage.query.get_or_404(message_id)
        
        # Check permissions
        if not current_user.has_permission('manage_users'):
            if message.direction == 'outbound' and message.user_id != current_user.id:
                flash('Access denied.', 'error')
                return redirect(url_for('sms.outbox'))
            elif message.direction == 'inbound':
                assigned_ports = current_user.get_assigned_ports()

                # Convert assignments to actual port IDs
                port_ids = []
                if assigned_ports:
                    for assignment in assigned_ports:
                        gateway_id = assignment.get('gateway_id')
                        port_number = assignment.get('port')

                        if gateway_id and port_number:
                            # Find port by gateway_id and port_number
                            port = SMSPort.query.filter_by(
                                gateway_id=gateway_id,
                                port_number=str(port_number),
                                is_active=True
                            ).first()
                            if port:
                                port_ids.append(port.id)

                if port_ids and message.port_id not in port_ids:
                    flash('Access denied.', 'error')
                    return redirect(url_for('sms.inbox'))
        
        return render_template('sms/message_detail.html', message=message)
        
    except Exception as e:
        current_app.logger.error(f"Message detail error: {str(e)}")
        flash('Error loading message.', 'error')
        return redirect(url_for('sms.inbox'))
