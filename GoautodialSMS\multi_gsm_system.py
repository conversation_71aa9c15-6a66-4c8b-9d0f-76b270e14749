#!/usr/bin/env python3
"""
Multi-GSM Gateway Management System
Supports multiple GSM gateways with advanced port assignment
"""

import sys
import os
sys.path.append('backend')

from app import app, db
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

# Enhanced Models for Multi-GSM Support
class GSMGateway(db.Model):
    """GSM Gateway model for multiple gateway support"""
    __tablename__ = 'gsm_gateways'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)  # e.g., "Main Office Gateway", "Branch Gateway"
    ip_address = Column(String(15), nullable=False)  # Gateway IP
    api_port = Column(String(10), default='80')  # HTTP API port
    tg_sms_port = Column(String(10), default='5038')  # TG SMS port
    username = Column(String(50), nullable=False)
    password = Column(String(100), nullable=False)
    max_ports = Column(Integer, default=8)  # Number of ports on this gateway
    status = Column(String(20), default='active')  # active, inactive, maintenance
    location = Column(String(100))  # Physical location
    description = Column(Text)
    is_primary = Column(Boolean, default=False)  # Primary gateway
    created_at = Column(DateTime, default=datetime.utcnow)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    ports = relationship("EnhancedSMSPort", back_populates="gateway", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f'<GSMGateway {self.name} ({self.ip_address})>'
    
    def get_active_ports(self):
        """Get all active ports for this gateway"""
        return [p for p in self.ports if p.is_active]
    
    def get_port_count(self):
        """Get total number of ports"""
        return len(self.ports)
    
    def get_available_ports(self):
        """Get ports that are not assigned to any user"""
        assigned_port_ids = set()
        for assignment in UserPortAssignment.query.all():
            if assignment.port.gateway_id == self.id:
                assigned_port_ids.add(assignment.port_id)
        
        return [p for p in self.ports if p.id not in assigned_port_ids and p.is_active]

class EnhancedSMSPort(db.Model):
    """Enhanced SMS Port model with gateway association"""
    __tablename__ = 'enhanced_sms_ports'
    
    id = Column(Integer, primary_key=True)
    gateway_id = Column(Integer, ForeignKey('gsm_gateways.id'), nullable=False)
    port_number = Column(String(10), nullable=False)  # Port number on the gateway
    status = Column(String(20), default='active')
    network_name = Column(String(100))
    signal_quality = Column(String(20))
    sim_imsi = Column(String(20))
    sim_phone_number = Column(String(20))  # SIM card phone number
    is_active = Column(Boolean, default=True)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    gateway = relationship("GSMGateway", back_populates="ports")
    assignments = relationship("UserPortAssignment", back_populates="port", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f'<EnhancedSMSPort Gateway:{self.gateway.name} Port:{self.port_number}>'
    
    def get_full_identifier(self):
        """Get full port identifier: Gateway_Name:Port_Number"""
        return f"{self.gateway.name}:Port_{self.port_number}"
    
    def get_assigned_users(self):
        """Get users assigned to this port"""
        return [assignment.user for assignment in self.assignments]

class UserPortAssignment(db.Model):
    """Advanced user-to-port assignment model"""
    __tablename__ = 'user_port_assignments'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    port_id = Column(Integer, ForeignKey('enhanced_sms_ports.id'), nullable=False)
    assignment_type = Column(String(20), default='exclusive')  # exclusive, shared, backup
    priority = Column(Integer, default=1)  # 1=highest, 5=lowest
    is_active = Column(Boolean, default=True)
    assigned_at = Column(DateTime, default=datetime.utcnow)
    assigned_by = Column(Integer, ForeignKey('users.id'))  # Admin who made assignment
    notes = Column(Text)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    port = relationship("EnhancedSMSPort", back_populates="assignments")
    assigned_by_user = relationship("User", foreign_keys=[assigned_by])
    
    def __repr__(self):
        return f'<UserPortAssignment {self.user.username} -> {self.port.get_full_identifier()}>'

def create_multi_gsm_tables():
    """Create tables for multi-GSM system"""
    with app.app_context():
        print("🏗️ Creating Multi-GSM Gateway Tables")
        print("=" * 40)
        
        try:
            # Create tables
            db.create_all()
            print("✅ Tables created successfully")
            
            # Check if tables exist
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            required_tables = ['gsm_gateways', 'enhanced_sms_ports', 'user_port_assignments']
            for table in required_tables:
                if table in tables:
                    print(f"✅ Table '{table}' exists")
                else:
                    print(f"❌ Table '{table}' missing")
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating tables: {str(e)}")
            return False

def setup_sample_multi_gsm_configuration():
    """Set up sample multi-GSM configuration"""
    with app.app_context():
        print("🔧 Setting Up Sample Multi-GSM Configuration")
        print("=" * 50)
        
        try:
            # Create sample GSM gateways
            gateways_config = [
                {
                    'name': 'Main Office Gateway',
                    'ip_address': '*************',
                    'api_port': '80',
                    'tg_sms_port': '5038',
                    'username': 'apiuser',
                    'password': 'apipass',
                    'max_ports': 8,
                    'location': 'Main Office - Floor 1',
                    'description': 'Primary GSM gateway for main office operations',
                    'is_primary': True
                },
                {
                    'name': 'Branch Office Gateway',
                    'ip_address': '*************',
                    'api_port': '80', 
                    'tg_sms_port': '5038',
                    'username': 'branchuser',
                    'password': 'branchpass',
                    'max_ports': 4,
                    'location': 'Branch Office - Reception',
                    'description': 'Secondary GSM gateway for branch office',
                    'is_primary': False
                },
                {
                    'name': 'Backup Gateway',
                    'ip_address': '*************',
                    'api_port': '80',
                    'tg_sms_port': '5038', 
                    'username': 'backupuser',
                    'password': 'backuppass',
                    'max_ports': 16,
                    'location': 'Data Center',
                    'description': 'High-capacity backup gateway for peak loads',
                    'is_primary': False
                }
            ]
            
            created_gateways = []
            
            for gw_config in gateways_config:
                # Check if gateway already exists
                existing = GSMGateway.query.filter_by(ip_address=gw_config['ip_address']).first()
                
                if existing:
                    print(f"⚠️ Gateway {gw_config['name']} already exists")
                    gateway = existing
                else:
                    gateway = GSMGateway(**gw_config)
                    db.session.add(gateway)
                    db.session.flush()  # Get the ID
                    print(f"✅ Created gateway: {gw_config['name']}")
                
                # Create ports for this gateway
                for port_num in range(1, gw_config['max_ports'] + 1):
                    existing_port = EnhancedSMSPort.query.filter_by(
                        gateway_id=gateway.id, 
                        port_number=str(port_num)
                    ).first()
                    
                    if not existing_port:
                        port = EnhancedSMSPort(
                            gateway_id=gateway.id,
                            port_number=str(port_num),
                            status='active',
                            network_name=f'Network_{port_num}',
                            signal_quality='85%',
                            sim_phone_number=f'+639{gateway.id:02d}{port_num:02d}123456',
                            is_active=True
                        )
                        db.session.add(port)
                        print(f"  ➕ Added port {port_num}")
                
                created_gateways.append(gateway)
            
            db.session.commit()
            
            # Show summary
            print(f"\n📊 Multi-GSM Configuration Summary:")
            for gateway in created_gateways:
                ports = EnhancedSMSPort.query.filter_by(gateway_id=gateway.id).all()
                print(f"  🏢 {gateway.name}:")
                print(f"     IP: {gateway.ip_address}")
                print(f"     Ports: {len(ports)}/{gateway.max_ports}")
                print(f"     Location: {gateway.location}")
                print(f"     Primary: {'Yes' if gateway.is_primary else 'No'}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error setting up multi-GSM: {str(e)}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("🚀 Multi-GSM Gateway Management System Setup")
    print("=" * 55)
    
    # Create tables
    if create_multi_gsm_tables():
        # Set up sample configuration
        setup_sample_multi_gsm_configuration()
        
        print(f"\n🎉 Multi-GSM system setup complete!")
        print(f"✅ Support for multiple GSM gateways")
        print(f"✅ Advanced port assignment system")
        print(f"✅ User-to-port mapping with priorities")
        print(f"✅ Gateway management and monitoring")
    else:
        print(f"❌ Failed to set up multi-GSM system")
