#!/usr/bin/env python3
"""
Create test SMS data for 8-port system demonstration
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSMessage
import uuid
from datetime import datetime, timedelta
import random

def create_8port_test_data():
    with app.app_context():
        print("📱 Creating Test SMS Data for 8-Port System")
        print("=" * 50)
        
        # Define test messages for each port/team
        test_messages = [
            # Sales Team (Ports 1-2)
            {
                'port': '1',
                'team': 'Sales',
                'messages': [
                    {'direction': 'inbound', 'phone': '+************', 'content': 'Hi, I\'m interested in your product pricing'},
                    {'direction': 'outbound', 'phone': '+************', 'content': 'Thank you for your interest! Our sales rep will contact you shortly.'},
                    {'direction': 'inbound', 'phone': '+************', 'content': 'Can you send me a quote for 100 units?'},
                    {'direction': 'outbound', 'phone': '+************', 'content': 'Quote sent via email. Valid for 30 days.'}
                ]
            },
            {
                'port': '2', 
                'team': 'Sales',
                'messages': [
                    {'direction': 'inbound', 'phone': '+639182345678', 'content': 'Is there a discount for bulk orders?'},
                    {'direction': 'outbound', 'phone': '+639182345678', 'content': 'Yes! 10% discount for orders over 50 units.'}
                ]
            },
            
            # Support Team (Ports 3-4)
            {
                'port': '3',
                'team': 'Support', 
                'messages': [
                    {'direction': 'inbound', 'phone': '+639193456789', 'content': 'My order #12345 hasn\'t arrived yet'},
                    {'direction': 'outbound', 'phone': '+639193456789', 'content': 'Checking your order status. Will update you in 1 hour.'},
                    {'direction': 'inbound', 'phone': '+639194567890', 'content': 'How do I reset my password?'},
                    {'direction': 'outbound', 'phone': '+639194567890', 'content': 'Password reset link sent to your email.'}
                ]
            },
            {
                'port': '4',
                'team': 'Support',
                'messages': [
                    {'direction': 'inbound', 'phone': '+639195678901', 'content': 'Product not working as expected'},
                    {'direction': 'outbound', 'phone': '+639195678901', 'content': 'Sorry to hear that. Technical team will call you today.'}
                ]
            },
            
            # Marketing Team (Ports 5-6)
            {
                'port': '5',
                'team': 'Marketing',
                'messages': [
                    {'direction': 'outbound', 'phone': '+639196789012', 'content': '🎉 FLASH SALE! 20% off all items today only. Use code FLASH20'},
                    {'direction': 'inbound', 'phone': '+639196789012', 'content': 'STOP'},
                    {'direction': 'outbound', 'phone': '+639197890123', 'content': 'New product launch next week! Be the first to know.'}
                ]
            },
            {
                'port': '6',
                'team': 'Marketing', 
                'messages': [
                    {'direction': 'outbound', 'phone': '+639198901234', 'content': 'Thank you for subscribing to our newsletter!'},
                    {'direction': 'inbound', 'phone': '+639198901234', 'content': 'When is the next sale?'},
                    {'direction': 'outbound', 'phone': '+639198901234', 'content': 'Next sale is planned for month-end. Stay tuned!'}
                ]
            },
            
            # Operations Team (Ports 7-8)
            {
                'port': '7',
                'team': 'Operations',
                'messages': [
                    {'direction': 'outbound', 'phone': '+639199012345', 'content': 'ALERT: Server maintenance scheduled for tonight 11PM-1AM'},
                    {'direction': 'outbound', 'phone': '+639190123456', 'content': 'Delivery truck #5 has arrived at warehouse'}
                ]
            },
            {
                'port': '8',
                'team': 'Operations',
                'messages': [
                    {'direction': 'inbound', 'phone': '+639191234567', 'content': 'Inventory count completed for Zone A'},
                    {'direction': 'outbound', 'phone': '+639191234567', 'content': 'Received. Please proceed with Zone B count.'},
                    {'direction': 'outbound', 'phone': '+639192345678', 'content': 'Monthly report ready for review'}
                ]
            }
        ]
        
        created_count = 0
        
        for port_data in test_messages:
            port = port_data['port']
            team = port_data['team']
            
            print(f"\n📞 Creating messages for Port {port} ({team} Team):")
            
            for i, msg_data in enumerate(port_data['messages']):
                # Create unique message ID
                message_id = f"TEST_{port}_{team}_{i}_{uuid.uuid4().hex[:8]}"
                
                # Create message with timestamp spread over last few hours
                created_time = datetime.now() - timedelta(hours=random.randint(1, 24))
                
                message = SMSMessage(
                    message_id=message_id,
                    direction=msg_data['direction'],
                    phone_number=msg_data['phone'],
                    content=msg_data['content'],
                    status='received' if msg_data['direction'] == 'inbound' else 'sent',
                    gsm_port=port,
                    created_at=created_time
                )
                
                db.session.add(message)
                created_count += 1
                
                direction_icon = "📥" if msg_data['direction'] == 'inbound' else "📤"
                print(f"   {direction_icon} {msg_data['phone']}: {msg_data['content'][:50]}...")
        
        db.session.commit()
        
        print(f"\n🎉 Successfully created {created_count} test messages!")
        
        # Show port statistics
        print(f"\n📊 Port Statistics:")
        for i in range(1, 9):
            port_str = str(i)
            total = SMSMessage.query.filter_by(gsm_port=port_str).count()
            inbound = SMSMessage.query.filter_by(gsm_port=port_str, direction='inbound').count()
            outbound = SMSMessage.query.filter_by(gsm_port=port_str, direction='outbound').count()
            
            if total > 0:
                print(f"   Port {i}: {total} total ({inbound} in, {outbound} out)")

def test_port_filtering():
    """Test port-based message filtering"""
    with app.app_context():
        print(f"\n🔒 Testing Port-Based Message Filtering")
        print("=" * 45)
        
        from app import User
        
        # Test each team's access
        teams = ['sales_team', 'support_team', 'marketing_team', 'operations_team']
        
        for team_name in teams:
            user = User.query.filter_by(username=team_name).first()
            if user:
                assigned_ports = user.get_assigned_ports()
                
                # Get messages this user can see
                if assigned_ports:
                    inbox_messages = SMSMessage.query.filter_by(direction='inbound').filter(
                        SMSMessage.gsm_port.in_(assigned_ports)
                    ).all()
                    outbox_messages = SMSMessage.query.filter_by(direction='outbound').filter(
                        SMSMessage.gsm_port.in_(assigned_ports)
                    ).all()
                else:
                    inbox_messages = SMSMessage.query.filter_by(direction='inbound').all()
                    outbox_messages = SMSMessage.query.filter_by(direction='outbound').all()
                
                print(f"\n👤 {team_name} (Ports {assigned_ports}):")
                print(f"   📥 Inbox: {len(inbox_messages)} messages")
                print(f"   📤 Outbox: {len(outbox_messages)} messages")
                
                # Show sample messages
                if inbox_messages:
                    print(f"   Sample inbox message: {inbox_messages[0].content[:40]}...")
                if outbox_messages:
                    print(f"   Sample outbox message: {outbox_messages[0].content[:40]}...")

if __name__ == "__main__":
    create_8port_test_data()
    test_port_filtering()
