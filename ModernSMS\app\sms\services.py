"""
SMS service classes for handling SMS operations
"""
import requests
import uuid
import re
from datetime import datetime
from flask import current_app
from app.models import SMSMessage, GSMGateway, SMSPort
from app import db

class SMSService:
    """Service for SMS operations"""
    
    @staticmethod
    def validate_phone_number(phone_number):
        """Validate phone number format"""
        # Remove all non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)
        
        # Check if it's a valid international format
        if cleaned.startswith('+'):
            # International format: +1234567890 (7-15 digits after +)
            return re.match(r'^\+\d{7,15}$', cleaned) is not None
        else:
            # Local format: assume 10-11 digits
            return re.match(r'^\d{7,15}$', cleaned) is not None
    
    @staticmethod
    def normalize_phone_number(phone_number):
        """Normalize phone number to standard format"""
        # Remove all non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)
        
        # If it doesn't start with +, assume it's local and add country code if needed
        if not cleaned.startswith('+'):
            # You can customize this based on your country
            if len(cleaned) == 10:  # US format
                cleaned = '+1' + cleaned
            elif len(cleaned) == 11 and cleaned.startswith('1'):
                cleaned = '+' + cleaned
            else:
                cleaned = '+' + cleaned
        
        return cleaned
    
    @staticmethod
    def send_sms(phone_number, message, port, user):
        """Send SMS through GSM gateway"""
        try:
            # Normalize phone number
            normalized_number = SMSService.normalize_phone_number(phone_number)
            
            # Get gateway configuration
            gateway = port.gateway
            if not gateway or gateway.status != 'active':
                return {'success': False, 'error': 'Gateway is not available'}
            
            # Generate unique message ID
            message_id = str(uuid.uuid4())
            
            # Create SMS message record
            sms_message = SMSMessage(
                message_id=message_id,
                direction='outbound',
                phone_number=normalized_number,
                content=message,
                status='pending',
                gateway_id=gateway.id,
                port_id=port.id,
                user_id=user.id,
                created_at=datetime.now()
            )
            
            db.session.add(sms_message)
            db.session.commit()
            
            # Send SMS via gateway API
            result = SMSService._send_via_gateway(gateway, port, normalized_number, message, message_id)
            
            # Update message status
            if result['success']:
                sms_message.status = 'sent'
                sms_message.sent_at = datetime.now()
            else:
                sms_message.status = 'failed'
            
            db.session.commit()
            
            return {
                'success': result['success'],
                'message_id': message_id,
                'error': result.get('error')
            }
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"SMS send error: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def _send_via_gateway(gateway, port, phone_number, message, message_id):
        """Send SMS via GSM gateway API"""
        try:
            # Construct API URL (same format as original system)
            url = f"http://{gateway.ip_address}:{gateway.api_port}/cgi/WebCGI"
            
            # Prepare parameters
            params = {
                '1500101': f"account={gateway.username}&password={gateway.password}&port={port.port_number}&destination={phone_number}&content={message}&id={message_id}"
            }
            
            # Send request
            response = requests.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                response_text = response.text.lower()
                
                # Check for success indicators
                if 'ok' in response_text or 'success' in response_text:
                    return {'success': True}
                else:
                    return {'success': False, 'error': f'Gateway error: {response.text}'}
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}: {response.text}'}
                
        except requests.exceptions.Timeout:
            return {'success': False, 'error': 'Gateway timeout'}
        except requests.exceptions.ConnectionError:
            return {'success': False, 'error': 'Cannot connect to gateway'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

class GSMPortService:
    """Service for GSM port operations"""
    
    @staticmethod
    def get_user_available_ports(user):
        """Get available ports for user"""
        try:
            if user.has_permission('manage_users'):
                # Admin can see all active ports
                return SMSPort.query.filter_by(is_active=True).all()
            else:
                # Regular user sees assigned ports
                assigned_ports = user.get_assigned_ports()
                if not assigned_ports:
                    # If no specific assignments, show all active ports
                    return SMSPort.query.filter_by(is_active=True).all()
                
                port_ids = [p.get('port_id') for p in assigned_ports if p.get('port_id')]
                return SMSPort.query.filter(SMSPort.id.in_(port_ids), SMSPort.is_active == True).all()
                
        except Exception as e:
            current_app.logger.error(f"Error getting user ports: {str(e)}")
            return []
    
    @staticmethod
    def detect_port_status(gateway, port_number):
        """Detect real port status from hardware"""
        try:
            # Test SIM card presence and network registration
            url = f"http://{gateway.ip_address}:{gateway.api_port}/cgi/WebCGI"
            params = {
                '1500102': f"account={gateway.username}&password={gateway.password}&port={port_number}"
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                response_text = response.text.lower()
                
                # Parse response for status information
                has_sim = 'sim' in response_text and 'error' not in response_text
                network_registered = 'registered' in response_text or 'connected' in response_text
                
                # Extract additional information
                network_name = 'Unknown'
                signal_strength = 'Unknown'
                phone_number = 'Unknown'
                
                if 'network:' in response_text:
                    network_name = response_text.split('network:')[1].split(',')[0].strip()
                if 'signal:' in response_text:
                    signal_strength = response_text.split('signal:')[1].split(',')[0].strip()
                if 'number:' in response_text:
                    phone_number = response_text.split('number:')[1].split(',')[0].strip()
                
                # Determine status
                if has_sim and network_registered:
                    status = 'active'
                elif has_sim:
                    status = 'sim_only'
                else:
                    status = 'no_sim'
                
                return {
                    'status': status,
                    'network_name': network_name,
                    'signal_strength': signal_strength,
                    'phone_number': phone_number,
                    'has_sim': has_sim,
                    'network_registered': network_registered
                }
            else:
                return {
                    'status': 'error',
                    'network_name': 'Unknown',
                    'signal_strength': 'Unknown',
                    'phone_number': 'Unknown',
                    'has_sim': False,
                    'network_registered': False
                }
                
        except Exception as e:
            current_app.logger.error(f"Port detection error for {gateway.name}:{port_number}: {str(e)}")
            return {
                'status': 'unknown',
                'network_name': 'Unknown',
                'signal_strength': 'Unknown',
                'phone_number': 'Unknown',
                'has_sim': False,
                'network_registered': False
            }
    
    @staticmethod
    def refresh_all_ports():
        """Refresh status for all ports"""
        try:
            updated_count = 0
            
            for port in SMSPort.query.filter_by(is_active=True).all():
                if port.gateway and port.gateway.status == 'active':
                    status_info = GSMPortService.detect_port_status(port.gateway, port.port_number)
                    
                    # Update port information
                    port.status = status_info['status']
                    port.network_name = status_info['network_name']
                    port.signal_strength = status_info['signal_strength']
                    port.phone_number = status_info['phone_number']
                    port.last_checked = datetime.now()
                    
                    updated_count += 1
            
            db.session.commit()
            return {'success': True, 'updated_count': updated_count}
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error refreshing ports: {str(e)}")
            return {'success': False, 'error': str(e)}
