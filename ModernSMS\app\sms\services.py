"""
SMS service classes for handling SMS operations
"""
import requests
import uuid
import re
import os
from datetime import datetime
from flask import current_app
from app.models import SMSMessage, GSMGateway, SMSPort
from app import db

class SMSService:
    """Service for SMS operations"""
    
    @staticmethod
    def validate_phone_number(phone_number):
        """Validate phone number format - accepts any format"""
        # Remove all non-digit characters
        cleaned = re.sub(r'[^\d]', '', phone_number)

        # Must have at least 7 digits (minimum for local numbers)
        if len(cleaned) < 7:
            return False

        # Must be all digits and reasonable length (max 15 digits)
        return cleaned.isdigit() and len(cleaned) <= 15

    @staticmethod
    def normalize_phone_number(phone_number):
        """Normalize phone number - keep as-is, just clean it"""
        # Remove all non-digit characters (spaces, dashes, parentheses, etc.)
        cleaned = re.sub(r'[^\d]', '', phone_number)

        # Remove leading zeros if any (but keep at least one digit)
        while cleaned.startswith('0') and len(cleaned) > 1:
            cleaned = cleaned[1:]

        # If empty after cleaning, return original
        if not cleaned:
            return phone_number

        return cleaned
    
    @staticmethod
    def send_sms(phone_number, message, port, user):
        """Send SMS through GSM gateway with HTTP API validation"""
        try:
            # Normalize phone number
            normalized_number = SMSService.normalize_phone_number(phone_number)

            # STRICT gateway validation with connectivity test
            gateway = port.gateway
            if not gateway:
                return {'success': False, 'error': 'No gateway associated with port'}

            if gateway.status != 'active':
                return {'success': False, 'error': f'Gateway "{gateway.name}" is not active (status: {gateway.status})'}

            # Basic port validation
            if not port.is_active:
                return {'success': False, 'error': f'Port {port.port_number} is not active'}

            # Generate unique message ID
            message_id = str(uuid.uuid4())

            # Create SMS message record
            sms_message = SMSMessage(
                message_id=message_id,
                direction='outbound',
                phone_number=normalized_number,
                content=message,
                status='pending',
                gateway_id=gateway.id,
                port_id=port.id,
                user_id=user.id,
                created_at=datetime.now()
            )

            db.session.add(sms_message)
            db.session.commit()

            # Send SMS via gateway API and WAIT for GSM response
            current_app.logger.info(f"Sending SMS to GSM gateway and waiting for response...")
            result = SMSService._send_via_gateway(gateway, port, normalized_number, message, message_id)

            # Update message status based on ACTUAL GSM response
            if result['success']:
                # Check if we got a proper GSM response
                if result.get('response') and not result.get('dev_mode'):
                    sms_message.status = 'sent'
                    sms_message.sent_at = datetime.now()
                    current_app.logger.info(f"SMS CONFIRMED by GSM: {message_id} to {normalized_number} - Response: {result.get('response')}")
                elif result.get('dev_mode'):
                    sms_message.status = 'sent'  # In dev mode, mark as sent for testing
                    sms_message.sent_at = datetime.now()
                    current_app.logger.info(f"SMS sent in DEV MODE: {message_id}")
                else:
                    sms_message.status = 'pending'
                    current_app.logger.warning(f"SMS submitted but no clear confirmation: {message_id}")
            else:
                sms_message.status = 'failed'
                current_app.logger.error(f"SMS send failed: {message_id} - {result.get('error')}")

            db.session.commit()

            return {
                'success': result['success'],
                'message_id': message_id,
                'error': result.get('error'),
                'gateway_info': {
                    'name': gateway.name,
                    'ip': gateway.ip_address,
                    'port_number': port.port_number
                }
            }

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"SMS send error: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def _send_via_gateway(gateway, port, phone_number, message, message_id):
        """Send SMS via GSM gateway API with STRICT HTTP format"""
        try:
            from flask import current_app
            # Check if we're in production mode
            is_development = current_app.config.get('FLASK_ENV', 'development') == 'development'
            is_production = os.environ.get('FLASK_ENV') == 'production'

            # Override if environment variable is set to production
            if is_production:
                is_development = False

            # Construct the EXACT HTTP API URL as specified
            # Format: http://[IP]/cgi/WebCGI?1500101=account=apiuser&password=apipass&port=[port]&destination=[phone_number]&content=[MSG]
            base_url = f"http://{gateway.ip_address}/cgi/WebCGI"

            # URL encode the message content to handle special characters
            import urllib.parse
            encoded_message = urllib.parse.quote(message)

            # Build the parameter string exactly as specified
            param_string = f"account={gateway.username}&password={gateway.password}&port={port.port_number}&destination={phone_number}&content={encoded_message}"

            # Complete URL with the 1500101 parameter
            full_url = f"{base_url}?1500101={param_string}"

            current_app.logger.info(f"SMS API CALL: {full_url}")

            if is_development:
                # Development mode - simulate but show the actual API call
                current_app.logger.info(f"DEV MODE: Would send SMS via HTTP API")
                current_app.logger.info(f"DEV MODE: Gateway: {gateway.name} ({gateway.ip_address})")
                current_app.logger.info(f"DEV MODE: Port: {port.port_number}")
                current_app.logger.info(f"DEV MODE: Destination: {phone_number}")
                current_app.logger.info(f"DEV MODE: Message: {message}")
                current_app.logger.info(f"DEV MODE: Full URL: {full_url}")

                # Simulate success with 90% rate
                import random
                if random.random() > 0.1:
                    return {
                        'success': True,
                        'dev_mode': True,
                        'api_url': full_url,
                        'response': 'OK (simulated)'
                    }
                else:
                    return {
                        'success': False,
                        'error': 'Simulated gateway error for testing',
                        'dev_mode': True,
                        'api_url': full_url
                    }

            # PRODUCTION MODE - Real HTTP API call
            try:
                current_app.logger.info(f"PRODUCTION: Sending HTTP request to gateway")

                # Send GET request to the gateway
                response = requests.get(full_url, timeout=30)

                current_app.logger.info(f"Gateway Response: Status={response.status_code}, Body='{response.text}'")

                if response.status_code == 200:
                    response_text = response.text.strip()

                    # Check for success indicators in response
                    if ('OK' in response_text.upper() or
                        'SUCCESS' in response_text.upper() or
                        'SENT' in response_text.upper() or
                        response_text.startswith('0')):  # Some gateways return "0" for success

                        return {
                            'success': True,
                            'response': response_text,
                            'api_url': full_url
                        }
                    else:
                        # Gateway returned an error
                        return {
                            'success': False,
                            'error': f'Gateway error: {response_text}',
                            'api_url': full_url
                        }
                else:
                    return {
                        'success': False,
                        'error': f'HTTP {response.status_code}: {response.text}',
                        'api_url': full_url
                    }

            except requests.exceptions.Timeout:
                return {
                    'success': False,
                    'error': f'Gateway timeout - no response within 30 seconds from {gateway.ip_address}',
                    'api_url': full_url
                }
            except requests.exceptions.ConnectionError:
                return {
                    'success': False,
                    'error': f'Cannot connect to gateway at {gateway.ip_address} - check IP address and network',
                    'api_url': full_url
                }
            except Exception as e:
                return {
                    'success': False,
                    'error': f'Request error: {str(e)}',
                    'api_url': full_url
                }

        except Exception as e:
            current_app.logger.error(f"SMS Gateway API Error: {str(e)}")
            return {'success': False, 'error': f'API error: {str(e)}'}

    @staticmethod
    def _test_gsm_connectivity(gateway):
        """Test if GSM gateway is actually reachable and responding"""
        try:
            from flask import current_app
            is_development = current_app.config.get('FLASK_ENV') == 'development'

            if is_development:
                current_app.logger.info(f"DEV MODE: Simulating GSM connectivity test for {gateway.name}")
                # In development, simulate connectivity
                return {
                    'connected': True,
                    'response_time': 0.1,
                    'dev_mode': True
                }

            # PRODUCTION: Test actual connectivity
            current_app.logger.info(f"PRODUCTION: Testing connectivity to GSM gateway {gateway.ip_address}")

            # Use a simple status check command to test connectivity
            test_url = f"http://{gateway.ip_address}/cgi/WebCGI?1500102=account={gateway.username}&password={gateway.password}&port=1"

            import time
            start_time = time.time()

            try:
                response = requests.get(test_url, timeout=10)
                response_time = time.time() - start_time

                current_app.logger.info(f"GSM Gateway Response: Status={response.status_code}, Time={response_time:.2f}s")

                if response.status_code == 200:
                    # Check if response indicates authentication success
                    if 'unauthorized' in response.text.lower() or 'invalid' in response.text.lower():
                        return {
                            'connected': False,
                            'error': f'Authentication failed - check username/password for gateway {gateway.name}'
                        }

                    return {
                        'connected': True,
                        'response_time': response_time,
                        'gateway_response': response.text[:100]  # First 100 chars
                    }
                else:
                    return {
                        'connected': False,
                        'error': f'HTTP {response.status_code} from gateway {gateway.ip_address}'
                    }

            except requests.exceptions.Timeout:
                return {
                    'connected': False,
                    'error': f'Gateway {gateway.ip_address} timeout - no response within 10 seconds'
                }
            except requests.exceptions.ConnectionError:
                return {
                    'connected': False,
                    'error': f'Cannot connect to gateway {gateway.ip_address} - check IP address and network'
                }

        except Exception as e:
            current_app.logger.error(f"GSM connectivity test error: {str(e)}")
            return {
                'connected': False,
                'error': f'Connectivity test failed: {str(e)}'
            }

    @staticmethod
    def _validate_gsm_port(gateway, port):
        """Validate that the specific port is ready on the GSM hardware"""
        try:
            from flask import current_app
            is_development = current_app.config.get('FLASK_ENV') == 'development'

            if is_development:
                current_app.logger.info(f"DEV MODE: Simulating port validation for port {port.port_number}")
                # In development, simulate port validation
                return {
                    'ready': True,
                    'sim_status': 'active',
                    'network': 'Test Network',
                    'signal': '75%',
                    'dev_mode': True
                }

            # PRODUCTION: Check actual port status
            current_app.logger.info(f"PRODUCTION: Checking port {port.port_number} status on {gateway.ip_address}")

            # Use port status command
            port_check_url = f"http://{gateway.ip_address}/cgi/WebCGI?1500102=account={gateway.username}&password={gateway.password}&port={port.port_number}"

            try:
                response = requests.get(port_check_url, timeout=15)

                current_app.logger.info(f"Port {port.port_number} Response: {response.text}")

                if response.status_code == 200:
                    response_text = response.text.lower()

                    # Check for SIM card presence
                    if 'no sim' in response_text or 'sim error' in response_text:
                        return {
                            'ready': False,
                            'error': f'Port {port.port_number} has no SIM card or SIM error'
                        }

                    # Check for network registration
                    if 'not registered' in response_text or 'no network' in response_text:
                        return {
                            'ready': False,
                            'error': f'Port {port.port_number} SIM not registered to network'
                        }

                    # Check for port errors
                    if 'port error' in response_text or 'hardware error' in response_text:
                        return {
                            'ready': False,
                            'error': f'Port {port.port_number} hardware error'
                        }

                    # If we get here, port seems ready
                    return {
                        'ready': True,
                        'response': response.text
                    }
                else:
                    return {
                        'ready': False,
                        'error': f'Port check failed: HTTP {response.status_code}'
                    }

            except requests.exceptions.Timeout:
                return {
                    'ready': False,
                    'error': f'Port {port.port_number} check timeout'
                }
            except requests.exceptions.ConnectionError:
                return {
                    'ready': False,
                    'error': f'Cannot connect to gateway for port check'
                }

        except Exception as e:
            current_app.logger.error(f"Port validation error: {str(e)}")
            return {
                'ready': False,
                'error': f'Port validation failed: {str(e)}'
            }

class GSMPortService:
    """Service for GSM port operations"""
    
    @staticmethod
    def get_user_available_ports(user):
        """Get available ports for user - STRICT port assignment enforcement"""
        try:
            if user.has_permission('manage_users'):
                # Admin can see all active ports
                current_app.logger.info(f"Admin user {user.username} - showing all active ports")
                return SMSPort.query.filter_by(is_active=True).all()
            else:
                # Regular user sees ONLY assigned ports
                assigned_ports = user.get_assigned_ports()
                current_app.logger.info(f"User {user.username} assigned ports: {assigned_ports}")

                if not assigned_ports:
                    # If no specific assignments, show NO ports (strict enforcement)
                    current_app.logger.warning(f"User {user.username} has no assigned ports - showing empty list")
                    return []

                # Build list of port IDs from assignments
                port_filters = []
                for assignment in assigned_ports:
                    gateway_id = assignment.get('gateway_id')
                    port_number = assignment.get('port')

                    current_app.logger.debug(f"Processing assignment: gateway_id={gateway_id}, port={port_number}")

                    if gateway_id and port_number:
                        # Find port by gateway_id and port_number
                        port = SMSPort.query.filter_by(
                            gateway_id=gateway_id,
                            port_number=str(port_number),
                            is_active=True
                        ).first()
                        if port:
                            port_filters.append(port.id)
                            current_app.logger.debug(f"Found matching port: {port.id} (Gateway {gateway_id}, Port {port_number})")
                        else:
                            current_app.logger.warning(f"No active port found for Gateway {gateway_id}, Port {port_number}")

                if not port_filters:
                    current_app.logger.warning(f"User {user.username} - no valid assigned ports found")
                    return []

                ports = SMSPort.query.filter(SMSPort.id.in_(port_filters)).all()
                current_app.logger.info(f"User {user.username} - found {len(ports)} available ports: {[f'GW{p.gateway_id}:P{p.port_number}' for p in ports]}")
                return ports

        except Exception as e:
            current_app.logger.error(f"Error getting user ports: {str(e)}")
            return []
    
    @staticmethod
    def detect_port_status(gateway, port_number):
        """Detect real port status from hardware"""
        try:
            # Test SIM card presence and network registration
            url = f"http://{gateway.ip_address}:{gateway.api_port}/cgi/WebCGI"
            params = {
                '1500102': f"account={gateway.username}&password={gateway.password}&port={port_number}"
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                response_text = response.text.lower()
                
                # Parse response for status information
                has_sim = 'sim' in response_text and 'error' not in response_text
                network_registered = 'registered' in response_text or 'connected' in response_text
                
                # Extract additional information
                network_name = 'Unknown'
                signal_strength = 'Unknown'
                phone_number = 'Unknown'
                
                if 'network:' in response_text:
                    network_name = response_text.split('network:')[1].split(',')[0].strip()
                if 'signal:' in response_text:
                    signal_strength = response_text.split('signal:')[1].split(',')[0].strip()
                if 'number:' in response_text:
                    phone_number = response_text.split('number:')[1].split(',')[0].strip()
                
                # Determine status
                if has_sim and network_registered:
                    status = 'active'
                elif has_sim:
                    status = 'sim_only'
                else:
                    status = 'no_sim'
                
                return {
                    'status': status,
                    'network_name': network_name,
                    'signal_strength': signal_strength,
                    'phone_number': phone_number,
                    'has_sim': has_sim,
                    'network_registered': network_registered
                }
            else:
                return {
                    'status': 'error',
                    'network_name': 'Unknown',
                    'signal_strength': 'Unknown',
                    'phone_number': 'Unknown',
                    'has_sim': False,
                    'network_registered': False
                }
                
        except Exception as e:
            current_app.logger.error(f"Port detection error for {gateway.name}:{port_number}: {str(e)}")
            return {
                'status': 'unknown',
                'network_name': 'Unknown',
                'signal_strength': 'Unknown',
                'phone_number': 'Unknown',
                'has_sim': False,
                'network_registered': False
            }
    
    @staticmethod
    def refresh_all_ports():
        """Refresh status for all ports"""
        try:
            updated_count = 0
            
            for port in SMSPort.query.filter_by(is_active=True).all():
                if port.gateway and port.gateway.status == 'active':
                    status_info = GSMPortService.detect_port_status(port.gateway, port.port_number)
                    
                    # Update port information
                    port.status = status_info['status']
                    port.network_name = status_info['network_name']
                    port.signal_strength = status_info['signal_strength']
                    port.phone_number = status_info['phone_number']
                    port.last_checked = datetime.now()
                    
                    updated_count += 1
            
            db.session.commit()
            return {'success': True, 'updated_count': updated_count}
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error refreshing ports: {str(e)}")
            return {'success': False, 'error': str(e)}
