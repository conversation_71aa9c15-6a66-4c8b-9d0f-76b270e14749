#!/usr/bin/env python3
"""
Check users in the database
"""

import sys
import os
sys.path.append('backend')

from app import app, db, User

def check_users():
    with app.app_context():
        users = User.query.all()
        print(f"Found {len(users)} users in database:")
        
        for user in users:
            print(f"\n👤 User: {user.username}")
            print(f"   Email: {user.email}")
            print(f"   Role: {user.role}")
            print(f"   Active: {user.is_active}")
            print(f"   Assigned Ports: {user.get_assigned_ports()}")
            print(f"   Permissions:")
            print(f"     can_send_sms: {user.can_send_sms}")
            print(f"     can_view_inbox: {user.can_view_inbox}")
            print(f"     can_view_outbox: {user.can_view_outbox}")
            print(f"     can_export: {user.can_export}")
            print(f"     can_manage_users: {user.can_manage_users}")
            print(f"     can_view_reports: {user.can_view_reports}")
            print(f"     can_manage_settings: {user.can_manage_settings}")
            print(f"     can_manage_ports: {user.can_manage_ports}")

if __name__ == "__main__":
    check_users()
