#!/usr/bin/env python3
"""
Test gateway ports and TG SMS service
"""
import socket
import time

def test_ports():
    """Test common TG SMS ports"""
    print("="*50)
    print("🔍 TESTING GATEWAY PORTS")
    print("="*50)
    
    gateway_ip = "*************"
    ports_to_test = [5038, 5039, 5040, 5041, 5042]
    
    print(f"📡 Testing gateway: {gateway_ip}")
    
    for port in ports_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((gateway_ip, port))
            
            if result == 0:
                print(f"✅ Port {port}: OPEN")
                
                # Try to get initial response
                try:
                    sock.settimeout(2)
                    response = sock.recv(1024).decode()
                    if response:
                        print(f"   📥 Response: {response.strip()}")
                except:
                    print(f"   📥 No initial response")
            else:
                print(f"❌ Port {port}: CLOSED")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Port {port}: ERROR - {str(e)}")

def test_tg_sms_auth():
    """Test TG SMS authentication on different ports"""
    print(f"\n🔐 TESTING TG SMS AUTHENTICATION")
    print("="*50)
    
    gateway_ip = "*************"
    username = "apiuser"
    password = "apipass"
    
    # Test ports where we found open connections
    test_ports = [5038, 5039]
    
    for port in test_ports:
        print(f"\n📱 Testing TG SMS on port {port}...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway_ip, port))
            
            # Read initial response
            initial = sock.recv(1024).decode()
            print(f"   📥 Initial: {initial.strip()}")
            
            # Send login
            login_cmd = f"Action: Login\r\nUsername: {username}\r\nSecret: {password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            # Read login response
            login_resp = sock.recv(1024).decode()
            print(f"   📥 Login: {login_resp.strip()}")
            
            if "Response: Success" in login_resp:
                print(f"   ✅ Authentication successful on port {port}!")
                
                # Test event subscription
                event_cmd = "Action: Events\r\nEventMask: sms\r\n\r\n"
                sock.send(event_cmd.encode())
                
                event_resp = sock.recv(1024).decode()
                print(f"   📥 Events: {event_resp.strip()}")
                
                sock.close()
                return port  # Return successful port
            else:
                print(f"   ❌ Authentication failed on port {port}")
            
            sock.close()
            
        except Exception as e:
            print(f"   ❌ Error on port {port}: {str(e)}")
    
    return None

if __name__ == '__main__':
    print("🚀 Gateway Port and TG SMS Test")
    
    # Test which ports are open
    test_ports()
    
    # Test TG SMS authentication
    working_port = test_tg_sms_auth()
    
    print(f"\n" + "="*50)
    print("🎯 RESULTS:")
    print("="*50)
    
    if working_port:
        print(f"✅ TG SMS working on port {working_port}")
        print(f"💡 Update your gateway settings to use port {working_port}")
    else:
        print(f"❌ TG SMS authentication failed on all ports")
        print(f"🔧 Possible issues:")
        print(f"   1. TG SMS service not enabled on gateway")
        print(f"   2. Wrong username/password")
        print(f"   3. TG SMS running on different port")
        print(f"   4. Gateway firewall blocking connections")
    
    print("="*50)
