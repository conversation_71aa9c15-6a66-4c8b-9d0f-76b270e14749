#!/usr/bin/env python3
"""
Complete Multi-GSM Removal
This script completely removes ALL multi-GSM functionality from the entire system
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app import app, db, GSMGateway, SMSPort, User

def complete_multi_gsm_removal():
    """Completely remove ALL multi-GSM functionality"""
    print("🔥 COMPLETE MULTI-GSM REMOVAL")
    print("=" * 60)
    
    with app.app_context():
        try:
            # Step 1: Complete database wipe and rebuild
            print("📋 Step 1: Complete database wipe...")
            
            # Delete EVERYTHING
            SMSPort.query.delete()
            GSMGateway.query.delete()
            
            db.session.commit()
            print("   🗑️ Deleted ALL gateways and ports")
            
            # Step 2: Create ONLY original single gateway
            print("\n📋 Step 2: Creating ONLY original single gateway...")
            
            original_gateway = GSMGateway(
                name='Main Gateway',
                ip_address='*************',
                api_port='80',
                tg_sms_port='5038',
                username='apiuser',
                password='apipass',
                max_ports=4,  # ONLY 4 ports
                status='active',
                location='Main Office',
                description='Single GSM Gateway - Original Design',
                is_primary=True
            )
            
            db.session.add(original_gateway)
            db.session.flush()
            
            print(f"   ✅ Created ONLY original gateway: {original_gateway.name}")
            
            # Step 3: Create ONLY 4 original ports
            print("\n📋 Step 3: Creating ONLY 4 original ports...")
            
            for port_num in range(1, 5):
                # Port 2 has no SIM
                has_sim = port_num != 2
                
                original_port = SMSPort(
                    port_number=str(port_num),  # Simple: 1, 2, 3, 4
                    status='detected' if has_sim else 'no_sim',
                    network_name='Unknown' if has_sim else 'None',
                    signal_quality='Unknown' if has_sim else 'N/A',
                    is_active=True,
                    gateway_id=original_gateway.id
                )
                
                db.session.add(original_port)
                status_icon = "✅" if has_sim else "❌"
                print(f"   {status_icon} Created ONLY port {port_num}")
            
            db.session.commit()
            
            # Step 4: Reset ALL user assignments to original
            print("\n📋 Step 4: Resetting ALL user assignments...")
            
            users = User.query.all()
            for user in users:
                if user.role == 'admin':
                    user.assigned_ports = '1,2,3,4'  # Admin gets all
                else:
                    user.assigned_ports = '1,3,4'    # Users get working ports
                
                print(f"   👤 Reset {user.username}: {user.assigned_ports}")
            
            db.session.commit()
            
            # Step 5: Verify ONLY original setup
            print("\n📋 Step 5: Verifying ONLY original setup...")
            
            gateways = GSMGateway.query.all()
            ports = SMSPort.query.all()
            
            print(f"   🏢 Gateways: {len(gateways)} (MUST be 1)")
            if len(gateways) != 1:
                print(f"   ❌ ERROR: Found {len(gateways)} gateways, should be 1!")
                return False
            
            print(f"   📱 Ports: {len(ports)} (MUST be 4)")
            if len(ports) != 4:
                print(f"   ❌ ERROR: Found {len(ports)} ports, should be 4!")
                return False
            
            gateway = gateways[0]
            print(f"   ✅ ONLY Gateway: {gateway.name} ({gateway.ip_address})")
            print(f"   ✅ ONLY Max Ports: {gateway.max_ports}")
            
            for port in ports:
                status_icon = "✅" if port.status == 'detected' else "❌"
                print(f"   {status_icon} ONLY Port {port.port_number}: {port.status}")
            
            print(f"\n✅ COMPLETE MULTI-GSM REMOVAL SUCCESSFUL!")
            print(f"📋 ONLY Original Configuration:")
            print(f"   - ONLY 1 GSM Gateway (Main Gateway)")
            print(f"   - ONLY 4 Ports (1,2,3,4)")
            print(f"   - ONLY Port 2 has no SIM")
            print(f"   - ALL multi-GSM complexity REMOVED")
            print(f"   - ONLY original working design")
            
            return True
            
        except Exception as e:
            print(f"❌ Error in complete removal: {str(e)}")
            db.session.rollback()
            return False

def verify_removal():
    """Verify that ALL multi-GSM is removed"""
    print(f"\n🔍 VERIFICATION: Checking for ANY multi-GSM remnants...")
    
    with app.app_context():
        try:
            gateways = GSMGateway.query.all()
            ports = SMSPort.query.all()
            
            # Check gateways
            print(f"📊 Gateway Check:")
            if len(gateways) == 1 and gateways[0].name == 'Main Gateway':
                print(f"   ✅ ONLY 1 gateway: {gateways[0].name}")
            else:
                print(f"   ❌ ERROR: Found {len(gateways)} gateways!")
                for gw in gateways:
                    print(f"      - {gw.name}")
                return False
            
            # Check ports
            print(f"📊 Port Check:")
            if len(ports) == 4:
                print(f"   ✅ ONLY 4 ports found")
                for port in ports:
                    if port.port_number in ['1', '2', '3', '4']:
                        print(f"   ✅ Valid port: {port.port_number}")
                    else:
                        print(f"   ❌ Invalid port: {port.port_number}")
                        return False
            else:
                print(f"   ❌ ERROR: Found {len(ports)} ports, should be 4!")
                return False
            
            # Check for multi-GSM port names
            multi_gsm_ports = [p for p in ports if 'gw' in p.port_number or '_port' in p.port_number]
            if multi_gsm_ports:
                print(f"   ❌ ERROR: Found multi-GSM port names:")
                for port in multi_gsm_ports:
                    print(f"      - {port.port_number}")
                return False
            else:
                print(f"   ✅ NO multi-GSM port names found")
            
            print(f"\n✅ VERIFICATION PASSED: ALL multi-GSM removed!")
            return True
            
        except Exception as e:
            print(f"❌ Verification error: {str(e)}")
            return False

if __name__ == "__main__":
    print("🔥 COMPLETE MULTI-GSM REMOVAL TOOL")
    print("=" * 60)
    print("This will COMPLETELY REMOVE ALL multi-GSM functionality")
    print("and restore ONLY the original single GSM design.")
    print()
    
    try:
        response = input("Proceed with COMPLETE removal? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            success = complete_multi_gsm_removal()
            if success:
                verify_success = verify_removal()
                if verify_success:
                    print(f"\n🎯 COMPLETE REMOVAL SUCCESSFUL!")
                    print(f"1. ALL multi-GSM functionality REMOVED")
                    print(f"2. ONLY original single GSM design remains")
                    print(f"3. Restart Flask application")
                    print(f"4. GUI should show ONLY single gateway")
                    print(f"5. ONLY 4 ports should be visible")
                else:
                    print(f"\n❌ Verification failed!")
            else:
                print(f"\n❌ Removal failed!")
        else:
            print("✅ Operation cancelled.")
    except KeyboardInterrupt:
        print("\n\n✅ Operation cancelled by user.")
