# Modern SMS Manager

A modern, clean, and feature-rich SMS management system built with Flask and MySQL. This system provides real-time GSM gateway management, conversation threading, and a mobile-like interface for SMS communications.

## ✨ Features

### 🎯 **Core Functionality**
- **Dynamic GSM Gateway Configuration** - No hardcoded settings
- **Real-time Port Detection** - Actual hardware status monitoring
- **Conversation Threading** - Mobile-like chat interface
- **User Permission System** - Role-based access control
- **Multi-Gateway Support** - Manage multiple GSM gateways
- **Real-time Updates** - Live status monitoring

### 🔧 **Technical Features**
- **Modern UI/UX** - Clean, responsive design
- **MySQL Database** - Reliable data storage
- **RESTful API** - External integration support
- **Security** - Session management, login attempts limiting
- **Scalable Architecture** - Modular blueprint structure

### 📱 **User Interface**
- **Dashboard** - Real-time statistics and quick actions
- **Compose SMS** - Easy message composition
- **Inbox/Outbox** - Organized message management
- **Conversations** - Threaded message view
- **Gateway Management** - Complete gateway configuration
- **User Profiles** - Account management

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- MySQL 5.7+ or MariaDB 10.3+
- GSM Gateway (compatible with original API)

### 1. Clone and Setup
```bash
git clone <repository-url>
cd ModernSMS

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Setup
```bash
# Run the database setup script
python setup_database.py
```

The script will:
- Create MySQL database and user
- Generate secure configuration
- Create `.env` file with settings

### 3. Run Application
```bash
python run.py
```

### 4. Access Application
- **URL**: http://127.0.0.1:5000
- **Username**: admin
- **Password**: admin123

## 📋 Configuration

### Environment Variables (.env)
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=modern_sms
DB_USER=sms_user
DB_PASSWORD=your_password

# Flask Configuration
SECRET_KEY=your_secret_key
FLASK_ENV=development

# Default GSM Gateway (Fallback)
DEFAULT_GSM_IP=************
DEFAULT_GSM_USERNAME=apiuser
DEFAULT_GSM_PASSWORD=apipass
DEFAULT_GSM_API_PORT=80
DEFAULT_GSM_TG_PORT=5038
DEFAULT_GSM_MAX_PORTS=8
```

### GSM Gateway Configuration
1. **Login** to the application
2. **Navigate** to Gateways section
3. **Add Gateway** with your hardware details:
   - Name: Descriptive name
   - IP Address: Gateway IP
   - API Port: Usually 80
   - TG SMS Port: Usually 5038
   - Username/Password: Gateway credentials
   - Max Ports: Number of SIM slots

## 🏗️ Architecture

### Project Structure
```
ModernSMS/
├── app/
│   ├── __init__.py          # Application factory
│   ├── models.py            # Database models
│   ├── main/                # Main blueprint
│   ├── auth/                # Authentication
│   ├── sms/                 # SMS management
│   ├── gateways/            # Gateway management
│   ├── api/                 # REST API
│   └── templates/           # HTML templates
├── config.py                # Configuration
├── run.py                   # Application entry point
├── setup_database.py        # Database setup
└── requirements.txt         # Dependencies
```

### Database Schema
- **Users** - User accounts and permissions
- **Roles** - User roles (admin, user)
- **GSM Gateways** - Gateway configurations
- **SMS Ports** - Port status and information
- **SMS Messages** - Message storage and tracking

## 🔌 API Endpoints

### Authentication Required
- `GET /api/messages` - Get messages with pagination
- `POST /api/messages/send` - Send SMS
- `GET /api/gateways` - Get gateways list
- `GET /api/gateways/{id}/ports` - Get gateway ports
- `POST /api/ports/refresh` - Refresh port status
- `GET /api/stats` - Get system statistics

### Example API Usage
```python
import requests

# Send SMS
response = requests.post('http://localhost:5000/api/messages/send', 
    json={
        'phone_number': '+**********',
        'message': 'Hello World!',
        'port_id': 1
    },
    headers={'Authorization': 'Bearer <token>'}
)
```

## 🔧 GSM Gateway Integration

### Supported API Format
The system uses the same API format as the original system:
```
http://{gateway_ip}:{port}/cgi/WebCGI?1500101=account={user}&password={pass}&port={port}&destination={number}&content={message}&id={id}
```

### Port Detection
```
http://{gateway_ip}:{port}/cgi/WebCGI?1500102=account={user}&password={pass}&port={port}
```

## 👥 User Management

### Default Users
- **admin** - Full system access
- Password: admin123

### Permissions
- `can_send_sms` - Send SMS messages
- `can_view_inbox` - View received messages
- `can_view_outbox` - View sent messages
- `can_manage_gateways` - Manage GSM gateways
- `can_manage_users` - User administration
- `can_view_reports` - Access reports
- `can_export_data` - Export functionality

### Port Assignments
Users can be assigned specific ports for restricted access:
```json
{
    "assigned_ports": [
        {"port": "1", "gateway_id": 1},
        {"port": "2", "gateway_id": 1}
    ]
}
```

## 🔒 Security Features

- **Password Hashing** - Werkzeug secure hashing
- **Session Management** - Secure session handling
- **Login Attempts** - Brute force protection
- **Account Locking** - Temporary lockouts
- **Permission System** - Role-based access
- **CSRF Protection** - Form security

## 📊 Monitoring & Logging

### Dashboard Metrics
- Total messages sent/received
- Daily statistics
- Gateway status
- Port availability
- Failed message count

### Real-time Updates
- Port status monitoring
- Gateway connectivity
- Message status tracking
- User activity

## 🚀 Production Deployment

### Using Gunicorn
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 run:app
```

### Environment Configuration
```env
FLASK_ENV=production
FLASK_DEBUG=False
SESSION_COOKIE_SECURE=True
```

### Database Optimization
- Enable MySQL query cache
- Configure connection pooling
- Set up database backups
- Monitor performance

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check MySQL service status
   - Verify credentials in .env
   - Ensure database exists

2. **Gateway Not Responding**
   - Check IP address and ports
   - Verify network connectivity
   - Test credentials

3. **Port Detection Failed**
   - Check gateway API compatibility
   - Verify port numbers
   - Review gateway logs

### Debug Mode
```bash
export FLASK_DEBUG=True
python run.py
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check documentation
- Review troubleshooting guide

---

**Modern SMS Manager** - Professional SMS management made simple! 🚀
