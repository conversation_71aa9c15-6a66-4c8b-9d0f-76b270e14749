"""
User management routes
"""
from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app.users import bp
from app.models import User, Role
from app import db
from datetime import datetime

def require_user_management_permission(f):
    """Decorator to require user management permission"""
    def decorated_function(*args, **kwargs):
        if not current_user.has_permission('manage_users'):
            flash('Access denied. User management permission required.', 'error')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@bp.route('/')
@login_required
@require_user_management_permission
def index():
    """User management dashboard"""
    try:
        users = User.query.all()
        roles = Role.query.all()
        
        # Calculate statistics
        stats = {
            'total_users': len(users),
            'active_users': len([u for u in users if u.is_active]),
            'admin_users': len([u for u in users if u.role and u.role.name == 'admin']),
            'regular_users': len([u for u in users if not u.role or u.role.name != 'admin'])
        }
        
        return render_template('users/index.html', users=users, roles=roles, stats=stats)
        
    except Exception as e:
        current_app.logger.error(f"User index error: {str(e)}")
        flash('Error loading users.', 'error')
        return render_template('users/index.html', users=[], roles=[], stats={})

@bp.route('/add', methods=['GET', 'POST'])
@login_required
@require_user_management_permission
def add():
    """Add new user"""
    if request.method == 'POST':
        try:
            # Get form data
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            first_name = request.form.get('first_name', '').strip()
            last_name = request.form.get('last_name', '').strip()
            password = request.form.get('password', '').strip()
            role_id = request.form.get('role_id', type=int)
            
            # Permissions
            can_send_sms = bool(request.form.get('can_send_sms'))
            can_view_inbox = bool(request.form.get('can_view_inbox'))
            can_view_outbox = bool(request.form.get('can_view_outbox'))
            can_manage_gateways = bool(request.form.get('can_manage_gateways'))
            can_manage_users = bool(request.form.get('can_manage_users'))
            can_view_reports = bool(request.form.get('can_view_reports'))
            can_export_data = bool(request.form.get('can_export_data'))

            # Port assignments
            assigned_ports_raw = request.form.getlist('assigned_ports')
            assigned_ports = []

            for port_assignment in assigned_ports_raw:
                if ':' in port_assignment:
                    gateway_id, port_number = port_assignment.split(':')
                    assigned_ports.append({
                        'gateway_id': int(gateway_id),
                        'port': port_number,
                        'port_id': None  # Will be resolved later if needed
                    })

            # Convert to JSON for storage
            assigned_ports_json = assigned_ports if assigned_ports else None
            
            # Validate required fields
            if not all([username, email, password]):
                error_msg = 'Username, email, and password are required'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': error_msg})
                flash(error_msg, 'error')
                return render_template('users/add.html', roles=Role.query.all())
            
            # Check for duplicate username
            existing_user = User.query.filter_by(username=username).first()
            if existing_user:
                error_msg = f'Username "{username}" already exists'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': error_msg})
                flash(error_msg, 'error')
                return render_template('users/add.html', roles=Role.query.all())
            
            # Check for duplicate email
            existing_email = User.query.filter_by(email=email).first()
            if existing_email:
                error_msg = f'Email "{email}" already exists'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': error_msg})
                flash(error_msg, 'error')
                return render_template('users/add.html', roles=Role.query.all())
            
            # Get role
            role = None
            if role_id:
                role = Role.query.get(role_id)
            
            # Create user
            user = User(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name,
                role=role,
                can_send_sms=can_send_sms,
                can_view_inbox=can_view_inbox,
                can_view_outbox=can_view_outbox,
                can_manage_gateways=can_manage_gateways,
                can_manage_users=can_manage_users,
                can_view_reports=can_view_reports,
                can_export_data=can_export_data,
                assigned_ports=assigned_ports_json,
                is_active=True
            )
            
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            success_msg = f'User "{username}" created successfully'
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': True,
                    'message': success_msg,
                    'user_id': user.id
                })
            
            flash(success_msg, 'success')
            return redirect(url_for('users.index'))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Add user error: {str(e)}")
            error_msg = 'Error creating user'
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': error_msg})
            flash(error_msg, 'error')
            return render_template('users/add.html', roles=Role.query.all())
    
    roles = Role.query.all()
    return render_template('users/add.html', roles=roles)

@bp.route('/<int:user_id>')
@login_required
@require_user_management_permission
def detail(user_id):
    """User detail view"""
    try:
        user = User.query.get_or_404(user_id)
        return render_template('users/detail.html', user=user)
        
    except Exception as e:
        current_app.logger.error(f"User detail error: {str(e)}")
        flash('Error loading user details.', 'error')
        return redirect(url_for('users.index'))

@bp.route('/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@require_user_management_permission
def edit(user_id):
    """Edit user"""
    user = User.query.get_or_404(user_id)
    
    if request.method == 'POST':
        try:
            # Update user fields
            user.username = request.form.get('username', '').strip()
            user.email = request.form.get('email', '').strip()
            user.first_name = request.form.get('first_name', '').strip()
            user.last_name = request.form.get('last_name', '').strip()
            user.is_active = bool(request.form.get('is_active'))
            
            # Update role
            role_id = request.form.get('role_id', type=int)
            if role_id:
                user.role = Role.query.get(role_id)
            else:
                user.role = None
            
            # Update permissions
            user.can_send_sms = bool(request.form.get('can_send_sms'))
            user.can_view_inbox = bool(request.form.get('can_view_inbox'))
            user.can_view_outbox = bool(request.form.get('can_view_outbox'))
            user.can_manage_gateways = bool(request.form.get('can_manage_gateways'))
            user.can_manage_users = bool(request.form.get('can_manage_users'))
            user.can_view_reports = bool(request.form.get('can_view_reports'))
            user.can_export_data = bool(request.form.get('can_export_data'))
            
            # Update password if provided
            new_password = request.form.get('new_password', '').strip()
            if new_password:
                user.set_password(new_password)
            
            # Validate required fields
            if not all([user.username, user.email]):
                flash('Username and email are required', 'error')
                return render_template('users/edit.html', user=user, roles=Role.query.all())
            
            # Check for duplicate username (excluding current user)
            existing_user = User.query.filter(
                User.username == user.username,
                User.id != user_id
            ).first()
            
            if existing_user:
                flash(f'Username "{user.username}" is already taken', 'error')
                return render_template('users/edit.html', user=user, roles=Role.query.all())
            
            # Check for duplicate email (excluding current user)
            existing_email = User.query.filter(
                User.email == user.email,
                User.id != user_id
            ).first()
            
            if existing_email:
                flash(f'Email "{user.email}" is already taken', 'error')
                return render_template('users/edit.html', user=user, roles=Role.query.all())
            
            db.session.commit()
            
            flash(f'User "{user.username}" updated successfully', 'success')
            return redirect(url_for('users.detail', user_id=user_id))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Edit user error: {str(e)}")
            flash('Error updating user', 'error')
            return render_template('users/edit.html', user=user, roles=Role.query.all())
    
    roles = Role.query.all()
    return render_template('users/edit.html', user=user, roles=roles)
