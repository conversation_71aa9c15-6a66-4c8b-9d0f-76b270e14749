#!/usr/bin/env python3
"""
Simple database fix for SMS ports schema
"""

import sys
import os
sys.path.append('backend')

def fix_database():
    print("🔧 Simple Database Fix")
    print("=" * 25)
    
    try:
        # Import after adding path
        from app import app, db, User, SMSMessage, SMSPort
        from werkzeug.security import generate_password_hash
        
        print("✅ Imported Flask app successfully")
        
        with app.app_context():
            print("📋 Checking current database state...")
            
            # Check if we can query users (this should work)
            try:
                users = User.query.all()
                print(f"✅ Users table working: {len(users)} users found")
            except Exception as e:
                print(f"❌ Users table error: {str(e)}")
            
            # Check if we can query messages (this should work)
            try:
                messages = SMSMessage.query.all()
                print(f"✅ Messages table working: {len(messages)} messages found")
            except Exception as e:
                print(f"❌ Messages table error: {str(e)}")
            
            # Check SMS ports table (this is the problem)
            try:
                ports = SMSPort.query.all()
                print(f"✅ SMS Ports table working: {len(ports)} ports found")
            except Exception as e:
                print(f"❌ SMS Ports table error: {str(e)}")
                print("🔧 Fixing SMS ports table...")
                
                # Drop and recreate SMS ports table
                try:
                    # Drop the problematic table
                    db.engine.execute("DROP TABLE IF EXISTS sms_ports")
                    print("   🗑️ Dropped old sms_ports table")
                    
                    # Recreate just the SMS ports table
                    SMSPort.__table__.create(db.engine)
                    print("   ✅ Created new sms_ports table with correct schema")
                    
                    # Add default ports
                    for port_num in range(1, 9):
                        port = SMSPort(
                            port_number=str(port_num),
                            status='active',
                            network_name='',
                            signal_quality='',
                            sim_imsi='',
                            is_active=True
                        )
                        db.session.add(port)
                    
                    db.session.commit()
                    print("   ✅ Added 8 default SMS ports")
                    
                    # Test the fix
                    ports = SMSPort.query.all()
                    print(f"   ✅ SMS Ports table now working: {len(ports)} ports")
                    
                except Exception as fix_error:
                    print(f"   ❌ Error fixing SMS ports table: {str(fix_error)}")
                    return False
            
            # Ensure admin user exists
            try:
                admin_user = User.query.filter_by(username='admin').first()
                if not admin_user:
                    admin_user = User(
                        username='admin',
                        email='<EMAIL>',
                        role='admin',
                        can_send_sms=True,
                        can_view_inbox=True,
                        can_view_outbox=True,
                        can_export=True,
                        can_manage_users=True,
                        can_view_reports=True,
                        can_manage_settings=True,
                        can_manage_ports=True
                    )
                    admin_user.set_password('admin123')
                    db.session.add(admin_user)
                    db.session.commit()
                    print("✅ Created admin user (admin/admin123)")
                else:
                    print("✅ Admin user already exists")
            except Exception as e:
                print(f"❌ Error with admin user: {str(e)}")
            
            print("\n🧪 Final Test...")
            
            # Final test of all tables
            try:
                users = User.query.all()
                messages = SMSMessage.query.all()
                ports = SMSPort.query.all()
                
                print(f"✅ Users: {len(users)}")
                print(f"✅ Messages: {len(messages)}")
                print(f"✅ Ports: {len(ports)}")
                
                # Test user editing functionality
                admin_user = User.query.filter_by(username='admin').first()
                if admin_user:
                    has_settings = admin_user.has_permission('can_manage_settings')
                    print(f"✅ Admin permissions: can_manage_settings = {has_settings}")
                
                print("\n🎉 DATABASE FIX COMPLETED SUCCESSFULLY!")
                print("=" * 40)
                print("✅ SMS ports table schema is now correct")
                print("✅ User editing should work without errors")
                print("✅ All database queries should work")
                
                return True
                
            except Exception as e:
                print(f"❌ Final test failed: {str(e)}")
                return False
    
    except Exception as e:
        print(f"❌ Error in database fix: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_database()
    if success:
        print("\n🚀 Database is now fixed! You can restart the Flask app.")
    else:
        print("\n⚠️ Database fix failed. Check the errors above.")
