#!/usr/bin/env python3
"""
Simple test application to verify everything works
"""
import os
import sys
from flask import Flask, render_template_string

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-key'

@app.route('/')
def index():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>Modern SMS Manager - Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body text-center p-5">
                        <h1 class="display-4 text-primary mb-4">🎉 Success!</h1>
                        <h2 class="h3 mb-4">Modern SMS Manager is Working!</h2>
                        
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="p-3 bg-light rounded">
                                    <h5>✅ Python</h5>
                                    <p class="mb-0">{{ python_version }}</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="p-3 bg-light rounded">
                                    <h5>✅ Flask</h5>
                                    <p class="mb-0">Running</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="p-3 bg-light rounded">
                                    <h5>✅ Dependencies</h5>
                                    <p class="mb-0">Installed</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success">
                            <h5>🚀 Ready for Full Application!</h5>
                            <p class="mb-0">All components are working correctly. You can now run the full Modern SMS Manager application.</p>
                        </div>
                        
                        <div class="mt-4">
                            <h6>Next Steps:</h6>
                            <ol class="text-start">
                                <li>Stop this test server (Ctrl+C)</li>
                                <li>Run: <code>python run.py</code></li>
                                <li>Access: <code>http://127.0.0.1:5000</code></li>
                                <li>Login: admin / admin123</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    ''', python_version=sys.version.split()[0])

if __name__ == '__main__':
    print("="*60)
    print("🧪 Modern SMS Manager - Test Application")
    print("="*60)
    print("🌐 Starting test server...")
    print("📍 URL: http://127.0.0.1:5000")
    print("🛑 Press Ctrl+C to stop")
    print("="*60)
    
    app.run(host='127.0.0.1', port=5000, debug=True, use_reloader=False)
