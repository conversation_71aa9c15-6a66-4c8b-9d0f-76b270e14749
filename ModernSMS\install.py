#!/usr/bin/env python3
"""
Modern SMS Manager - One-Click Installation Script
Automates the complete setup process
"""
import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header():
    """Print installation header"""
    print("="*70)
    print("🚀 Modern SMS Manager - One-Click Installation")
    print("="*70)
    print("This script will set up everything you need to run Modern SMS Manager")
    print()

def check_python_version():
    """Check Python version"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required. Current version:", f"{version.major}.{version.minor}")
        sys.exit(1)
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")

def check_mysql():
    """Check if MySQL is available"""
    print("\n🗄️  Checking MySQL availability...")
    try:
        # Try to import mysql connector
        import mysql.connector
        print("✅ MySQL connector available")
        return True
    except ImportError:
        print("⚠️  MySQL connector not found - will install during setup")
        return False

def create_virtual_environment():
    """Create virtual environment"""
    print("\n📦 Setting up virtual environment...")
    
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created")
    except subprocess.CalledProcessError:
        print("❌ Failed to create virtual environment")
        sys.exit(1)

def get_pip_command():
    """Get the correct pip command for the platform"""
    if platform.system() == "Windows":
        return os.path.join("venv", "Scripts", "pip")
    else:
        return os.path.join("venv", "bin", "pip")

def get_python_command():
    """Get the correct python command for the platform"""
    if platform.system() == "Windows":
        return os.path.join("venv", "Scripts", "python")
    else:
        return os.path.join("venv", "bin", "python")

def install_dependencies():
    """Install Python dependencies"""
    print("\n📚 Installing dependencies...")
    
    pip_cmd = get_pip_command()
    
    try:
        # Upgrade pip first
        subprocess.run([pip_cmd, "install", "--upgrade", "pip"], check=True)
        
        # Install requirements
        subprocess.run([pip_cmd, "install", "-r", "requirements.txt"], check=True)
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        sys.exit(1)

def setup_database():
    """Run database setup"""
    print("\n🗄️  Setting up database...")
    
    python_cmd = get_python_command()
    
    try:
        # Run database setup script
        subprocess.run([python_cmd, "setup_database.py"], check=True)
        print("✅ Database setup completed")
    except subprocess.CalledProcessError:
        print("❌ Database setup failed")
        print("You can run 'python setup_database.py' manually later")

def create_startup_scripts():
    """Create startup scripts"""
    print("\n📝 Creating startup scripts...")
    
    # Windows batch file
    if platform.system() == "Windows":
        with open("start.bat", "w") as f:
            f.write("""@echo off
echo Starting Modern SMS Manager...
venv\\Scripts\\python run.py
pause
""")
        print("✅ Created start.bat for Windows")
    
    # Unix shell script
    with open("start.sh", "w") as f:
        f.write("""#!/bin/bash
echo "Starting Modern SMS Manager..."
source venv/bin/activate
python run.py
""")
    
    # Make shell script executable on Unix
    if platform.system() != "Windows":
        os.chmod("start.sh", 0o755)
        print("✅ Created start.sh for Unix/Linux")

def print_completion_message():
    """Print completion message with instructions"""
    print("\n" + "="*70)
    print("🎉 Installation completed successfully!")
    print("="*70)
    
    print("\n📋 What was installed:")
    print("   ✅ Python virtual environment")
    print("   ✅ All required dependencies")
    print("   ✅ Database configuration")
    print("   ✅ Startup scripts")
    
    print("\n🚀 How to start the application:")
    if platform.system() == "Windows":
        print("   • Double-click start.bat")
        print("   • Or run: venv\\Scripts\\python run.py")
    else:
        print("   • Run: ./start.sh")
        print("   • Or run: source venv/bin/activate && python run.py")
    
    print("\n🌐 Access the application:")
    print("   • URL: http://127.0.0.1:5000")
    print("   • Username: admin")
    print("   • Password: admin123")
    
    print("\n📖 Next steps:")
    print("   1. Start the application")
    print("   2. Login with admin credentials")
    print("   3. Add your GSM gateways")
    print("   4. Configure port settings")
    print("   5. Start sending SMS!")
    
    print("\n📚 Documentation:")
    print("   • README.md - Complete setup guide")
    print("   • .env - Configuration file")
    print("   • setup_database.py - Database setup")
    
    print("\n" + "="*70)

def main():
    """Main installation process"""
    print_header()
    
    # Check if we're in the right directory
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found!")
        print("Please run this script from the ModernSMS directory")
        sys.exit(1)
    
    # Confirm installation
    confirm = input("Do you want to proceed with the installation? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("Installation cancelled.")
        sys.exit(0)
    
    try:
        # Run installation steps
        check_python_version()
        check_mysql()
        create_virtual_environment()
        install_dependencies()
        
        # Ask about database setup
        print("\n" + "="*50)
        print("Database Setup")
        print("="*50)
        print("The next step will set up the MySQL database.")
        print("You'll need MySQL root credentials.")
        
        setup_db = input("\nSet up database now? (Y/n): ").strip().lower()
        if setup_db not in ['n', 'no']:
            setup_database()
        else:
            print("⚠️  Database setup skipped - run 'python setup_database.py' later")
        
        create_startup_scripts()
        print_completion_message()
        
    except KeyboardInterrupt:
        print("\n\n❌ Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
