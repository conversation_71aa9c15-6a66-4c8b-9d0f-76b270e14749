{% extends "base.html" %}

{% block title %}{{ user.full_name }} - User Details - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">User Details</h1>
        <p class="text-muted">{{ user.full_name }}</p>
    </div>
    <div>
        <a href="{{ url_for('users.edit', user_id=user.id) }}" class="btn btn-primary">
            <i class="bi bi-pencil"></i> Edit User
        </a>
        <a href="{{ url_for('users.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person"></i> User Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Username</label>
                            <p class="form-control-plaintext">{{ user.username }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Email</label>
                            <p class="form-control-plaintext">{{ user.email }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Full Name</label>
                            <p class="form-control-plaintext">{{ user.full_name or 'Not provided' }}</p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Role</label>
                            <p class="form-control-plaintext">
                                {% if user.role %}
                                    <span class="badge bg-primary">{{ user.role.name.title() }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">No Role</span>
                                {% endif %}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Status</label>
                            <p class="form-control-plaintext">
                                {% if user.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Created</label>
                            <p class="form-control-plaintext">
                                {{ user.created_at.strftime('%B %d, %Y at %I:%M %p') if user.created_at else 'Unknown' }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-check"></i> Permissions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="permission-item mb-2">
                            <i class="bi bi-send text-primary"></i>
                            <span class="ms-2">Send SMS</span>
                            {% if user.can_send_sms %}
                                <span class="badge bg-success ms-auto">Allowed</span>
                            {% else %}
                                <span class="badge bg-danger ms-auto">Denied</span>
                            {% endif %}
                        </div>
                        
                        <div class="permission-item mb-2">
                            <i class="bi bi-inbox text-info"></i>
                            <span class="ms-2">View Inbox</span>
                            {% if user.can_view_inbox %}
                                <span class="badge bg-success ms-auto">Allowed</span>
                            {% else %}
                                <span class="badge bg-danger ms-auto">Denied</span>
                            {% endif %}
                        </div>
                        
                        <div class="permission-item mb-2">
                            <i class="bi bi-send text-success"></i>
                            <span class="ms-2">View Outbox</span>
                            {% if user.can_view_outbox %}
                                <span class="badge bg-success ms-auto">Allowed</span>
                            {% else %}
                                <span class="badge bg-danger ms-auto">Denied</span>
                            {% endif %}
                        </div>
                        
                        <div class="permission-item mb-2">
                            <i class="bi bi-graph-up text-secondary"></i>
                            <span class="ms-2">View Reports</span>
                            {% if user.can_view_reports %}
                                <span class="badge bg-success ms-auto">Allowed</span>
                            {% else %}
                                <span class="badge bg-danger ms-auto">Denied</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="permission-item mb-2">
                            <i class="bi bi-hdd-network text-warning"></i>
                            <span class="ms-2">Manage Gateways</span>
                            {% if user.can_manage_gateways %}
                                <span class="badge bg-success ms-auto">Allowed</span>
                            {% else %}
                                <span class="badge bg-danger ms-auto">Denied</span>
                            {% endif %}
                        </div>
                        
                        <div class="permission-item mb-2">
                            <i class="bi bi-people text-danger"></i>
                            <span class="ms-2">Manage Users</span>
                            {% if user.can_manage_users %}
                                <span class="badge bg-success ms-auto">Allowed</span>
                            {% else %}
                                <span class="badge bg-danger ms-auto">Denied</span>
                            {% endif %}
                        </div>
                        
                        <div class="permission-item mb-2">
                            <i class="bi bi-download text-dark"></i>
                            <span class="ms-2">Export Data</span>
                            {% if user.can_export_data %}
                                <span class="badge bg-success ms-auto">Allowed</span>
                            {% else %}
                                <span class="badge bg-danger ms-auto">Denied</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-diagram-3"></i> Port Assignments</h5>
            </div>
            <div class="card-body">
                {% if user.assigned_ports %}
                    <div class="row">
                        {% for assignment in user.assigned_ports %}
                        <div class="col-md-4 mb-3">
                            <div class="port-assignment-card">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-hdd-network text-primary me-2"></i>
                                    <div>
                                        <div class="fw-bold">Gateway {{ assignment.gateway_id }}</div>
                                        <small class="text-muted">Port {{ assignment.port }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-diagram-3 text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-2">No Port Assignments</h6>
                        <p class="text-muted">This user has no assigned ports and cannot send SMS.</p>
                        <a href="{{ url_for('users.edit', user_id=user.id) }}" class="btn btn-outline-primary">
                            <i class="bi bi-plus"></i> Assign Ports
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-activity"></i> Activity Summary</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Last Login:</strong><br>
                    <small class="text-muted">
                        {{ user.last_login.strftime('%B %d, %Y at %I:%M %p') if user.last_login else 'Never logged in' }}
                    </small>
                </div>
                
                <div class="mb-3">
                    <strong>Account Created:</strong><br>
                    <small class="text-muted">
                        {{ user.created_at.strftime('%B %d, %Y') if user.created_at else 'Unknown' }}
                    </small>
                </div>
                
                <div class="mb-3">
                    <strong>Total Messages Sent:</strong><br>
                    <span class="badge bg-primary">{{ user.sent_messages.count() if user.sent_messages else 0 }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Account Status:</strong><br>
                    {% if user.is_active %}
                        <span class="badge bg-success">Active Account</span>
                    {% else %}
                        <span class="badge bg-danger">Inactive Account</span>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-gear"></i> Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('users.edit', user_id=user.id) }}" class="btn btn-outline-primary">
                        <i class="bi bi-pencil"></i> Edit User
                    </a>
                    
                    {% if user.id != current_user.id %}
                    <button class="btn btn-outline-warning" onclick="toggleUserStatus({{ user.id }}, '{{ user.username }}', {{ user.is_active|lower }})">
                        <i class="bi bi-{{ 'person-x' if user.is_active else 'person-check' }}"></i>
                        {{ 'Deactivate' if user.is_active else 'Activate' }} User
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('users.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-list"></i> All Users
                    </a>
                </div>
            </div>
        </div>
        
        {% if user.assigned_ports %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Port Access Info</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">
                    This user can access <strong>{{ user.assigned_ports|length }} port(s)</strong> 
                    across {{ user.assigned_ports|map(attribute='gateway_id')|unique|list|length }} gateway(s).
                </p>
                
                <p class="small text-muted">
                    Port assignments control which GSM ports the user can use for sending SMS messages.
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .permission-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background: #f8f9fa;
    }
    
    .port-assignment-card {
        padding: 12px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .port-assignment-card:hover {
        border-color: var(--primary-color);
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function toggleUserStatus(userId, username, isActive) {
        const action = isActive ? 'deactivate' : 'activate';
        const message = `Are you sure you want to ${action} user "${username}"?`;
        
        if (confirm(message)) {
            // This would be implemented with an AJAX call to toggle user status
            showToast(`User ${action} functionality would be implemented here`, 'info');
        }
    }
</script>
{% endblock %}
