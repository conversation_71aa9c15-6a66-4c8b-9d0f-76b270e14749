#!/usr/bin/env python3
"""
Fix Unified Configuration
This script fixes the database to allow all gateways to use the same working configuration
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app import app, db, GSMGateway, SMSPort, MAIN_GATEWAY_WORKING_CONFIG

def fix_unified_configuration():
    """Fix the database to support unified configuration"""
    print("🔧 Fixing Database for Unified Configuration")
    print("=" * 60)
    
    with app.app_context():
        try:
            # Step 1: Delete all existing gateways except Main Gateway
            print("📋 Step 1: Cleaning up existing gateways...")
            
            main_gateway = GSMGateway.query.filter_by(name='Main Gateway').first()
            if main_gateway:
                print(f"   ✅ Found Main Gateway (ID: {main_gateway.id})")
                
                # Delete other gateways and their ports
                other_gateways = GSMGateway.query.filter(GSMGateway.name != 'Main Gateway').all()
                for gateway in other_gateways:
                    print(f"   🗑️ Deleting gateway: {gateway.name}")
                    
                    # Delete ports for this gateway
                    ports = SMSPort.query.filter_by(gateway_id=gateway.id).all()
                    for port in ports:
                        db.session.delete(port)
                    
                    # Delete gateway
                    db.session.delete(gateway)
                
                db.session.commit()
                print(f"   ✅ Cleaned up other gateways")
            else:
                print("   ❌ Main Gateway not found!")
                return False
            
            # Step 2: Update Main Gateway to use correct configuration
            print("\n📋 Step 2: Updating Main Gateway configuration...")
            
            main_gateway.ip_address = MAIN_GATEWAY_WORKING_CONFIG['ip_address']
            main_gateway.api_port = MAIN_GATEWAY_WORKING_CONFIG['api_port']
            main_gateway.tg_sms_port = MAIN_GATEWAY_WORKING_CONFIG['tg_sms_port']
            main_gateway.username = MAIN_GATEWAY_WORKING_CONFIG['username']
            main_gateway.password = MAIN_GATEWAY_WORKING_CONFIG['password']
            main_gateway.max_ports = MAIN_GATEWAY_WORKING_CONFIG['max_ports']
            main_gateway.status = MAIN_GATEWAY_WORKING_CONFIG['status']
            main_gateway.description = 'Main GSM Gateway - Primary (Working Config)'
            
            # Update ports for Main Gateway (only 4 ports)
            existing_ports = SMSPort.query.filter_by(gateway_id=main_gateway.id).all()
            
            # Delete extra ports (keep only 4)
            for port in existing_ports:
                if port.port_number.isdigit() and int(port.port_number) > 4:
                    print(f"   🗑️ Deleting extra port: {port.port_number}")
                    db.session.delete(port)
                elif port.port_number.isdigit() and int(port.port_number) <= 4:
                    print(f"   ✅ Keeping port: {port.port_number}")
                    port.is_active = True
                    port.status = 'detected'
                    port.network_name = 'Unknown'
                    port.signal_quality = 'Unknown'
            
            # Create missing ports (1-4)
            for port_num in range(1, 5):
                existing = SMSPort.query.filter_by(
                    gateway_id=main_gateway.id,
                    port_number=str(port_num)
                ).first()
                
                if not existing:
                    print(f"   ➕ Creating port: {port_num}")
                    new_port = SMSPort(
                        port_number=str(port_num),
                        status='detected',
                        network_name='Unknown',
                        signal_quality='Unknown',
                        is_active=True,
                        gateway_id=main_gateway.id
                    )
                    db.session.add(new_port)
            
            db.session.commit()
            print(f"   ✅ Updated Main Gateway with 4 ports")
            
            # Step 3: Create virtual gateways with unique IPs
            print("\n📋 Step 3: Creating virtual gateways...")
            
            virtual_gateways = [
                {
                    'name': 'rcbc',
                    'ip_address': '*************',  # Same as main but will be virtual
                    'location': 'Secondary Location',
                    'description': 'RCBC Virtual Gateway - Uses Main Gateway Hardware'
                },
                {
                    'name': 'Gateway3',
                    'ip_address': '*************',  # Same as main but will be virtual
                    'location': 'Third Location',
                    'description': 'Gateway3 Virtual Gateway - Uses Main Gateway Hardware'
                }
            ]
            
            for i, gw_config in enumerate(virtual_gateways, start=2):
                print(f"   ➕ Creating virtual gateway: {gw_config['name']}")
                
                # Use unique IP for database constraint (but same config)
                virtual_ip = f"192.168.1.{126 + i}"  # *************, *************, etc.
                
                virtual_gateway = GSMGateway(
                    name=gw_config['name'],
                    ip_address=virtual_ip,  # Unique IP for database
                    api_port=MAIN_GATEWAY_WORKING_CONFIG['api_port'],
                    tg_sms_port=MAIN_GATEWAY_WORKING_CONFIG['tg_sms_port'],
                    username=MAIN_GATEWAY_WORKING_CONFIG['username'],
                    password=MAIN_GATEWAY_WORKING_CONFIG['password'],
                    max_ports=MAIN_GATEWAY_WORKING_CONFIG['max_ports'],
                    status=MAIN_GATEWAY_WORKING_CONFIG['status'],
                    location=gw_config['location'],
                    description=gw_config['description'],
                    is_primary=False
                )
                
                db.session.add(virtual_gateway)
                db.session.flush()  # Get the ID
                
                # Create virtual ports for this gateway
                for port_num in range(1, 5):
                    port_identifier = f"gw{virtual_gateway.id}_port{port_num}"
                    
                    virtual_port = SMSPort(
                        port_number=port_identifier,
                        status='virtual',
                        network_name='Virtual Network',
                        signal_quality='N/A',
                        is_active=False,  # Virtual ports are inactive
                        gateway_id=virtual_gateway.id
                    )
                    db.session.add(virtual_port)
                
                print(f"   ✅ Created virtual gateway: {gw_config['name']} with 4 virtual ports")
            
            db.session.commit()
            
            # Step 4: Verify the setup
            print("\n📋 Step 4: Verifying setup...")
            
            all_gateways = GSMGateway.query.all()
            print(f"   Total Gateways: {len(all_gateways)}")
            
            for gateway in all_gateways:
                ports = SMSPort.query.filter_by(gateway_id=gateway.id).all()
                active_ports = [p for p in ports if p.is_active]
                
                print(f"   🏢 {gateway.name}:")
                print(f"      IP: {gateway.ip_address}")
                print(f"      Ports: {len(ports)} total, {len(active_ports)} active")
                print(f"      Type: {'Real Hardware' if gateway.name == 'Main Gateway' else 'Virtual'}")
            
            print(f"\n✅ Unified Configuration Setup Complete!")
            print(f"📋 Summary:")
            print(f"   - Main Gateway: Real hardware with 4 active ports")
            print(f"   - Virtual Gateways: Share same config but are virtual")
            print(f"   - All gateways use the same working configuration")
            print(f"   - Only Main Gateway ports will actually send SMS")
            
            return True
            
        except Exception as e:
            print(f"❌ Error fixing configuration: {str(e)}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    success = fix_unified_configuration()
    if success:
        print(f"\n🎯 Next Steps:")
        print(f"1. Restart the Flask application")
        print(f"2. Check GSM Gateways page - should show 3 gateways with 4 ports each")
        print(f"3. Only Main Gateway ports should show as active")
        print(f"4. Virtual gateway ports should show as inactive")
        print(f"5. SMS sending will only work from Main Gateway ports")
    else:
        print(f"\n❌ Setup failed. Please check the errors above.")
