#!/usr/bin/env python3
"""
Simple Test App - Multi-Gateway User Creation
"""

import sys
import os
sys.path.append('backend')

# Import Flask and test the multi-gateway functionality
from flask import Flask, render_template, request, redirect, url_for, flash
from app import app, db, GSMGateway, SMSPort, User

@app.route('/test_create_user')
def test_create_user():
    """Test create user route with multi-gateway support"""
    try:
        # Get actual gateways from database
        gateways = GSMGateway.query.all()
        print(f"📋 Found {len(gateways)} gateways in database")

        ports = SMSPort.query.filter_by(is_active=True).all()

        # Prepare gateway data for template - include ALL gateways
        gateway_data = []
        for gateway in gateways:
            # Get ports for this specific gateway
            gateway_ports = [p for p in ports if p.gateway_id == gateway.id]
            
            gateway_info = {
                'id': gateway.id,
                'name': gateway.name,
                'ip_address': gateway.ip_address,
                'total_ports': len(gateway_ports),
                'active_ports': len([p for p in gateway_ports if p.status in ['active', 'detected']]),
                'ports': gateway_ports
            }
            gateway_data.append(gateway_info)
            print(f"📊 Gateway {gateway.name}: {len(gateway_ports)} ports, {gateway_info['active_ports']} active")

        # For backward compatibility, use first gateway as main gateway_info
        main_gateway_info = gateway_data[0] if gateway_data else None

        return render_template('create_user.html', 
                             ports=ports, 
                             gateway_info=main_gateway_info,
                             gateways=gateway_data)

    except Exception as e:
        print(f"Error: {str(e)}")
        return f"Error: {str(e)}"

if __name__ == '__main__':
    print("🧪 Testing Multi-Gateway User Creation")
    print("=" * 40)
    
    with app.app_context():
        # Check current gateways
        gateways = GSMGateway.query.all()
        print(f"📊 Found {len(gateways)} gateways:")
        
        for gateway in gateways:
            gateway_ports = SMSPort.query.filter_by(gateway_id=gateway.id).all()
            print(f"   Gateway '{gateway.name}' (ID: {gateway.id}): {len(gateway_ports)} ports")
    
    print("\n🚀 Starting test Flask app on http://127.0.0.1:9000")
    print("Visit: http://127.0.0.1:9000/test_create_user")
    
    app.run(host='127.0.0.1', port=9000, debug=True, use_reloader=False)
