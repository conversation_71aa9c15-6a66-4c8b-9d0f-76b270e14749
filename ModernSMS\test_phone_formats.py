#!/usr/bin/env python3
"""
Test phone number format handling
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.sms.services import SMSService

def test_phone_formats():
    """Test various phone number formats"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("📱 Phone Number Format Testing")
        print("="*60)
        
        # Test various phone number formats
        test_numbers = [
            # Local formats
            "09123456789",
            "0************", 
            "0************",
            "(0912) 345-6789",
            
            # International formats
            "+639123456789",
            "+63 ************",
            "+63-************",
            
            # US formats
            "1234567890",
            "(*************",
            "************",
            "************",
            "+1 (*************",
            
            # Invalid formats
            "123",  # Too short
            "12345678901234567890",  # Too long
            "abc123def456",  # Contains letters
            "",  # Empty
        ]
        
        print("\n🧪 Testing Phone Number Validation:")
        print("-" * 60)
        for number in test_numbers:
            is_valid = SMSService.validate_phone_number(number)
            normalized = SMSService.normalize_phone_number(number)
            
            status = "✅ VALID" if is_valid else "❌ INVALID"
            print(f"{status:10} | {number:20} → {normalized}")
        
        print("\n" + "="*60)
        print("📋 Summary:")
        print("✅ Accepts local formats (09123456789)")
        print("✅ Accepts international formats (+639123456789)")
        print("✅ Accepts US formats (************)")
        print("✅ Removes formatting characters")
        print("✅ Validates length (7-15 digits)")
        print("❌ Rejects too short/long numbers")
        print("❌ Rejects non-numeric content")

if __name__ == '__main__':
    test_phone_formats()
