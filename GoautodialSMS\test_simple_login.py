#!/usr/bin/env python3
"""
Simple test to check login functionality
"""

import requests

BASE_URL = "http://127.0.0.1:5000"

def test_simple_login():
    session = requests.Session()
    
    # Test admin login
    print("Testing admin login...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=True)
    print(f"Admin login final URL: {response.url}")
    print(f"Admin login status: {response.status_code}")
    
    # Test accessing a protected page
    inbox_response = session.get(f"{BASE_URL}/inbox")
    print(f"Inbox access status: {inbox_response.status_code}")
    
    # Test debug route
    debug_response = session.get(f"{BASE_URL}/debug/user_permissions")
    print(f"Debug route status: {debug_response.status_code}")
    if debug_response.status_code == 200:
        print("Debug route content:", debug_response.text[:200])
    
    # Test test1 login
    print("\nTesting test1 login...")
    session2 = requests.Session()
    login_data2 = {'username': 'test1', 'password': 'test123'}
    response2 = session2.post(f"{BASE_URL}/login", data=login_data2, allow_redirects=True)
    print(f"Test1 login final URL: {response2.url}")
    print(f"Test1 login status: {response2.status_code}")
    
    # Check if redirected to login (failed) or dashboard (success)
    if 'login' in response2.url:
        print("❌ Test1 login failed - redirected to login page")
    else:
        print("✅ Test1 login successful")

if __name__ == "__main__":
    test_simple_login()
