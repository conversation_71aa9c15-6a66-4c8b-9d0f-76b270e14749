#!/usr/bin/env python3
"""
Debug SMS sending functionality
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway, SMSPort, User, SMSMessage
from app.sms.services import SMSService

def debug_sms_system():
    """Debug the SMS system"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔍 SMS System Debug")
        print("="*60)
        
        # Check gateways
        gateways = GSMGateway.query.all()
        print(f"\n📡 Gateways: {len(gateways)}")
        for gw in gateways:
            print(f"   - {gw.name} ({gw.ip_address}:{gw.api_port}) - Status: {gw.status}")
        
        # Check ports
        ports = SMSPort.query.all()
        print(f"\n🔌 Ports: {len(ports)}")
        for port in ports:
            print(f"   - Gateway {port.gateway_id}, Port {port.port_number} - Status: {port.status}, Active: {port.is_active}")
        
        # Check users
        users = User.query.all()
        print(f"\n👥 Users: {len(users)}")
        for user in users:
            print(f"   - {user.username} - Can send SMS: {user.can_send_sms}")
        
        # Check messages
        messages = SMSMessage.query.all()
        print(f"\n💬 Messages: {len(messages)}")
        for msg in messages:
            print(f"   - {msg.direction} to/from {msg.phone_number} - Status: {msg.status}")
        
        print("\n" + "="*60)
        
        # Test SMS sending if we have data
        if gateways and ports and users:
            print("🧪 Testing SMS sending...")
            
            # Get first active user
            user = User.query.filter_by(can_send_sms=True).first()
            if not user:
                print("❌ No user with SMS permission found")
                return
            
            # Get first active port
            port = SMSPort.query.filter_by(is_active=True).first()
            if not port:
                print("❌ No active port found")
                return
            
            print(f"📤 Testing SMS send with user: {user.username}, port: {port.port_number}")
            
            # Test SMS sending
            result = SMSService.send_sms(
                phone_number="+1234567890",
                message="Test message from debug script",
                port=port,
                user=user
            )
            
            print(f"📊 Result: {result}")
            
            if result['success']:
                print("✅ SMS sending test PASSED")
            else:
                print(f"❌ SMS sending test FAILED: {result.get('error')}")
        else:
            print("⚠️  Cannot test SMS - missing gateways, ports, or users")
            
            if not gateways:
                print("   - Add a gateway first")
            if not ports:
                print("   - Ports will be created automatically when gateway is added")
            if not users:
                print("   - Create a user account")

if __name__ == '__main__':
    debug_sms_system()
