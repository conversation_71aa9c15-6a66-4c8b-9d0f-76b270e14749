#!/usr/bin/env python3
"""
Complete Revert to Original Single GSM Design
This script completely removes all multi-GSM functionality and restores the original working design
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app import app, db, GSMGateway, SMSPort, User

def complete_revert_to_original():
    """Completely revert to original single GSM design"""
    print("🔄 Complete Revert to Original Single GSM Design")
    print("=" * 60)
    
    with app.app_context():
        try:
            # Step 1: Clean everything
            print("📋 Step 1: Complete database cleanup...")
            
            # Delete all SMS ports
            SMSPort.query.delete()
            print("   🗑️ Deleted all SMS ports")
            
            # Delete all GSM gateways
            GSMGateway.query.delete()
            print("   🗑️ Deleted all GSM gateways")
            
            db.session.commit()
            print("   ✅ Database completely cleaned")
            
            # Step 2: Create ONLY the original working gateway
            print("\n📋 Step 2: Creating original single GSM gateway...")
            
            # Original working configuration
            original_gateway = GSMGateway(
                name='Main Gateway',
                ip_address='*************',
                api_port='80',
                tg_sms_port='5038',
                username='apiuser',
                password='apipass',
                max_ports=4,  # Your hardware has 4 ports
                status='active',
                location='Main Office',
                description='Original Single GSM Gateway',
                is_primary=True
            )
            
            db.session.add(original_gateway)
            db.session.flush()
            
            print(f"   ✅ Created original gateway: {original_gateway.name}")
            
            # Step 3: Create ONLY 4 simple ports (original design)
            print("\n📋 Step 3: Creating 4 original ports...")
            
            for port_num in range(1, 5):
                # Port 2 has no SIM (based on your feedback)
                has_sim = port_num != 2
                
                original_port = SMSPort(
                    port_number=str(port_num),  # Simple: 1, 2, 3, 4
                    status='detected' if has_sim else 'no_sim',
                    network_name='Unknown' if has_sim else 'None',
                    signal_quality='Unknown' if has_sim else 'N/A',
                    is_active=True,
                    gateway_id=original_gateway.id
                )
                
                db.session.add(original_port)
                status_icon = "✅" if has_sim else "❌"
                print(f"   {status_icon} Created original port {port_num}")
            
            db.session.commit()
            
            # Step 4: Reset user assignments to original format
            print("\n📋 Step 4: Resetting user assignments...")
            
            users = User.query.all()
            for user in users:
                if user.role == 'admin':
                    user.assigned_ports = '1,2,3,4'  # Admin gets all ports
                else:
                    user.assigned_ports = '1,3,4'    # Regular users get working ports
                
                print(f"   👤 Reset {user.username}: {user.assigned_ports}")
            
            db.session.commit()
            
            # Step 5: Verify original setup
            print("\n📋 Step 5: Verifying original setup...")
            
            gateways = GSMGateway.query.all()
            ports = SMSPort.query.all()
            
            print(f"   🏢 Gateways: {len(gateways)} (should be 1)")
            for gateway in gateways:
                print(f"      - {gateway.name} ({gateway.ip_address})")
            
            print(f"   📱 Ports: {len(ports)} (should be 4)")
            for port in ports:
                status_icon = "✅" if port.status == 'detected' else "❌"
                print(f"      {status_icon} Port {port.port_number}: {port.status}")
            
            print(f"\n✅ Complete Revert to Original Design Successful!")
            print(f"📋 Original Configuration Restored:")
            print(f"   - 1 GSM Gateway (Main Gateway)")
            print(f"   - 4 Ports (1,2,3,4) - Original numbering")
            print(f"   - Port 2 has no SIM (as specified)")
            print(f"   - All multi-GSM complexity removed")
            print(f"   - Original working design restored")
            
            return True
            
        except Exception as e:
            print(f"❌ Error reverting to original: {str(e)}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    print("🚀 Complete Revert to Original Single GSM Tool")
    print("=" * 60)
    print("This will completely remove ALL multi-GSM functionality")
    print("and restore the original working single GSM design.")
    print()
    
    try:
        response = input("Proceed with complete revert? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            success = complete_revert_to_original()
            if success:
                print(f"\n🎯 Next Steps:")
                print(f"1. Restart Flask application")
                print(f"2. Check that SMS receiver reconnects")
                print(f"3. Test SMS sending and receiving")
                print(f"4. Verify inbox functionality works")
                print(f"5. Original single GSM design restored")
            else:
                print(f"\n❌ Revert failed. Please check errors above.")
        else:
            print("✅ Operation cancelled.")
    except KeyboardInterrupt:
        print("\n\n✅ Operation cancelled by user.")
