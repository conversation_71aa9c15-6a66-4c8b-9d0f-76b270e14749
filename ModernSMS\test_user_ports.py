#!/usr/bin/env python3
"""
Test user port assignments
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, Role, GSMGateway, SMSPort

def test_user_ports():
    """Test user port assignments"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔧 USER PORT ASSIGNMENT TEST")
        print("="*60)
        
        try:
            # Check existing users
            users = User.query.all()
            print(f"\n📋 Found {len(users)} users:")
            for user in users:
                print(f"  - {user.username} (Role: {user.role.name if user.role else 'None'})")
                print(f"    Assigned ports: {user.assigned_ports}")
                print(f"    Is admin: {user.has_permission('manage_users')}")
            
            # Check gateways and ports
            gateways = GSMGateway.query.all()
            print(f"\n🌐 Found {len(gateways)} gateways:")
            for gateway in gateways:
                ports = SMSPort.query.filter_by(gateway_id=gateway.id).all()
                print(f"  - Gateway {gateway.id}: {gateway.name}")
                print(f"    Ports: {[f'P{p.port_number}(ID:{p.id})' for p in ports]}")
            
            # Test creating a user with port assignments
            print(f"\n🆕 Creating test user with port assignments...")
            
            # Create test user
            test_user = User(
                username='testuser',
                email='<EMAIL>',
                first_name='Test',
                last_name='User',
                assigned_ports=[
                    {'gateway_id': 1, 'port': '1'},
                    {'gateway_id': 1, 'port': '2'}
                ],
                is_active=True
            )
            test_user.set_password('test123')
            
            # Check if user already exists
            existing = User.query.filter_by(username='testuser').first()
            if existing:
                print("  ⚠️  Test user already exists, updating...")
                existing.assigned_ports = [
                    {'gateway_id': 1, 'port': '1'},
                    {'gateway_id': 1, 'port': '2'}
                ]
                db.session.commit()
                test_user = existing
            else:
                db.session.add(test_user)
                db.session.commit()
                print("  ✅ Test user created")
            
            # Test port access
            print(f"\n🔍 Testing port access for {test_user.username}:")
            print(f"  Assigned ports: {test_user.assigned_ports}")
            
            # Test can_use_port method
            for gateway in gateways:
                for port_num in ['1', '2', '3', '4']:
                    can_use = test_user.can_use_port(port_num, gateway.id)
                    print(f"  Can use Gateway {gateway.id}, Port {port_num}: {can_use}")
            
            # Test GSMPortService
            from app.sms.services import GSMPortService
            available_ports = GSMPortService.get_user_available_ports(test_user)
            print(f"\n📡 Available ports from service: {len(available_ports)}")
            for port in available_ports:
                print(f"  - Port ID {port.id}: Gateway {port.gateway_id}, Port {port.port_number}")
            
            print(f"\n✅ Test completed successfully!")
            print(f"📝 Test user credentials: testuser / test123")
            
        except Exception as e:
            print(f"❌ Error during test: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_user_ports()
