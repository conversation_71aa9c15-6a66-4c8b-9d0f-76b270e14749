{% extends "base.html" %}

{% block title %}Inbox - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">SMS Inbox</h1>
        <p class="text-muted">Received messages</p>
    </div>
    <div>
        <button class="btn btn-outline-primary" onclick="refreshInbox()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-inbox"></i> Received Messages</h5>
        <span class="badge bg-primary">{{ messages.total if messages else 0 }} messages</span>
    </div>
    <div class="card-body">
        {% if messages and messages.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>From</th>
                            <th>Message</th>
                            <th>Port</th>
                            <th>Received</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for message in messages.items %}
                        <tr>
                            <td>
                                <a href="{{ url_for('sms.conversation', phone_number=message.phone_number) }}" 
                                   class="text-decoration-none fw-bold">
                                    {{ message.phone_number }}
                                </a>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 300px;">
                                    {{ message.content }}
                                </div>
                            </td>
                            <td>
                                {% if message.port %}
                                    <span class="badge bg-info">Port {{ message.port.port_number }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">Unknown</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ message.created_at.strftime('%m/%d/%Y %H:%M') }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('sms.conversation', phone_number=message.phone_number) }}" 
                                       class="btn btn-outline-primary" title="View Conversation">
                                        <i class="bi bi-chat-dots"></i>
                                    </a>
                                    <a href="{{ url_for('sms.message_detail', message_id=message.id) }}" 
                                       class="btn btn-outline-info" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if messages.pages > 1 %}
            <nav aria-label="Inbox pagination">
                <ul class="pagination justify-content-center">
                    {% if messages.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sms.inbox', page=messages.prev_num) }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in messages.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != messages.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('sms.inbox', page=page_num) }}">{{ page_num }}</a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if messages.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sms.inbox', page=messages.next_num) }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">No Messages</h4>
                <p class="text-muted">You haven't received any SMS messages yet.</p>
                <p class="text-muted">Messages will appear here when they are received through your GSM gateways.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function refreshInbox() {
        location.reload();
    }

    // Auto-refresh every 30 seconds
    setInterval(refreshInbox, 30000);
</script>
{% endblock %}
