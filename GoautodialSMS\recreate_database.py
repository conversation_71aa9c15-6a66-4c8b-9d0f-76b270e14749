#!/usr/bin/env python3
"""
Recreate database with correct schema
"""

import sys
import os
sys.path.append('backend')

from app import app, db, User, SMSMessage, SMSPort
from werkzeug.security import generate_password_hash
import sqlite3

def backup_existing_data():
    """Backup existing user and message data"""
    print("📦 Backing up existing data...")
    
    backup_data = {
        'users': [],
        'messages': []
    }
    
    try:
        with app.app_context():
            # Backup users
            users = User.query.all()
            for user in users:
                backup_data['users'].append({
                    'username': user.username,
                    'email': user.email,
                    'password_hash': user.password_hash,
                    'role': user.role,
                    'is_active': user.is_active,
                    'can_send_sms': user.can_send_sms,
                    'can_view_inbox': user.can_view_inbox,
                    'can_view_outbox': user.can_view_outbox,
                    'can_export': user.can_export,
                    'can_manage_users': user.can_manage_users,
                    'can_view_reports': user.can_view_reports,
                    'can_manage_settings': user.can_manage_settings,
                    'can_manage_ports': user.can_manage_ports,
                    'assigned_ports': user.assigned_ports
                })
            
            # Backup messages
            messages = SMSMessage.query.all()
            for msg in messages:
                backup_data['messages'].append({
                    'message_id': msg.message_id,
                    'direction': msg.direction,
                    'phone_number': msg.phone_number,
                    'content': msg.content,
                    'status': msg.status,
                    'created_at': msg.created_at,
                    'gsm_port': msg.gsm_port,
                    'smsc': msg.smsc,
                    'reply_to_id': msg.reply_to_id
                })
            
            print(f"   ✅ Backed up {len(backup_data['users'])} users")
            print(f"   ✅ Backed up {len(backup_data['messages'])} messages")
            
    except Exception as e:
        print(f"   ⚠️ Error backing up data: {str(e)}")
    
    return backup_data

def recreate_database():
    """Recreate database with correct schema"""
    print("\n🔧 Recreating database with correct schema...")
    
    # Remove existing database files
    db_files = [
        'backend/sms_system.db',
        'backend/instance/sms_system.db',
        'instance/sms_system.db'
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            os.remove(db_file)
            print(f"   🗑️ Removed {db_file}")
    
    # Create directories if they don't exist
    os.makedirs('backend/instance', exist_ok=True)
    os.makedirs('instance', exist_ok=True)
    
    # Create new database with correct schema
    with app.app_context():
        db.create_all()
        print("   ✅ Created new database with correct schema")

def restore_data(backup_data):
    """Restore backed up data"""
    print("\n📥 Restoring backed up data...")
    
    with app.app_context():
        try:
            # Restore users
            for user_data in backup_data['users']:
                user = User(
                    username=user_data['username'],
                    email=user_data['email'],
                    password_hash=user_data['password_hash'],
                    role=user_data['role'],
                    is_active=user_data['is_active'],
                    can_send_sms=user_data['can_send_sms'],
                    can_view_inbox=user_data['can_view_inbox'],
                    can_view_outbox=user_data['can_view_outbox'],
                    can_export=user_data['can_export'],
                    can_manage_users=user_data['can_manage_users'],
                    can_view_reports=user_data['can_view_reports'],
                    can_manage_settings=user_data['can_manage_settings'],
                    can_manage_ports=user_data['can_manage_ports'],
                    assigned_ports=user_data['assigned_ports']
                )
                db.session.add(user)
            
            # Restore messages
            for msg_data in backup_data['messages']:
                message = SMSMessage(
                    message_id=msg_data['message_id'],
                    direction=msg_data['direction'],
                    phone_number=msg_data['phone_number'],
                    content=msg_data['content'],
                    status=msg_data['status'],
                    created_at=msg_data['created_at'],
                    gsm_port=msg_data['gsm_port'],
                    smsc=msg_data['smsc'],
                    reply_to_id=msg_data['reply_to_id']
                )
                db.session.add(message)
            
            db.session.commit()
            print(f"   ✅ Restored {len(backup_data['users'])} users")
            print(f"   ✅ Restored {len(backup_data['messages'])} messages")
            
        except Exception as e:
            print(f"   ❌ Error restoring data: {str(e)}")
            db.session.rollback()

def initialize_default_ports():
    """Initialize default SMS ports"""
    print("\n📱 Initializing SMS ports...")
    
    with app.app_context():
        try:
            # Create default ports (1-8)
            for port_num in range(1, 9):
                port = SMSPort(
                    port_number=str(port_num),
                    status='active',
                    network_name='',
                    signal_quality='',
                    sim_imsi='',
                    is_active=True
                )
                db.session.add(port)
            
            db.session.commit()
            print(f"   ✅ Created 8 default SMS ports")
            
        except Exception as e:
            print(f"   ❌ Error creating ports: {str(e)}")
            db.session.rollback()

def create_default_admin():
    """Create default admin user if none exists"""
    print("\n👤 Checking admin user...")
    
    with app.app_context():
        try:
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    role='admin',
                    can_send_sms=True,
                    can_view_inbox=True,
                    can_view_outbox=True,
                    can_export=True,
                    can_manage_users=True,
                    can_view_reports=True,
                    can_manage_settings=True,
                    can_manage_ports=True
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("   ✅ Created default admin user (admin/admin123)")
            else:
                print("   ✅ Admin user already exists")
                
        except Exception as e:
            print(f"   ❌ Error creating admin user: {str(e)}")
            db.session.rollback()

def test_database():
    """Test that database is working correctly"""
    print("\n🧪 Testing database...")
    
    with app.app_context():
        try:
            # Test SMS ports query
            ports = SMSPort.query.all()
            print(f"   ✅ SMS Ports query successful: {len(ports)} ports")
            
            # Test users query
            users = User.query.all()
            print(f"   ✅ Users query successful: {len(users)} users")
            
            # Test user editing functionality
            admin_user = User.query.filter_by(username='admin').first()
            if admin_user:
                has_settings_perm = admin_user.has_permission('can_manage_settings')
                print(f"   ✅ User permissions working: can_manage_settings = {has_settings_perm}")
            
            print("   ✅ Database is working correctly!")
            return True
            
        except Exception as e:
            print(f"   ❌ Database test failed: {str(e)}")
            return False

def copy_to_all_locations():
    """Copy the working database to all possible locations"""
    print("\n📋 Copying database to all locations...")
    
    source_db = 'backend/sms_system.db'
    target_locations = [
        'backend/instance/sms_system.db',
        'instance/sms_system.db'
    ]
    
    for target in target_locations:
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(target), exist_ok=True)
            
            # Copy database
            import shutil
            shutil.copy2(source_db, target)
            print(f"   ✅ Copied to {target}")
            
        except Exception as e:
            print(f"   ❌ Error copying to {target}: {str(e)}")

def main():
    print("🔄 DATABASE RECREATION UTILITY")
    print("=" * 35)
    
    # Step 1: Backup existing data
    backup_data = backup_existing_data()
    
    # Step 2: Recreate database
    recreate_database()
    
    # Step 3: Restore data
    restore_data(backup_data)
    
    # Step 4: Initialize ports
    initialize_default_ports()
    
    # Step 5: Ensure admin user exists
    create_default_admin()
    
    # Step 6: Test database
    if test_database():
        # Step 7: Copy to all locations
        copy_to_all_locations()
        
        print(f"\n🎉 DATABASE RECREATION COMPLETED SUCCESSFULLY!")
        print("=" * 45)
        print("✅ Database schema is now correct")
        print("✅ All data has been preserved")
        print("✅ SMS ports are properly initialized")
        print("✅ User editing should work without errors")
        print("✅ GSM Gateway GUI should be accessible")
        
        return True
    else:
        print(f"\n⚠️ Database recreation failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🚀 You can now restart the Flask app and everything should work!")
    else:
        print(f"\n⚠️ Please check the errors above and try again.")
