{% extends "base.html" %}

{% block title %}Login - Modern SMS Manager{% endblock %}

{% block login_content %}
<div class="row justify-content-center align-items-center min-vh-100">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow-lg border-0">
            <div class="card-body p-5">
                <!-- Logo and Title -->
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <i class="bi bi-chat-square-dots text-primary" style="font-size: 3rem;"></i>
                    </div>
                    <h2 class="fw-bold text-dark">Modern SMS Manager</h2>
                    <p class="text-muted">Sign in to your account</p>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }}"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Login Form -->
                <form method="POST" id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label fw-semibold">Username</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-person"></i>
                            </span>
                            <input type="text" 
                                   class="form-control border-start-0" 
                                   id="username" 
                                   name="username" 
                                   placeholder="Enter your username"
                                   required 
                                   autofocus>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label fw-semibold">Password</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-lock"></i>
                            </span>
                            <input type="password" 
                                   class="form-control border-start-0" 
                                   id="password" 
                                   name="password" 
                                   placeholder="Enter your password"
                                   required>
                            <button class="btn btn-outline-secondary" 
                                    type="button" 
                                    onclick="togglePassword()"
                                    id="togglePasswordBtn">
                                <i class="bi bi-eye" id="togglePasswordIcon"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">
                            Remember me
                        </label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                            <span class="btn-text">Sign In</span>
                            <span class="loading-spinner d-none"></span>
                        </button>
                    </div>
                </form>

                <!-- Default Credentials Info -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="fw-bold text-muted mb-2">
                        <i class="bi bi-info-circle"></i> Default Credentials
                    </h6>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Username:</small><br>
                            <code>admin</code>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Password:</small><br>
                            <code>admin123</code>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="text-center mt-4">
                    <small class="text-muted">
                        Modern SMS Manager v1.0.0<br>
                        Secure SMS Management System
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .min-vh-100 {
        min-height: 100vh !important;
    }
    
    .input-group-text {
        border-radius: 10px 0 0 10px;
    }
    
    .form-control {
        border-radius: 0 10px 10px 0;
    }
    
    .form-control:focus {
        box-shadow: none;
        border-color: var(--primary-color);
    }
    
    .input-group .form-control:focus + .btn {
        border-color: var(--primary-color);
    }
    
    .card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }
    
    code {
        background: rgba(var(--bs-primary-rgb), 0.1);
        color: var(--primary-color);
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('togglePasswordIcon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'bi bi-eye';
        }
    }

    // Handle form submission
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        const loginBtn = document.getElementById('loginBtn');
        const btnText = loginBtn.querySelector('.btn-text');
        const spinner = loginBtn.querySelector('.loading-spinner');
        
        // Show loading state
        btnText.classList.add('d-none');
        spinner.classList.remove('d-none');
        loginBtn.disabled = true;
        
        // Form will submit normally, loading state will be reset on page reload
    });

    // Auto-fill demo credentials
    document.addEventListener('DOMContentLoaded', function() {
        // Add click handlers for demo credentials
        const usernameCode = document.querySelector('code:contains("admin")');
        const passwordCode = document.querySelector('code:contains("admin123")');
        
        // You can add click handlers here to auto-fill if needed
    });

    // Focus management
    document.addEventListener('DOMContentLoaded', function() {
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        
        // Auto-focus username field
        usernameInput.focus();
        
        // Move to password field when username is entered
        usernameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                passwordInput.focus();
            }
        });
    });
</script>
{% endblock %}
