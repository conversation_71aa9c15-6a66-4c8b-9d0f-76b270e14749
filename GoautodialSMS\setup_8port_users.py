#!/usr/bin/env python3
"""
Set up users for 8-port GSM gateway
"""

import sys
import os
sys.path.append('backend')

from app import app, db, User
from werkzeug.security import generate_password_hash

def setup_8port_users():
    with app.app_context():
        print("👥 Setting Up Users for 8-Port GSM Gateway")
        print("=" * 50)
        
        # Define users for 8-port system
        users_to_create = [
            {
                'username': 'sales_team',
                'email': '<EMAIL>',
                'password': 'sales123',
                'role': 'user',
                'assigned_ports': '1,2',
                'permissions': {
                    'can_send_sms': True,
                    'can_view_inbox': True,
                    'can_view_outbox': True,
                    'can_export': True
                },
                'description': 'Sales team - handles customer inquiries and sales communications'
            },
            {
                'username': 'support_team',
                'email': '<EMAIL>', 
                'password': 'support123',
                'role': 'user',
                'assigned_ports': '3,4',
                'permissions': {
                    'can_send_sms': True,
                    'can_view_inbox': True,
                    'can_view_outbox': True,
                    'can_export': True,
                    'can_view_reports': True
                },
                'description': 'Support team - customer service and technical support'
            },
            {
                'username': 'marketing_team',
                'email': '<EMAIL>',
                'password': 'marketing123', 
                'role': 'user',
                'assigned_ports': '5,6',
                'permissions': {
                    'can_send_sms': True,
                    'can_view_inbox': True,
                    'can_view_outbox': True,
                    'can_export': True
                },
                'description': 'Marketing team - campaigns and promotional messages'
            },
            {
                'username': 'operations_team',
                'email': '<EMAIL>',
                'password': 'ops123',
                'role': 'user', 
                'assigned_ports': '7,8',
                'permissions': {
                    'can_send_sms': True,
                    'can_view_inbox': True,
                    'can_view_outbox': True,
                    'can_export': True,
                    'can_view_reports': True
                },
                'description': 'Operations team - internal communications and alerts'
            }
        ]
        
        created_users = []
        
        for user_data in users_to_create:
            # Check if user already exists
            existing_user = User.query.filter_by(username=user_data['username']).first()
            
            if existing_user:
                print(f"   ⚠️ User '{user_data['username']}' already exists, updating...")
                user = existing_user
            else:
                print(f"   ✅ Creating user '{user_data['username']}'...")
                user = User(
                    username=user_data['username'],
                    email=user_data['email'],
                    password_hash=generate_password_hash(user_data['password']),
                    role=user_data['role']
                )
                db.session.add(user)
            
            # Update user properties
            user.assigned_ports = user_data['assigned_ports']
            
            # Set permissions
            for perm, value in user_data['permissions'].items():
                setattr(user, perm, value)
            
            created_users.append({
                'username': user_data['username'],
                'password': user_data['password'],
                'ports': user_data['assigned_ports'],
                'description': user_data['description']
            })
        
        db.session.commit()
        
        print(f"\n🎉 Successfully configured {len(created_users)} users for 8-port system!")
        
        # Display user summary
        print(f"\n📋 User Summary:")
        print(f"{'Username':<15} {'Password':<12} {'Ports':<8} {'Description'}")
        print("-" * 70)
        
        for user in created_users:
            print(f"{user['username']:<15} {user['password']:<12} {user['ports']:<8} {user['description'][:40]}")
        
        # Show port allocation
        print(f"\n🔌 Port Allocation Summary:")
        print(f"   Port 1-2: sales_team")
        print(f"   Port 3-4: support_team") 
        print(f"   Port 5-6: marketing_team")
        print(f"   Port 7-8: operations_team")
        print(f"   All ports: admin (system administration)")
        
        # Verify the setup
        print(f"\n🔍 Verification:")
        all_users = User.query.all()
        for user in all_users:
            assigned_ports = user.get_assigned_ports()
            if user.role == 'admin':
                print(f"   🔑 {user.username}: Admin access to all ports")
            elif assigned_ports:
                print(f"   👤 {user.username}: Ports {assigned_ports}")
            else:
                print(f"   👤 {user.username}: No port restrictions")

if __name__ == "__main__":
    setup_8port_users()
