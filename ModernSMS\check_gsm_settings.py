#!/usr/bin/env python3
"""
Check what changed in GSM settings after IP change
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def check_current_gsm_status():
    """Check current GSM gateway status and settings"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔍 CHECKING CURRENT GSM GATEWAY STATUS")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        print(f"📡 Current Gateway: {gateway.name}")
        print(f"   IP: {gateway.ip_address}:{gateway.tg_sms_port}")
        print(f"   Credentials: {gateway.username}/{gateway.password}")
        print(f"   Last Updated: {gateway.updated_at}")
        
        try:
            # Test basic connection
            print(f"\n🌐 Testing connection...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Read initial response
            initial = sock.recv(1024).decode()
            print(f"📥 Initial response: {initial.strip()}")
            
            # Send login
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            # Read login response
            login_resp = sock.recv(1024).decode()
            print(f"📥 Login response: {login_resp.strip()}")
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed - this might be the issue!")
                sock.close()
                return False
            
            print("✅ Login successful")
            
            # Check what commands we can run
            print(f"\n🔍 Checking available commands...")
            
            # Try to get system info
            info_cmd = "Action: Command\r\nCommand: core show version\r\n\r\n"
            sock.send(info_cmd.encode())
            info_resp = sock.recv(2048).decode()
            print(f"📥 System info: {info_resp.strip()}")
            
            # Try to check TG SMS status
            tgsms_cmd = "Action: Command\r\nCommand: tgsms show status\r\n\r\n"
            sock.send(tgsms_cmd.encode())
            tgsms_resp = sock.recv(2048).decode()
            print(f"📥 TG SMS status: {tgsms_resp.strip()}")
            
            # Try to check SMS configuration
            sms_cmd = "Action: Command\r\nCommand: sms show config\r\n\r\n"
            sock.send(sms_cmd.encode())
            sms_resp = sock.recv(2048).decode()
            print(f"📥 SMS config: {sms_resp.strip()}")
            
            # Most importantly, check event subscription
            print(f"\n📡 Testing event subscription...")
            event_cmd = "Action: Events\r\nEventMask: all\r\n\r\n"
            sock.send(event_cmd.encode())
            
            event_resp = sock.recv(1024).decode()
            print(f"📥 Event response: {event_resp.strip()}")
            
            if "Events: On" in event_resp:
                print("🎉 Events are enabled - SMS should work!")
                return True
            elif "Events: Off" in event_resp:
                print("❌ Events are disabled - this is the problem!")
                return False
            else:
                print("⚠️  Unclear event status")
                return False
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Error checking gateway: {str(e)}")
            return False

def try_different_event_commands():
    """Try different ways to enable events"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🧪 TRYING DIFFERENT EVENT ENABLE METHODS")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        # Different event enable commands to try
        event_commands = [
            "Action: Events\r\nEventMask: all\r\n\r\n",
            "Action: Events\r\nEventMask: sms\r\n\r\n",
            "Action: Events\r\nEventMask: message\r\n\r\n",
            "Action: Events\r\nEventMask: *\r\n\r\n",
            "Action: Events\r\nEventMask: call,sms\r\n\r\n"
        ]
        
        for i, event_cmd in enumerate(event_commands, 1):
            print(f"\n🔧 Method {i}: {event_cmd.strip()}")
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                sock.connect((gateway.ip_address, gateway.tg_sms_port))
                
                # Login first
                initial = sock.recv(1024).decode()
                login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
                sock.send(login_cmd.encode())
                login_resp = sock.recv(1024).decode()
                
                if "Response: Success" not in login_resp:
                    print(f"   ❌ Login failed")
                    sock.close()
                    continue
                
                # Try the event command
                sock.send(event_cmd.encode())
                event_resp = sock.recv(1024).decode()
                print(f"   📥 Response: {event_resp.strip()}")
                
                if "Events: On" in event_resp or "Success" in event_resp:
                    print(f"   🎉 Method {i} worked! Events enabled!")
                    
                    # Test by listening for a few seconds
                    print(f"   👂 Listening for events...")
                    sock.settimeout(3)
                    
                    try:
                        test_data = sock.recv(1024).decode()
                        if test_data:
                            print(f"   📨 Received: {test_data.strip()}")
                    except socket.timeout:
                        print(f"   ⏰ No immediate events")
                    
                    sock.close()
                    return i  # Return the working method number
                else:
                    print(f"   ❌ Method {i} failed")
                
                sock.close()
                
            except Exception as e:
                print(f"   ❌ Error with method {i}: {str(e)}")
        
        return None

def check_received_messages_history():
    """Check if there are any recent received messages"""
    app = create_app()
    
    with app.app_context():
        print(f"\n📊 CHECKING MESSAGE HISTORY")
        print("="*60)
        
        from app.models import SMSMessage
        from datetime import datetime, timedelta
        
        # Check messages from last 24 hours
        yesterday = datetime.now() - timedelta(hours=24)
        recent_messages = SMSMessage.query.filter(
            SMSMessage.direction == 'inbound',
            SMSMessage.created_at >= yesterday
        ).order_by(SMSMessage.created_at.desc()).all()
        
        print(f"📥 Messages received in last 24 hours: {len(recent_messages)}")
        
        if recent_messages:
            print(f"📱 Recent messages:")
            for msg in recent_messages[:5]:
                print(f"   - {msg.created_at}: From {msg.phone_number}")
                print(f"     Content: {msg.content[:50]}...")
        
        # Check when SMS receiving stopped working
        all_received = SMSMessage.query.filter_by(direction='inbound').order_by(SMSMessage.created_at.desc()).limit(10).all()
        
        if all_received:
            latest = all_received[0]
            time_since = datetime.now() - latest.created_at
            hours_since = int(time_since.total_seconds() / 3600)
            
            print(f"\n⏰ Last received SMS: {latest.created_at}")
            print(f"   From: {latest.phone_number}")
            print(f"   Time since: {hours_since} hours ago")
            
            if hours_since > 1:
                print(f"   ⚠️  SMS receiving stopped {hours_since} hours ago")
            else:
                print(f"   ✅ Recent SMS activity")

if __name__ == '__main__':
    print("🚀 GSM Gateway Settings Checker")
    print("🎯 Finding what changed after IP address change")
    
    # Check current status
    events_working = check_current_gsm_status()
    
    if not events_working:
        print(f"\n🔧 Events are disabled. Trying to enable them...")
        working_method = try_different_event_commands()
        
        if working_method:
            print(f"\n✅ Found working method {working_method} to enable events!")
            print(f"💡 The SMS receiver should now work")
        else:
            print(f"\n❌ Could not enable events with any method")
            print(f"💡 Check your GSM gateway web interface:")
            print(f"   1. Look for TG SMS or Event settings")
            print(f"   2. Enable SMS events/notifications")
            print(f"   3. Check user permissions for apiuser")
    else:
        print(f"\n✅ Events are enabled - SMS should be working!")
    
    # Check message history
    check_received_messages_history()
    
    print("\n" + "="*60)
    print("💡 If events are enabled but SMS still not working:")
    print("   1. Restart the SMS receiver from dashboard")
    print("   2. Send a test SMS to your gateway number")
    print("   3. Check application logs for SMS events")
    print("="*60)
