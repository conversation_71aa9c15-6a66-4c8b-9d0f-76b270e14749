{% extends "base.html" %}

{% block title %}Message Details - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Message Details</h1>
        <p class="text-muted">{{ message.direction.title() }} message</p>
    </div>
    <div>
        <a href="{{ url_for('sms.conversation', phone_number=message.phone_number) }}" class="btn btn-outline-primary">
            <i class="bi bi-chat-dots"></i> View Conversation
        </a>
        <a href="{{ url_for('sms.inbox' if message.direction == 'inbound' else 'sms.outbox') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-{{ 'inbox' if message.direction == 'inbound' else 'send' }}"></i>
                    {{ 'Received' if message.direction == 'inbound' else 'Sent' }} Message
                </h5>
            </div>
            <div class="card-body">
                <div class="message-display p-4 border rounded bg-light">
                    <div class="message-content">
                        <h6 class="fw-bold mb-3">
                            {% if message.direction == 'inbound' %}
                                From: {{ message.phone_number }}
                            {% else %}
                                To: {{ message.phone_number }}
                            {% endif %}
                        </h6>
                        
                        <div class="message-text mb-3" style="font-size: 1.1rem; line-height: 1.5;">
                            {{ message.content }}
                        </div>
                        
                        <div class="message-meta">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>{{ 'Received' if message.direction == 'inbound' else 'Sent' }}:</strong><br>
                                        {{ message.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                    </small>
                                </div>
                                
                                {% if message.direction == 'outbound' and message.sent_at %}
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Delivered:</strong><br>
                                        {{ message.sent_at.strftime('%B %d, %Y at %I:%M %p') }}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if message.direction == 'outbound' and current_user.can_send_sms %}
                <div class="mt-4">
                    <h6>Quick Actions</h6>
                    <div class="d-flex gap-2">
                        <a href="{{ url_for('sms.compose') }}?phone={{ message.phone_number }}" class="btn btn-outline-primary">
                            <i class="bi bi-reply"></i> Send Another
                        </a>
                        <a href="{{ url_for('sms.conversation', phone_number=message.phone_number) }}" class="btn btn-outline-info">
                            <i class="bi bi-chat-dots"></i> View Conversation
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Message Information</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Message ID:</strong><br>
                    <small class="text-muted font-monospace">{{ message.message_id }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Direction:</strong><br>
                    {% if message.direction == 'inbound' %}
                        <span class="badge bg-info">
                            <i class="bi bi-arrow-down"></i> Inbound
                        </span>
                    {% else %}
                        <span class="badge bg-primary">
                            <i class="bi bi-arrow-up"></i> Outbound
                        </span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    {% if message.status == 'sent' %}
                        <span class="badge bg-success">Sent</span>
                    {% elif message.status == 'delivered' %}
                        <span class="badge bg-success">Delivered</span>
                    {% elif message.status == 'failed' %}
                        <span class="badge bg-danger">Failed</span>
                    {% elif message.status == 'pending' %}
                        <span class="badge bg-warning">Pending</span>
                    {% elif message.status == 'received' %}
                        <span class="badge bg-info">Received</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ message.status.title() }}</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>Phone Number:</strong><br>
                    <span class="text-muted">{{ message.phone_number }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Character Count:</strong><br>
                    <span class="text-muted">{{ message.content|length }} characters</span>
                </div>
                
                {% if message.port %}
                <div class="mb-3">
                    <strong>Port:</strong><br>
                    <span class="text-muted">Port {{ message.port.port_number }}</span>
                </div>
                {% endif %}
                
                {% if message.gateway %}
                <div class="mb-3">
                    <strong>Gateway:</strong><br>
                    <span class="text-muted">{{ message.gateway.name }}</span>
                </div>
                {% endif %}
                
                {% if message.direction == 'outbound' and message.user %}
                <div class="mb-3">
                    <strong>Sent By:</strong><br>
                    <span class="text-muted">{{ message.user.full_name }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        {% if message.direction == 'inbound' and current_user.can_send_sms %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-reply"></i> Quick Reply</h6>
            </div>
            <div class="card-body">
                <form id="quickReplyForm">
                    <div class="mb-3">
                        <textarea class="form-control" id="replyMessage" placeholder="Type your reply..." rows="3" maxlength="160"></textarea>
                        <div class="form-text">
                            <span id="charCount">0</span>/160 characters
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <select class="form-select" id="replyPort">
                            <option value="">Select port...</option>
                            {% if message.port %}
                                <option value="{{ message.port.id }}" selected>
                                    Port {{ message.port.port_number }} (Same as received)
                                </option>
                            {% endif %}
                            <!-- Add other available ports here if needed -->
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-send"></i> Send Reply
                    </button>
                </form>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .message-display {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-left: 4px solid var(--primary-color);
    }
    
    .message-text {
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    
    .font-monospace {
        font-family: 'Courier New', monospace;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Character counter
    const replyMessage = document.getElementById('replyMessage');
    if (replyMessage) {
        replyMessage.addEventListener('input', function() {
            const charCount = this.value.length;
            document.getElementById('charCount').textContent = charCount;
            
            if (charCount > 160) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });
    }

    // Handle quick reply form
    const quickReplyForm = document.getElementById('quickReplyForm');
    if (quickReplyForm) {
        quickReplyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = document.getElementById('replyMessage').value.trim();
            const portId = document.getElementById('replyPort').value;
            
            if (!message) {
                showToast('Please enter a message', 'error');
                return;
            }
            
            if (!portId) {
                showToast('Please select a port', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('phone_number', '{{ message.phone_number }}');
            formData.append('message', message);
            formData.append('port_id', portId);
            
            fetch('{{ url_for("sms.send_sms") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Reply sent successfully!', 'success');
                    document.getElementById('replyMessage').value = '';
                    document.getElementById('charCount').textContent = '0';
                } else {
                    showToast('Error: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('Failed to send reply: ' + error.message, 'error');
            });
        });
    }
</script>
{% endblock %}
