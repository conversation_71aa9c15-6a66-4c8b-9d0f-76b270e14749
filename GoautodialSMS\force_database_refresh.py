#!/usr/bin/env python3
"""
Force Database Refresh
"""

import sys
import os
sys.path.append('backend')

def force_database_refresh():
    """Force refresh the database to ensure correct data"""
    print("🔄 Force Database Refresh")
    print("=" * 30)
    
    try:
        from app import app, db, GSMGateway, SMSPort, SMS_API_CONFIG, TG_SMS_CONFIG
        
        with app.app_context():
            print("📋 Current Database State:")
            
            # Check current gateways
            gateways = GSMGateway.query.all()
            print(f"   Gateways: {len(gateways)}")
            
            for gateway in gateways:
                print(f"      Gateway {gateway.id}: {gateway.name}")
                print(f"         Max Ports: {gateway.max_ports}")
                print(f"         Status: {gateway.status}")
            
            # Check current ports
            ports = SMSPort.query.all()
            print(f"   Ports: {len(ports)}")
            
            for port in ports:
                print(f"      Port {port.id}: {port.port_number}")
                print(f"         Gateway ID: {port.gateway_id}")
                print(f"         Status: {port.status}")
                print(f"         Active: {port.is_active}")
            
            # Force update the gateway to have 8 ports
            print(f"\n🔧 Forcing Gateway Update:")
            
            if gateways:
                gateway = gateways[0]
                print(f"   Updating gateway {gateway.name}")
                print(f"   Current max_ports: {gateway.max_ports}")
                
                # Force update max_ports to 8
                gateway.max_ports = 8
                db.session.commit()
                
                print(f"   Updated max_ports to: {gateway.max_ports}")
                
                # Verify the update
                updated_gateway = GSMGateway.query.get(gateway.id)
                print(f"   Verified max_ports: {updated_gateway.max_ports}")
                
                # Test to_dict method
                gateway_dict = updated_gateway.to_dict()
                print(f"   to_dict() max_ports: {gateway_dict['max_ports']}")
            
            else:
                print("   No gateways found, creating new one...")
                
                # Create main gateway
                main_gateway = GSMGateway(
                    name='Main Gateway',
                    ip_address=SMS_API_CONFIG['ip'],
                    api_port='80',
                    tg_sms_port=str(TG_SMS_CONFIG['port']),
                    username=SMS_API_CONFIG['account'],
                    password=SMS_API_CONFIG['password'],
                    max_ports=8,  # Force 8 ports
                    status='active',
                    location='Main Office',
                    description='Primary GSM gateway (force created)',
                    is_primary=True
                )
                db.session.add(main_gateway)
                db.session.flush()
                
                # Associate existing ports with this gateway
                existing_ports = SMSPort.query.filter_by(is_active=True).all()
                for port in existing_ports:
                    port.gateway_id = main_gateway.id
                
                db.session.commit()
                print(f"   Created gateway with {main_gateway.max_ports} ports")
            
            # Ensure we have 8 active ports
            print(f"\n📱 Ensuring 8 Active Ports:")
            
            active_ports = SMSPort.query.filter_by(is_active=True).all()
            print(f"   Current active ports: {len(active_ports)}")
            
            # Get the gateway ID
            gateway = GSMGateway.query.first()
            gateway_id = gateway.id if gateway else 1
            
            # Ensure ports 1-8 exist and are active
            for i in range(1, 9):
                port = SMSPort.query.filter_by(port_number=str(i)).first()
                if not port:
                    # Create missing port
                    new_port = SMSPort(
                        port_number=str(i),
                        status='detected',
                        is_active=True,
                        gateway_id=gateway_id
                    )
                    db.session.add(new_port)
                    print(f"   Created port {i}")
                else:
                    # Ensure port is active and assigned to gateway
                    port.is_active = True
                    port.gateway_id = gateway_id
                    print(f"   Updated port {i}")
            
            db.session.commit()
            
            # Final verification
            print(f"\n✅ Final Verification:")
            final_gateways = GSMGateway.query.all()
            final_ports = SMSPort.query.filter_by(is_active=True).all()
            
            print(f"   Gateways: {len(final_gateways)}")
            for gw in final_gateways:
                print(f"      {gw.name}: {gw.max_ports} max ports")
            
            print(f"   Active Ports: {len(final_ports)}")
            port_numbers = [p.port_number for p in final_ports]
            print(f"      Port numbers: {sorted(port_numbers)}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error refreshing database: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    force_database_refresh()
