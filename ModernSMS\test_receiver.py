#!/usr/bin/env python3
"""
Test SMS receiver functionality
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def test_receiver():
    """Test SMS receiver"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("📱 SMS Receiver Test")
        print("="*60)
        
        try:
            from app.sms.receiver import SMSReceiver
            print("✅ SMS receiver class imported successfully")

            # Create receiver with app context
            receiver = SMSReceiver(app)

            print(f"Running: {receiver.running}")
            print(f"Connected: {receiver.connected}")
            print(f"Thread: {receiver.thread}")

            # Test starting receiver
            print("\n🚀 Testing receiver start...")
            receiver.start_receiver()

            import time
            time.sleep(5)  # Wait longer to see connection attempt

            print(f"After start - Running: {receiver.running}")
            print(f"After start - Thread alive: {receiver.thread.is_alive() if receiver.thread else False}")

            # Test stopping receiver
            print("\n🛑 Testing receiver stop...")
            receiver.stop_receiver()

            print("✅ SMS receiver test completed")
            
        except Exception as e:
            print(f"❌ Error testing SMS receiver: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_receiver()
