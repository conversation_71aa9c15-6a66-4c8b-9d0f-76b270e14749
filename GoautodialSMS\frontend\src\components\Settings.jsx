import React, { useState, useEffect } from 'react';
import { 
  Settings as SettingsIcon, 
  Save, 
  RefreshCw, 
  AlertCircle,
  CheckCircle,
  Server,
  Phone,
  Globe
} from 'lucide-react';
import toast from 'react-hot-toast';
import { smsApi } from '../services/api';

const Settings = () => {
  const [settings, setSettings] = useState({
    sms_api_ip: '',
    sms_api_account: '',
    sms_api_password: '',
    sms_api_port: '',
    tg_sms_ip: '',
    tg_sms_port: '5038',
    tg_sms_username: '',
    tg_sms_password: '',
  });
  
  const [ports, setPorts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState(null);

  useEffect(() => {
    loadSettings();
    loadPorts();
  }, []);

  const loadSettings = async () => {
    try {
      // In a real app, you'd load these from your backend
      // For now, we'll use default values
      setSettings({
        sms_api_ip: '*************',
        sms_api_account: 'apiuser',
        sms_api_password: 'apipass',
        sms_api_port: '1',
        tg_sms_ip: '*************',
        tg_sms_port: '5038',
        tg_sms_username: 'apiuser',
        tg_sms_password: 'apipass',
      });
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const loadPorts = async () => {
    try {
      const response = await smsApi.getPorts();
      setPorts(response.data);
    } catch (error) {
      console.error('Error loading ports:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // In a real app, you'd save these to your backend
      // For now, we'll just show a success message
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const testConnection = async () => {
    try {
      setTestingConnection(true);
      setConnectionStatus(null);
      
      // In a real app, you'd test the connection to your SMS services
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate test
      
      // Simulate random success/failure for demo
      const success = Math.random() > 0.3;
      
      if (success) {
        setConnectionStatus({ success: true, message: 'Connection successful' });
        toast.success('Connection test successful');
      } else {
        setConnectionStatus({ success: false, message: 'Connection failed: Unable to reach SMS server' });
        toast.error('Connection test failed');
      }
    } catch (error) {
      setConnectionStatus({ success: false, message: error.message });
      toast.error('Connection test failed');
    } finally {
      setTestingConnection(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="space-y-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i}>
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-10 bg-gray-200 rounded w-full"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center space-x-3">
              <SettingsIcon className="h-8 w-8 text-gray-600" />
              <span>Settings</span>
            </h1>
            <p className="text-gray-600 mt-1">Configure your SMS system settings</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={testConnection}
              disabled={testingConnection}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 transition-colors"
            >
              <RefreshCw className={`h-4 w-4 ${testingConnection ? 'animate-spin' : ''}`} />
              <span>Test Connection</span>
            </button>
            
            <button
              onClick={handleSave}
              disabled={saving}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              <Save className="h-4 w-4" />
              <span>{saving ? 'Saving...' : 'Save Settings'}</span>
            </button>
          </div>
        </div>

        {/* Connection Status */}
        {connectionStatus && (
          <div className={`mb-6 p-4 rounded-lg border ${
            connectionStatus.success 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center space-x-2">
              {connectionStatus.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600" />
              )}
              <span className={`text-sm font-medium ${
                connectionStatus.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {connectionStatus.message}
              </span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* SMS API Settings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <Globe className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">SMS API Settings</h2>
            </div>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="sms_api_ip" className="block text-sm font-medium text-gray-700 mb-2">
                  API Server IP Address
                </label>
                <input
                  type="text"
                  id="sms_api_ip"
                  name="sms_api_ip"
                  value={settings.sms_api_ip}
                  onChange={handleInputChange}
                  placeholder="*************"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label htmlFor="sms_api_account" className="block text-sm font-medium text-gray-700 mb-2">
                  API Account
                </label>
                <input
                  type="text"
                  id="sms_api_account"
                  name="sms_api_account"
                  value={settings.sms_api_account}
                  onChange={handleInputChange}
                  placeholder="apiuser"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label htmlFor="sms_api_password" className="block text-sm font-medium text-gray-700 mb-2">
                  API Password
                </label>
                <input
                  type="password"
                  id="sms_api_password"
                  name="sms_api_password"
                  value={settings.sms_api_password}
                  onChange={handleInputChange}
                  placeholder="apipass"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label htmlFor="sms_api_port" className="block text-sm font-medium text-gray-700 mb-2">
                  Default SMS Port
                </label>
                <input
                  type="text"
                  id="sms_api_port"
                  name="sms_api_port"
                  value={settings.sms_api_port}
                  onChange={handleInputChange}
                  placeholder="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* TG SMS Server Settings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <Server className="h-6 w-6 text-green-600" />
              <h2 className="text-xl font-semibold text-gray-900">TG SMS Server</h2>
            </div>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="tg_sms_ip" className="block text-sm font-medium text-gray-700 mb-2">
                  TG Server IP Address
                </label>
                <input
                  type="text"
                  id="tg_sms_ip"
                  name="tg_sms_ip"
                  value={settings.tg_sms_ip}
                  onChange={handleInputChange}
                  placeholder="*************"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label htmlFor="tg_sms_port" className="block text-sm font-medium text-gray-700 mb-2">
                  TG Server Port
                </label>
                <input
                  type="text"
                  id="tg_sms_port"
                  name="tg_sms_port"
                  value={settings.tg_sms_port}
                  onChange={handleInputChange}
                  placeholder="5038"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label htmlFor="tg_sms_username" className="block text-sm font-medium text-gray-700 mb-2">
                  Username
                </label>
                <input
                  type="text"
                  id="tg_sms_username"
                  name="tg_sms_username"
                  value={settings.tg_sms_username}
                  onChange={handleInputChange}
                  placeholder="apiuser"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label htmlFor="tg_sms_password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <input
                  type="password"
                  id="tg_sms_password"
                  name="tg_sms_password"
                  value={settings.tg_sms_password}
                  onChange={handleInputChange}
                  placeholder="apipass"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* SMS Ports Status */}
        <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <Phone className="h-6 w-6 text-purple-600" />
              <h2 className="text-xl font-semibold text-gray-900">SMS Ports Status</h2>
            </div>
            <button
              onClick={loadPorts}
              className="flex items-center space-x-2 px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
          
          {ports.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {ports.map((port) => (
                <div key={port.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">Port {port.port_number}</span>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      port.status?.includes('Up') 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {port.status?.includes('Up') ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    {port.network_name && (
                      <div>Network: {port.network_name}</div>
                    )}
                    {port.signal_quality && (
                      <div>Signal: {port.signal_quality}</div>
                    )}
                    {port.sim_imsi && (
                      <div>IMSI: {port.sim_imsi}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Phone className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No SMS ports configured</p>
            </div>
          )}
        </div>

        {/* API Documentation */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">API Configuration</h3>
          <div className="text-sm text-blue-800 space-y-2">
            <p><strong>Outgoing SMS URL Format:</strong></p>
            <code className="block bg-blue-100 p-2 rounded text-xs">
              http://[IP]/cgi/WebCGI?1500101=account=[account]&password=[password]&port=[port]&destination=[phone]&content=[message]
            </code>
            <p className="mt-3"><strong>Incoming SMS:</strong> TCP connection to port 5038</p>
            <p><strong>Rate Limit:</strong> 1 SMS per 10 seconds per port</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
