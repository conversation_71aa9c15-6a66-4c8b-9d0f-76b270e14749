#!/usr/bin/env python3
"""
Test user login with detailed debugging
"""

import requests

BASE_URL = "http://127.0.0.1:5000"

def test_user_login_detailed():
    # Test each user individually
    users_to_test = [
        ('admin', 'admin123'),
        ('test1', 'test123'),
        ('diet', 'test123'),
        ('test2', 'test123')
    ]
    
    for username, password in users_to_test:
        print(f"\n🧪 Testing login for {username}:")
        
        session = requests.Session()
        
        # Get login page first
        login_page = session.get(f"{BASE_URL}/login")
        print(f"  Login page status: {login_page.status_code}")
        
        # Attempt login
        login_data = {'username': username, 'password': password}
        login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
        print(f"  Login POST status: {login_response.status_code}")
        print(f"  Login POST headers: {dict(login_response.headers)}")
        
        if login_response.status_code == 302:
            # Follow redirect
            redirect_url = login_response.headers.get('Location', '')
            print(f"  Redirect to: {redirect_url}")
            
            if 'login' in redirect_url:
                print(f"  ❌ {username} login failed - redirected back to login")
                
                # Check if there are any error messages
                follow_response = session.get(f"{BASE_URL}{redirect_url}")
                if 'Invalid username or password' in follow_response.text:
                    print(f"  ❌ Invalid credentials error")
                elif 'not active' in follow_response.text.lower():
                    print(f"  ❌ Account not active error")
                else:
                    print(f"  ❌ Unknown login error")
            else:
                print(f"  ✅ {username} login successful")
                
                # Test accessing a protected page
                inbox_response = session.get(f"{BASE_URL}/inbox")
                print(f"  Inbox access status: {inbox_response.status_code}")
                
                # Test permissions
                perm_response = session.get(f"{BASE_URL}/debug/check_user_permissions")
                if perm_response.status_code == 200:
                    perm_data = perm_response.json()
                    user_info = perm_data.get('user_info', {})
                    print(f"  User role: {user_info.get('role', 'Unknown')}")
                    print(f"  User active: {user_info.get('is_active', 'Unknown')}")
                    permissions = user_info.get('permissions', {})
                    active_perms = [k for k, v in permissions.items() if v]
                    print(f"  Active permissions: {active_perms}")
        else:
            print(f"  ❌ {username} login failed - no redirect (status: {login_response.status_code})")

if __name__ == "__main__":
    test_user_login_detailed()
