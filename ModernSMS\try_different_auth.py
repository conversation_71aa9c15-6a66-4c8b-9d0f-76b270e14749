#!/usr/bin/env python3
"""
Try different authentication methods and credentials
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def try_different_credentials():
    """Try different credential combinations"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔐 TRYING DIFFERENT CREDENTIALS")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        # Different credential combinations to try
        credentials = [
            ("apiuser", "apipass"),     # Current
            ("admin", "admin"),         # Common default
            ("admin", "password"),      # Common default
            ("manager", "manager"),     # Asterisk default
            ("tgsms", "tgsms"),        # TG SMS default
            ("user", "user"),          # Simple default
            ("sms", "sms"),            # SMS specific
            ("", ""),                  # No auth
        ]
        
        for username, password in credentials:
            print(f"\n🔐 Testing: '{username}' / '{password}'")
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((gateway.ip_address, gateway.tg_sms_port))
                
                # Read initial
                initial = sock.recv(1024).decode()
                
                # Send login
                if username and password:
                    login_cmd = f"Action: Login\r\nUsername: {username}\r\nSecret: {password}\r\n\r\n"
                else:
                    login_cmd = "Action: Login\r\n\r\n"  # Try no credentials
                
                sock.send(login_cmd.encode())
                login_resp = sock.recv(1024).decode()
                
                print(f"   📥 Login: {login_resp.strip()}")
                
                if "Response: Success" in login_resp:
                    print(f"   ✅ Login successful!")
                    
                    # Test SMS command
                    gsm_cmd = "Action: smscommand\r\ncommand: gsm show spans\r\n\r\n"
                    sock.send(gsm_cmd.encode())
                    gsm_resp = sock.recv(2048).decode()
                    
                    if "GSM span" in gsm_resp and "Authentication accepted" not in gsm_resp:
                        print(f"   🎉 SMS COMMANDS WORK WITH THESE CREDENTIALS!")
                        print(f"   📱 GSM Response: {gsm_resp[:100]}...")
                        
                        # Test events
                        event_cmd = "Action: Events\r\n\r\n"
                        sock.send(event_cmd.encode())
                        event_resp = sock.recv(1024).decode()
                        
                        if "Events: On" in event_resp:
                            print(f"   🎉 EVENTS ALSO WORK!")
                            sock.close()
                            return username, password, True
                        else:
                            print(f"   ⚠️  Events: {event_resp.strip()}")
                            sock.close()
                            return username, password, False
                    else:
                        print(f"   ⚠️  SMS commands limited")
                else:
                    print(f"   ❌ Login failed")
                
                sock.close()
                
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        return None, None, False

def try_no_authentication():
    """Try connecting without authentication"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔓 TRYING NO AUTHENTICATION")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Read initial
            initial = sock.recv(1024).decode()
            print(f"📥 Initial: {initial.strip()}")
            
            # Try SMS command without login
            print(f"📱 Trying SMS command without login...")
            gsm_cmd = "Action: smscommand\r\ncommand: gsm show spans\r\n\r\n"
            sock.send(gsm_cmd.encode())
            
            gsm_resp = sock.recv(2048).decode()
            print(f"📥 Response: {gsm_resp.strip()}")
            
            if "GSM span" in gsm_resp:
                print(f"🎉 SMS commands work without authentication!")
                
                # Try events without login
                event_cmd = "Action: Events\r\n\r\n"
                sock.send(event_cmd.encode())
                event_resp = sock.recv(1024).decode()
                print(f"📥 Events: {event_resp.strip()}")
                
                if "Events: On" in event_resp:
                    print(f"🎉 Events also work without authentication!")
                    sock.close()
                    return True
            
            sock.close()
            return False
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False

def check_tg_sms_settings():
    """Check what TG SMS settings might be needed"""
    print(f"\n💡 TG SMS CONFIGURATION CHECKLIST")
    print("="*60)
    print(f"In your TG gateway web interface, check these settings:")
    print(f"")
    print(f"📱 SMS → API Settings:")
    print(f"   ✅ Enable API: [✓] Checked")
    print(f"   ✅ Allowed IPs: ************* or 0.0.0.0/0")
    print(f"   ✅ Username: apiuser")
    print(f"   ✅ Password: apipass")
    print(f"")
    print(f"📱 SMS → General Settings:")
    print(f"   ✅ SMS Service: Enabled")
    print(f"   ✅ Event Notifications: Enabled")
    print(f"   ✅ Auto-start SMS service: Enabled")
    print(f"")
    print(f"📱 System → Services:")
    print(f"   ✅ TG SMS Service: Running")
    print(f"   ✅ Manager Interface: Running")
    print(f"")
    print(f"📱 User Management:")
    print(f"   ✅ apiuser permissions: Full access or SMS events")

if __name__ == '__main__':
    print("🚀 Authentication and Credentials Test")
    
    # Try different credentials
    working_user, working_pass, events_work = try_different_credentials()
    
    if working_user:
        print(f"\n🎉 FOUND WORKING CREDENTIALS!")
        print(f"   Username: {working_user}")
        print(f"   Password: {working_pass}")
        print(f"   Events work: {'✅ Yes' if events_work else '❌ No'}")
        
        if events_work:
            print(f"\n✅ SMS receiving should work with these credentials!")
            print(f"💡 Update your gateway settings to use:")
            print(f"   Username: {working_user}")
            print(f"   Password: {working_pass}")
        else:
            print(f"\n⚠️  SMS commands work but events need configuration")
    else:
        print(f"\n❌ No working credentials found with current method")
        
        # Try no authentication
        no_auth_works = try_no_authentication()
        
        if no_auth_works:
            print(f"\n🎉 No authentication required!")
        else:
            print(f"\n❌ Authentication is required")
    
    # Show configuration checklist
    check_tg_sms_settings()
    
    print("\n" + "="*60)
