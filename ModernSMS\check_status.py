#!/usr/bin/env python3
"""
Modern SMS Manager - System Status Check
"""
import os
import sys
import subprocess
from pathlib import Path

def print_header():
    print("="*70)
    print("🔍 Modern SMS Manager - System Status Check")
    print("="*70)

def check_python():
    print("🐍 Python Environment:")
    print(f"   Version: {sys.version.split()[0]}")
    print(f"   Executable: {sys.executable}")
    
    # Check if we're in virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("   ✅ Virtual environment: Active")
    else:
        print("   ⚠️  Virtual environment: Not detected")
    print()

def check_files():
    print("📁 File Structure:")
    required_files = [
        'requirements.txt',
        'config.py', 
        'run.py',
        '.env',
        'app/__init__.py',
        'app/models.py'
    ]
    
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - Missing!")
    print()

def check_dependencies():
    print("📦 Dependencies:")
    required_packages = [
        'flask',
        'flask-sqlalchemy', 
        'flask-login',
        'pymysql',
        'python-dotenv'
    ]
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - Not installed!")
    print()

def check_database():
    print("🗄️  Database:")
    try:
        # Try to import and test
        from app import create_app, db
        from sqlalchemy import text
        
        app = create_app()
        with app.app_context():
            db.session.execute(text('SELECT 1'))
            print("   ✅ Database connection: Working")
            
            # Check if tables exist
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            if tables:
                print(f"   ✅ Tables found: {len(tables)}")
                for table in tables:
                    print(f"      - {table}")
            else:
                print("   ⚠️  No tables found - Database needs initialization")
                
    except Exception as e:
        print(f"   ❌ Database error: {str(e)}")
    print()

def check_config():
    print("⚙️  Configuration:")
    env_file = Path('.env')
    if env_file.exists():
        print("   ✅ .env file exists")
        
        # Read and check key settings
        with open('.env', 'r') as f:
            content = f.read()
            
        if 'USE_SQLITE=true' in content:
            print("   📊 Database: SQLite (Development)")
        elif 'DB_HOST' in content:
            print("   📊 Database: MySQL (Production)")
        else:
            print("   ⚠️  Database configuration unclear")
            
        if 'SECRET_KEY' in content:
            print("   🔐 Secret key: Configured")
        else:
            print("   ❌ Secret key: Missing!")
            
    else:
        print("   ❌ .env file missing!")
    print()

def test_import():
    print("🧪 Import Test:")
    try:
        from app import create_app
        app = create_app()
        print("   ✅ Application import: Success")
        print(f"   📊 Database URI: {app.config.get('SQLALCHEMY_DATABASE_URI', 'Not set')}")
    except Exception as e:
        print(f"   ❌ Import failed: {str(e)}")
    print()

def check_port():
    print("🌐 Network:")
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 5000))
        sock.close()
        
        if result == 0:
            print("   ✅ Port 5000: Available (or app is running)")
        else:
            print("   ✅ Port 5000: Free")
    except Exception as e:
        print(f"   ❌ Port check failed: {str(e)}")
    print()

def print_summary():
    print("="*70)
    print("📋 Quick Start Commands:")
    print("="*70)
    print("🚀 Start Application:")
    print("   Windows: start.bat")
    print("   Linux/Mac: ./start.sh")
    print("   Manual: python run.py")
    print()
    print("🌐 Access Application:")
    print("   URL: http://127.0.0.1:5000")
    print("   Username: admin")
    print("   Password: admin123")
    print()
    print("🔧 Troubleshooting:")
    print("   - Check .env file configuration")
    print("   - Ensure virtual environment is activated")
    print("   - Run: pip install -r requirements.txt")
    print("   - For MySQL: python setup_database.py")
    print("="*70)

def main():
    print_header()
    check_python()
    check_files()
    check_dependencies()
    check_config()
    test_import()
    check_database()
    check_port()
    print_summary()

if __name__ == '__main__':
    main()
