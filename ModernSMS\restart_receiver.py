#!/usr/bin/env python3
"""
Restart SMS receiver manually
"""
import os
import sys
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def restart_sms_receiver():
    """Restart SMS receiver with current gateway settings"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔄 RESTARTING SMS RECEIVER")
        print("="*60)
        
        # Get current receiver
        receiver = getattr(app, 'sms_receiver', None)
        
        if receiver:
            print("🛑 Stopping current SMS receiver...")
            try:
                receiver.stop_receiver()
                print("✅ Current receiver stopped")
            except Exception as e:
                print(f"⚠️  Error stopping receiver: {str(e)}")
            
            # Wait for cleanup
            time.sleep(3)
        else:
            print("ℹ️  No current receiver found")
        
        # Get primary gateway
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        if not gateway:
            print("❌ No active primary gateway found!")
            return False
        
        print(f"📡 Using Gateway: {gateway.name}")
        print(f"   IP: {gateway.ip_address}:{gateway.tg_sms_port}")
        print(f"   Username: {gateway.username}")
        
        # Start new receiver
        print("🚀 Starting new SMS receiver...")
        try:
            from app.sms.receiver import SMSReceiver
            new_receiver = SMSReceiver(app)
            new_receiver.start_receiver()
            app.sms_receiver = new_receiver
            
            print("✅ SMS receiver started successfully!")
            print(f"   Connected to: {gateway.ip_address}:{gateway.tg_sms_port}")
            
            # Wait a moment and check status
            time.sleep(2)
            
            print(f"\n📊 Receiver Status:")
            print(f"   Running: {new_receiver.running}")
            print(f"   Connected: {new_receiver.connected}")
            print(f"   Thread alive: {new_receiver.thread.is_alive() if new_receiver.thread else False}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to start SMS receiver: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_connection():
    """Test connection to gateway"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔍 TESTING GATEWAY CONNECTION:")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        if not gateway:
            print("❌ No gateway found!")
            return False
        
        import socket
        try:
            print(f"🌐 Testing network connection to {gateway.ip_address}:{gateway.tg_sms_port}...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((gateway.ip_address, gateway.tg_sms_port))
            sock.close()
            
            if result == 0:
                print("✅ Network connection successful")
                
                # Test TG SMS authentication
                print("🔐 Testing TG SMS authentication...")
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                sock.connect((gateway.ip_address, gateway.tg_sms_port))
                
                login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
                sock.send(login_cmd.encode())
                
                response = sock.recv(1024).decode()
                sock.close()
                
                if "Response: Success" in response:
                    print("✅ TG SMS authentication successful")
                    return True
                else:
                    print("❌ TG SMS authentication failed")
                    print(f"   Response: {response.strip()}")
                    return False
            else:
                print(f"❌ Network connection failed (error: {result})")
                return False
                
        except Exception as e:
            print(f"❌ Connection test failed: {str(e)}")
            return False

if __name__ == '__main__':
    print("🚀 SMS Receiver Restart Tool")
    
    # Test connection first
    if test_connection():
        print("\n" + "="*60)
        
        # Restart receiver
        success = restart_sms_receiver()
        
        if success:
            print(f"\n🎉 SMS receiver restart completed successfully!")
            print(f"📱 The receiver should now be listening for SMS messages")
            print(f"💡 Try sending an SMS to your gateway number to test")
        else:
            print(f"\n❌ SMS receiver restart failed!")
            print(f"🔧 Check the error messages above for troubleshooting")
    else:
        print(f"\n❌ Gateway connection test failed!")
        print(f"🔧 Fix gateway connection issues before restarting receiver")
    
    print("\n" + "="*60)
