# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

from cryptography.hazmat.primitives.asymmetric import dsa

class DSAPrivateKey: ...
class DSAPublicKey: ...
class DSAParameters: ...

def generate_parameters(key_size: int) -> dsa.DSAParameters: ...
def private_key_from_ptr(ptr: int) -> dsa.DSAPrivateKey: ...
def public_key_from_ptr(ptr: int) -> dsa.DSAPublicKey: ...
def from_private_numbers(
    numbers: dsa.DSAPrivateNumbers,
) -> dsa.DSAPrivateKey: ...
def from_public_numbers(numbers: dsa.DSAPublicNumbers) -> dsa.DSAPublicKey: ...
def from_parameter_numbers(
    numbers: dsa.DSAParameterNumbers,
) -> dsa.DSAParameters: ...
