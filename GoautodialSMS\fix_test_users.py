#!/usr/bin/env python3
"""
Fix test users - activate them and set proper passwords
"""

import sys
import os
sys.path.append('backend')

from app import app, db, User

def fix_test_users():
    with app.app_context():
        users_to_fix = ['test1', 'diet', 'test2']
        
        for username in users_to_fix:
            user = User.query.filter_by(username=username).first()
            if user:
                print(f"\n👤 Fixing user: {username}")
                print(f"   Current status: Active={user.is_active}")
                
                # Activate user
                user.is_active = True
                
                # Set password
                user.set_password('test123')
                
                # Ensure basic permissions
                user.can_send_sms = True
                user.can_view_inbox = True
                user.can_view_outbox = True
                
                print(f"   ✅ Activated user and set password to 'test123'")
                print(f"   ✅ Set basic permissions (send_sms, view_inbox, view_outbox)")
                print(f"   Assigned ports: {user.get_assigned_ports()}")
                print(f"   Permissions:")
                print(f"     can_send_sms: {user.can_send_sms}")
                print(f"     can_view_inbox: {user.can_view_inbox}")
                print(f"     can_view_outbox: {user.can_view_outbox}")
                print(f"     can_export: {user.can_export}")
                print(f"     can_manage_settings: {user.can_manage_settings}")
            else:
                print(f"❌ User {username} not found")
        
        # Commit all changes
        db.session.commit()
        print(f"\n✅ All user fixes committed to database!")

if __name__ == "__main__":
    fix_test_users()
