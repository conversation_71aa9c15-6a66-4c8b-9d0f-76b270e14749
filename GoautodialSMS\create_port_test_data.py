#!/usr/bin/env python3
"""
Create test SMS data with specific port assignments
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSMessage
import uuid
from datetime import datetime

def create_port_test_data():
    with app.app_context():
        print("📱 Creating Port-Specific Test Data")
        print("=" * 40)
        
        # Create test messages for different ports
        test_messages = [
            # Port 1 messages (for test1 user)
            {
                'message_id': f'TEST_PORT1_{uuid.uuid4()}',
                'direction': 'inbound',
                'phone_number': '+1111111111',
                'content': 'Test message for PORT 1 - test1 should see this',
                'status': 'received',
                'gsm_port': '1'
            },
            {
                'message_id': f'TEST_PORT1_OUT_{uuid.uuid4()}',
                'direction': 'outbound',
                'phone_number': '+1111111111',
                'content': 'Outbound test for PORT 1 - test1 should see this',
                'status': 'sent',
                'gsm_port': '1'
            },
            
            # Port 2 messages (for diet user)
            {
                'message_id': f'TEST_PORT2_{uuid.uuid4()}',
                'direction': 'inbound',
                'phone_number': '+2222222222',
                'content': 'Test message for PORT 2 - diet should see this',
                'status': 'received',
                'gsm_port': '2'
            },
            {
                'message_id': f'TEST_PORT2_OUT_{uuid.uuid4()}',
                'direction': 'outbound',
                'phone_number': '+2222222222',
                'content': 'Outbound test for PORT 2 - diet should see this',
                'status': 'sent',
                'gsm_port': '2'
            },
            
            # Port 3 messages (no user assigned)
            {
                'message_id': f'TEST_PORT3_{uuid.uuid4()}',
                'direction': 'inbound',
                'phone_number': '+3333333333',
                'content': 'Test message for PORT 3 - only admin should see this',
                'status': 'received',
                'gsm_port': '3'
            }
        ]
        
        # Add messages to database
        for msg_data in test_messages:
            message = SMSMessage(
                message_id=msg_data['message_id'],
                direction=msg_data['direction'],
                phone_number=msg_data['phone_number'],
                content=msg_data['content'],
                status=msg_data['status'],
                gsm_port=msg_data['gsm_port'],
                created_at=datetime.now()
            )
            
            db.session.add(message)
            print(f"✅ Created {msg_data['direction']} message for port {msg_data['gsm_port']}")
        
        db.session.commit()
        print(f"\n🎉 Successfully created {len(test_messages)} test messages!")
        
        # Verify the data
        print(f"\n📊 Port Distribution:")
        for port in ['1', '2', '3']:
            count = SMSMessage.query.filter_by(gsm_port=port).count()
            print(f"  Port {port}: {count} messages")

if __name__ == "__main__":
    create_port_test_data()
