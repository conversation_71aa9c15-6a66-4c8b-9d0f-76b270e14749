"""
Modern SMS Manager Application Factory
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>
from config import config

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()

def create_app(config_name='default'):
    """Application factory pattern"""
    app = Flask(__name__)
    
    # Load configuration
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    
    # Configure Flask-Login
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        from app.models import User
        return User.query.get(int(user_id))
    
    # Register blueprints
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.sms import bp as sms_bp
    app.register_blueprint(sms_bp, url_prefix='/sms')
    
    from app.gateways import bp as gateways_bp
    app.register_blueprint(gateways_bp, url_prefix='/gateways')
    
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    from app.users import bp as users_bp
    app.register_blueprint(users_bp, url_prefix='/users')
    
    # Create database tables
    with app.app_context():
        db.create_all()
        
        # Create default admin user if not exists
        from app.models import User, Role
        admin_role = Role.query.filter_by(name='admin').first()
        if not admin_role:
            admin_role = Role(name='admin', description='Administrator')
            db.session.add(admin_role)
        
        user_role = Role.query.filter_by(name='user').first()
        if not user_role:
            user_role = Role(name='user', description='Regular User')
            db.session.add(user_role)
        
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role=admin_role
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
        
        db.session.commit()

    # Start SMS receiver for incoming messages
    def start_sms_receiver():
        from app.sms.receiver import SMSReceiver
        try:
            # Create receiver with app context
            receiver = SMSReceiver(app)
            receiver.start_receiver()
            app.logger.info("✅ SMS receiver started successfully")

            # Store receiver in app for later access
            app.sms_receiver = receiver
        except Exception as e:
            app.logger.error(f"❌ Failed to start SMS receiver: {str(e)}")

    # Start receiver after app is fully initialized
    import threading
    threading.Timer(2.0, start_sms_receiver).start()

    return app
