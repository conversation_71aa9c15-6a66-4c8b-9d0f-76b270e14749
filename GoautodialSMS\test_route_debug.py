#!/usr/bin/env python3
"""
Test Route Debug - Check if routes are working and logging
"""

import requests
import time

def test_route_debug():
    """Test routes and check for debug logs"""
    print("🔍 Testing Route Debug")
    print("=" * 25)
    
    try:
        # Wait for Flask app
        time.sleep(2)
        
        # Create session and login
        session = requests.Session()
        
        # Login as admin
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data, allow_redirects=True)
        
        if login_response.status_code == 200:
            print("✅ Successfully logged in")
            
            # Test a simple route first
            print("\n🏠 Testing Index Route:")
            index_response = session.get('http://127.0.0.1:5000/')
            print(f"   Status: {index_response.status_code}")
            
            # Test GSM Gateway route with detailed logging
            print("\n🔧 Testing GSM Gateway Route:")
            gsm_response = session.get('http://127.0.0.1:5000/gsm_gateways')
            print(f"   Status: {gsm_response.status_code}")
            
            if gsm_response.status_code == 200:
                # Check response headers
                print(f"   Content-Type: {gsm_response.headers.get('Content-Type', 'Unknown')}")
                print(f"   Content-Length: {len(gsm_response.text)}")
                
                # Look for specific patterns
                content = gsm_response.text
                
                # Check if it's the actual template or cached content
                if "gsm_gateways.html" in content or "GSM Gateway Management" in content:
                    print("✅ Correct template loaded")
                else:
                    print("❌ Wrong template or cached content")
                
                # Check for template variables
                if "{{ gateway" in content:
                    print("❌ Template variables not rendered (Jinja2 issue)")
                else:
                    print("✅ Template variables rendered")
                
                # Check for our debug patterns
                if "Ports (8):" in content:
                    print("✅ Shows 8 ports")
                elif "Ports (4):" in content:
                    print("❌ Shows 4 ports (cached)")
                else:
                    print("❓ No port count found")
                
                # Show a snippet of the content
                if len(content) > 1000:
                    snippet = content[500:1000]
                    print(f"   Content snippet: {snippet[:200]}...")
                
            else:
                print(f"❌ GSM Gateway route failed: {gsm_response.status_code}")
            
            # Test User Creation route
            print("\n👤 Testing User Creation Route:")
            user_response = session.get('http://127.0.0.1:5000/users/create')
            print(f"   Status: {user_response.status_code}")
            
            if user_response.status_code == 200:
                content = user_response.text
                
                if "create_user.html" in content or "Create User" in content:
                    print("✅ Correct template loaded")
                else:
                    print("❌ Wrong template")
                
                if "No GSM gateway configured yet" in content:
                    print("❌ Shows 'No GSM gateway configured' (cached)")
                elif "Main Gateway" in content:
                    print("✅ Shows Main Gateway")
                else:
                    print("❓ No gateway info found")
            
            return True
            
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_route_debug()
