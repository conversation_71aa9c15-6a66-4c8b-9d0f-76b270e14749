#!/usr/bin/env python3
"""
Test SMS API after enabling in TG gateway
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def test_api_enabled():
    """Test if API is now enabled and working"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🧪 TESTING SMS API AFTER ENABLING")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        print(f"📡 Testing: {gateway.name} ({gateway.ip_address}:{gateway.tg_sms_port})")
        print(f"🔐 Credentials: {gateway.username}/{gateway.password}")
        
        try:
            # Connect to TG SMS server
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Read initial response
            initial = sock.recv(1024).decode()
            print(f"📥 Initial: {initial.strip()}")
            
            # Send login
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            # Read login response
            login_resp = sock.recv(1024).decode()
            print(f"📥 Login: {login_resp.strip()}")
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed!")
                sock.close()
                return False
            
            print("✅ Login successful!")
            
            # Test event subscription (this should now work)
            print(f"\n📡 Testing event subscription...")
            event_cmd = "Action: Events\r\nEventMask: all\r\n\r\n"
            sock.send(event_cmd.encode())
            
            event_resp = sock.recv(1024).decode()
            print(f"📥 Event response: {event_resp.strip()}")
            
            if "Events: On" in event_resp:
                print("🎉 SUCCESS! SMS EVENTS ARE NOW ENABLED!")
                
                # Test SMS command (check GSM spans)
                print(f"\n📱 Testing SMS commands...")
                gsm_cmd = "Action: smscommand\r\ncommand: gsm show spans\r\n\r\n"
                sock.send(gsm_cmd.encode())
                
                gsm_resp = sock.recv(2048).decode()
                print(f"📥 GSM Spans: {gsm_resp.strip()}")
                
                # Listen for SMS events
                print(f"\n👂 Listening for SMS events for 15 seconds...")
                print("📱 Send an SMS to your gateway number NOW!")
                
                sock.settimeout(1)
                start_time = time.time()
                event_count = 0
                
                while time.time() - start_time < 15:
                    try:
                        data = sock.recv(4096).decode()
                        if data and "Event:" in data:
                            event_count += 1
                            print(f"\n📨 SMS EVENT {event_count} RECEIVED!")
                            print(f"   {data}")
                            
                            if "ReceivedSMS" in data:
                                print("🎉 SMS RECEIVING IS WORKING!")
                                
                    except socket.timeout:
                        print(".", end="", flush=True)
                        continue
                    except Exception as e:
                        print(f"\n❌ Error reading events: {str(e)}")
                        break
                
                print(f"\n📊 Events received during test: {event_count}")
                sock.close()
                return True
                
            elif "Permission denied" in event_resp:
                print("❌ Still getting permission denied")
                print("💡 Check if you added the correct IP address in API settings")
                sock.close()
                return False
            else:
                print("⚠️  Unexpected response to event subscription")
                sock.close()
                return False
            
        except Exception as e:
            print(f"❌ Error testing API: {str(e)}")
            return False

def restart_sms_receiver():
    """Restart SMS receiver with API now enabled"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔄 RESTARTING SMS RECEIVER")
        print("="*60)
        
        try:
            from app.sms.receiver import SMSReceiver
            
            # Stop existing receiver
            receiver = getattr(app, 'sms_receiver', None)
            if receiver:
                print("🛑 Stopping existing receiver...")
                receiver.stop_receiver()
                time.sleep(2)
            
            # Start new receiver
            print("🚀 Starting new SMS receiver...")
            new_receiver = SMSReceiver(app)
            new_receiver.start_receiver()
            app.sms_receiver = new_receiver
            
            # Wait and check status
            time.sleep(3)
            
            print(f"📊 SMS Receiver Status:")
            print(f"   Running: {'✅' if new_receiver.running else '❌'}")
            print(f"   Connected: {'✅' if new_receiver.connected else '❌'}")
            print(f"   Thread alive: {'✅' if (new_receiver.thread and new_receiver.thread.is_alive()) else '❌'}")
            
            if new_receiver.running and new_receiver.connected:
                print(f"🎉 SMS receiver successfully restarted!")
                print(f"📱 Ready to receive SMS messages!")
                return True
            else:
                print(f"❌ SMS receiver failed to start properly")
                return False
                
        except Exception as e:
            print(f"❌ Error restarting SMS receiver: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    print("🚀 SMS API Test After Enabling")
    print("🎯 Verifying that API is now working")
    
    # Test if API is working
    api_working = test_api_enabled()
    
    if api_working:
        print(f"\n✅ API is working! Restarting SMS receiver...")
        
        # Restart SMS receiver
        receiver_working = restart_sms_receiver()
        
        if receiver_working:
            print(f"\n🎉 SUCCESS! SMS receiving is now fully operational!")
            print(f"📱 Try sending an SMS to test receiving")
            print(f"🌐 Check your inbox in the web interface")
        else:
            print(f"\n⚠️  API works but receiver has issues")
            print(f"💡 Try restarting from the dashboard")
    else:
        print(f"\n❌ API still not working properly")
        print(f"💡 Double-check TG SMS API settings:")
        print(f"   1. API is enabled (checkbox ticked)")
        print(f"   2. Correct IP addresses are allowed")
        print(f"   3. Settings are saved and applied")
    
    print("\n" + "="*60)
