#!/usr/bin/env python3
"""
Check received SMS messages
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import SMSMessage
from datetime import datetime, timedelta

def check_received_messages():
    """Check all received SMS messages"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("📥 RECEIVED SMS MESSAGES")
        print("="*60)
        
        # Get all received messages
        all_received = SMSMessage.query.filter_by(direction='inbound').order_by(SMSMessage.created_at.desc()).all()
        
        print(f"📊 Total received messages: {len(all_received)}")
        
        if len(all_received) == 0:
            print("❌ No received messages found in database")
            return
        
        print(f"\n📱 Recent received messages:")
        print("-" * 60)
        
        for i, msg in enumerate(all_received[:15], 1):  # Show last 15 messages
            print(f"{i:2d}. From: {msg.phone_number}")
            print(f"    Time: {msg.created_at}")
            print(f"    Port: {msg.port.port_number if msg.port else 'Unknown'}")
            print(f"    Message: {msg.content}")
            print(f"    Status: {msg.status}")
            print("-" * 60)
        
        # Check very recent messages (last 30 minutes)
        thirty_minutes_ago = datetime.now() - timedelta(minutes=30)
        very_recent = SMSMessage.query.filter(
            SMSMessage.direction == 'inbound',
            SMSMessage.created_at >= thirty_minutes_ago
        ).order_by(SMSMessage.created_at.desc()).all()
        
        print(f"\n🕐 Messages in last 30 minutes: {len(very_recent)}")
        
        if very_recent:
            print("🎉 RECENT ACTIVITY DETECTED!")
            for msg in very_recent:
                print(f"   📞 {msg.phone_number} at {msg.created_at}")
                print(f"   💬 {msg.content}")
        else:
            print("⚠️  No messages received in the last 30 minutes")
        
        # Check last hour
        one_hour_ago = datetime.now() - timedelta(hours=1)
        last_hour = SMSMessage.query.filter(
            SMSMessage.direction == 'inbound',
            SMSMessage.created_at >= one_hour_ago
        ).count()
        
        print(f"\n📈 Messages in last hour: {last_hour}")
        
        # Show the very latest message
        if all_received:
            latest = all_received[0]
            print(f"\n🔥 LATEST MESSAGE:")
            print(f"   From: {latest.phone_number}")
            print(f"   Time: {latest.created_at}")
            print(f"   Content: {latest.content}")
            print(f"   Port: {latest.port.port_number if latest.port else 'Unknown'}")
            
            # Calculate time since last message
            time_diff = datetime.now() - latest.created_at
            minutes_ago = int(time_diff.total_seconds() / 60)
            
            if minutes_ago < 1:
                print(f"   ⏰ Received: Just now!")
            elif minutes_ago < 60:
                print(f"   ⏰ Received: {minutes_ago} minutes ago")
            else:
                hours_ago = int(minutes_ago / 60)
                print(f"   ⏰ Received: {hours_ago} hours ago")

if __name__ == '__main__':
    check_received_messages()
    
    print("\n" + "="*60)
    print("💡 If you see recent messages above, SMS receiving IS working!")
    print("💡 If not, try sending an SMS to your gateway number")
    print("="*60)
