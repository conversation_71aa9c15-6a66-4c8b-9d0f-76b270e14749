#!/usr/bin/env python3
"""
Debug user permissions in detail
"""

import sys
import os
sys.path.append('backend')

from app import app, db, User

def debug_user_permissions():
    with app.app_context():
        print("🔍 Debugging User Permissions")
        print("=" * 50)
        
        # Get all users
        users = User.query.all()
        
        for user in users:
            print(f"\n👤 User: {user.username}")
            print(f"   ID: {user.id}")
            print(f"   Email: {user.email}")
            print(f"   Role: {user.role}")
            print(f"   Active: {user.is_active}")
            print(f"   Assigned Ports: {user.get_assigned_ports()}")
            
            print(f"   Direct Permission Attributes:")
            print(f"     can_send_sms: {user.can_send_sms}")
            print(f"     can_view_inbox: {user.can_view_inbox}")
            print(f"     can_view_outbox: {user.can_view_outbox}")
            print(f"     can_export: {user.can_export}")
            print(f"     can_manage_users: {user.can_manage_users}")
            print(f"     can_view_reports: {user.can_view_reports}")
            print(f"     can_manage_settings: {user.can_manage_settings}")
            print(f"     can_manage_ports: {user.can_manage_ports}")
            
            print(f"   Permission Method Tests:")
            permissions_to_test = [
                'can_send_sms',
                'can_view_inbox', 
                'can_view_outbox',
                'can_export',
                'can_manage_users',
                'can_view_reports',
                'can_manage_settings',
                'can_manage_ports',
                'send_sms',  # Test without 'can_' prefix
                'view_inbox',
                'view_outbox',
                'export',
                'manage_users',
                'view_reports',
                'manage_settings',
                'manage_ports'
            ]
            
            for perm in permissions_to_test:
                result = user.has_permission(perm)
                print(f"     has_permission('{perm}'): {result}")
            
            print(f"   Port Access Tests:")
            for port in ['1', '2', '3']:
                can_use = user.can_use_port(port)
                print(f"     can_use_port('{port}'): {can_use}")

if __name__ == "__main__":
    debug_user_permissions()
