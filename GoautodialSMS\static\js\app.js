/**
 * SMS Management System - Main JavaScript File
 */

// Global variables
let autoRefreshInterval;
let isPageVisible = true;

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize the application
function initializeApp() {
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize auto-refresh
    initializeAutoRefresh();
    
    // Initialize page visibility detection
    initializeVisibilityDetection();
    
    // Initialize form validations
    initializeFormValidations();
    
    // Initialize character counters
    initializeCharacterCounters();
    
    // Initialize phone number formatting
    initializePhoneFormatting();
    
    // Initialize table interactions
    initializeTableInteractions();
    
    console.log('SMS Management System initialized');
}

// Initialize Bootstrap tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Initialize auto-refresh functionality
function initializeAutoRefresh() {
    // Auto-refresh every 30 seconds if page is visible
    autoRefreshInterval = setInterval(function() {
        if (isPageVisible && shouldAutoRefresh()) {
            refreshPage();
        }
    }, 30000);
}

// Initialize page visibility detection
function initializeVisibilityDetection() {
    document.addEventListener('visibilitychange', function() {
        isPageVisible = !document.hidden;
    });
}

// Check if page should auto-refresh
function shouldAutoRefresh() {
    // Don't refresh if user is typing in a form
    const activeElement = document.activeElement;
    if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
        return false;
    }
    
    // Don't refresh if modal is open
    const openModals = document.querySelectorAll('.modal.show');
    if (openModals.length > 0) {
        return false;
    }
    
    return true;
}

// Refresh the current page
function refreshPage() {
    // Add loading indicator
    showLoadingIndicator();
    
    // Reload page
    setTimeout(function() {
        location.reload();
    }, 500);
}

// Show loading indicator
function showLoadingIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'loadingIndicator';
    indicator.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
    indicator.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    indicator.style.zIndex = '9999';
    indicator.innerHTML = `
        <div class="bg-white p-4 rounded shadow">
            <div class="d-flex align-items-center">
                <div class="spinner-border text-primary me-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span>Refreshing...</span>
            </div>
        </div>
    `;
    
    document.body.appendChild(indicator);
}

// Initialize form validations
function initializeFormValidations() {
    const forms = document.querySelectorAll('form[data-validate="true"]');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!validateForm(form)) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
}

// Validate form
function validateForm(form) {
    let isValid = true;
    
    // Check required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            isValid = false;
            showFieldError(field, 'This field is required');
        } else {
            clearFieldError(field);
        }
    });
    
    // Validate phone numbers
    const phoneFields = form.querySelectorAll('input[type="tel"]');
    phoneFields.forEach(function(field) {
        if (field.value && !isValidPhoneNumber(field.value)) {
            isValid = false;
            showFieldError(field, 'Please enter a valid phone number');
        }
    });
    
    // Validate message length
    const messageFields = form.querySelectorAll('textarea[name="message"]');
    messageFields.forEach(function(field) {
        if (field.value.length > 1024) {
            isValid = false;
            showFieldError(field, 'Message is too long (maximum 1024 characters)');
        }
    });
    
    return isValid;
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    let feedback = field.parentNode.querySelector('.invalid-feedback');
    if (!feedback) {
        feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        field.parentNode.appendChild(feedback);
    }
    
    feedback.textContent = message;
}

// Clear field error
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.remove();
    }
}

// Initialize character counters
function initializeCharacterCounters() {
    const textareas = document.querySelectorAll('textarea[data-char-counter="true"]');
    
    textareas.forEach(function(textarea) {
        const counter = document.querySelector(`[data-counter-for="${textarea.id}"]`);
        if (counter) {
            updateCharacterCounter(textarea, counter);
            
            textarea.addEventListener('input', function() {
                updateCharacterCounter(textarea, counter);
            });
        }
    });
}

// Update character counter
function updateCharacterCounter(textarea, counter) {
    const length = textarea.value.length;
    const maxLength = textarea.getAttribute('maxlength') || 1024;
    const smsCount = calculateSMSCount(textarea.value);
    
    counter.textContent = `${length}/${maxLength} characters (${smsCount} SMS)`;
    
    // Update counter color
    if (length > maxLength * 0.9) {
        counter.className = 'char-counter danger';
    } else if (length > maxLength * 0.7) {
        counter.className = 'char-counter warning';
    } else {
        counter.className = 'char-counter';
    }
}

// Calculate SMS count
function calculateSMSCount(message) {
    const length = message.length;
    if (length === 0) return 0;
    if (length <= 160) return 1;
    return Math.ceil(length / 153);
}

// Initialize phone number formatting
function initializePhoneFormatting() {
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    
    phoneInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            formatPhoneNumber(input);
        });
    });
}

// Format phone number
function formatPhoneNumber(input) {
    let value = input.value.replace(/[^\d+]/g, '');
    
    // Add + if not present and number starts with country code
    if (value && !value.startsWith('+') && value.length > 7) {
        value = '+' + value;
    }
    
    input.value = value;
}

// Validate phone number
function isValidPhoneNumber(phoneNumber) {
    const cleaned = phoneNumber.replace(/[^\d+]/g, '');
    return /^\+?[1-9]\d{7,14}$/.test(cleaned);
}

// Initialize table interactions
function initializeTableInteractions() {
    // Initialize row selection
    initializeRowSelection();
    
    // Initialize sortable headers
    initializeSortableHeaders();
}

// Initialize row selection
function initializeRowSelection() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const rowCheckboxes = document.querySelectorAll('.message-checkbox');
    
    if (selectAllCheckbox && rowCheckboxes.length > 0) {
        selectAllCheckbox.addEventListener('change', function() {
            rowCheckboxes.forEach(function(checkbox) {
                checkbox.checked = selectAllCheckbox.checked;
            });
            updateBulkActions();
        });
        
        rowCheckboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                updateSelectAllState();
                updateBulkActions();
            });
        });
    }
}

// Update select all checkbox state
function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const rowCheckboxes = document.querySelectorAll('.message-checkbox');
    
    if (selectAllCheckbox && rowCheckboxes.length > 0) {
        const checkedCount = document.querySelectorAll('.message-checkbox:checked').length;
        
        if (checkedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCount === rowCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }
}

// Update bulk actions visibility
function updateBulkActions() {
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
    
    if (bulkActions && selectedCount) {
        if (checkedBoxes.length > 0) {
            selectedCount.textContent = checkedBoxes.length;
            bulkActions.style.display = 'block';
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

// Initialize sortable headers
function initializeSortableHeaders() {
    const sortableHeaders = document.querySelectorAll('th[data-sortable="true"]');
    
    sortableHeaders.forEach(function(header) {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            sortTable(header);
        });
    });
}

// Sort table
function sortTable(header) {
    // This would implement table sorting functionality
    console.log('Sorting by:', header.textContent);
}

// Utility functions
const Utils = {
    // Format date
    formatDate: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    },
    
    // Format relative time
    formatRelativeTime: function(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} day${days > 1 ? 's' : ''} ago`;
        }
    },
    
    // Truncate text
    truncateText: function(text, maxLength) {
        if (text.length <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + '...';
    },
    
    // Show notification
    showNotification: function(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.top = '20px';
        alertDiv.style.right = '20px';
        alertDiv.style.zIndex = '9999';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(function() {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
};

// Export utilities to global scope
window.Utils = Utils;

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
