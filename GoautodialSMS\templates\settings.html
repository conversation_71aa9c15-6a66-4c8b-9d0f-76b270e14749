{% extends "base.html" %}

{% block title %}Settings - SMS Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-gear"></i> System Settings
            </h1>
            <div class="btn-group">
                <button onclick="location.reload()" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
                <form action="{{ url_for('test_connection') }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-outline-success">
                        <i class="bi bi-wifi"></i> Test Connection
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i> System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-{{ 'success' if settings.receiver_status else 'danger' }} text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-{{ 'wifi' if settings.receiver_status else 'wifi-off' }} fs-1"></i>
                                <h6 class="mt-2">SMS Receiver</h6>
                                <small>{{ 'Connected' if settings.receiver_status else 'Disconnected' }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-people fs-1"></i>
                                <h6 class="mt-2">Total Users</h6>
                                <small>{{ settings.total_users or 0 }} users</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-chat-dots fs-1"></i>
                                <h6 class="mt-2">Total Messages</h6>
                                <small>{{ settings.total_messages or 0 }} messages</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <i class="bi bi-hdd-network fs-1"></i>
                                <h6 class="mt-2">SMS Ports</h6>
                                <small>{{ settings.total_ports or 0 }} ports</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mb-4">
        <!-- GSM Gateway Settings -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-router"></i> GSM Gateway Configuration
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('update_settings') }}" method="POST" id="gatewayForm">
                    <div class="mb-3">
                        <label for="ip_address" class="form-label">Gateway IP Address *</label>
                        <input type="text" class="form-control" id="ip_address" name="ip_address"
                               value="{{ settings.sms_api_ip or '*************' }}"
                               placeholder="*************" required>
                        <div class="form-text">IP address of your GSM gateway device</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="api_port" class="form-label">API Port</label>
                                <input type="number" class="form-control" id="api_port" name="api_port"
                                       value="80" min="1" max="65535">
                                <div class="form-text">HTTP API port (default: 80)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tg_sms_port" class="form-label">TG SMS Port</label>
                                <input type="number" class="form-control" id="tg_sms_port" name="tg_sms_port"
                                       value="{{ settings.tg_sms_port or '5038' }}" min="1" max="65535">
                                <div class="form-text">TG SMS port (default: 5038)</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">Username *</label>
                        <input type="text" class="form-control" id="username" name="username"
                               value="{{ settings.sms_api_account or 'apiuser' }}"
                               placeholder="apiuser" required>
                        <div class="form-text">Gateway API username</div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password *</label>
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="Enter new password" required>
                        <div class="form-text">Gateway API password (required for security)</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Gateway Configuration
                        </button>
                    </div>

                    <div class="alert alert-warning mt-3">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Important:</strong> After updating the configuration, restart the system to apply changes.
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <!-- Port Status Overview -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-hdd-network"></i> GSM Port Status
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Hardware Status:</strong> Your GSM gateway has 8 ports. Only ports with active SIM cards will show as "Active".
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-hdd-network fs-1"></i>
                                <h6 class="mt-2">Total Ports</h6>
                                <small>8 ports available</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="bi bi-sim fs-1"></i>
                                <h6 class="mt-2">Active SIMs</h6>
                                <small>1 SIM detected</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    <a href="{{ url_for('gsm_gateways') }}" class="btn btn-outline-primary">
                        <i class="bi bi-eye"></i> View Port Details
                    </a>
                    <a href="{{ url_for('ports') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-gear"></i> Manage Port Assignments
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Port Management -->
{% if current_user.has_permission('can_manage_ports') %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-hdd-network"></i> SMS Port Management
                </h5>
                <a href="{{ url_for('ports') }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-gear"></i> Manage Ports
                </a>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>SMS Ports:</strong> Configure and manage SMS gateway ports for sending and receiving messages.
                    Each port can be assigned to specific users and has its own rate limits.
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-primary">{{ settings.total_ports or 0 }}</h4>
                            <small class="text-muted">Total Ports</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-success">Active</h4>
                            <small class="text-muted">Port Status</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-info">1/10s</h4>
                            <small class="text-muted">Rate Limit</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Connection Test -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-wifi"></i> System Diagnostics
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <form action="{{ url_for('test_connection') }}" method="POST">
                            <button type="submit" class="btn btn-outline-success w-100">
                                <i class="bi bi-wifi"></i> Test SMS Connection
                            </button>
                        </form>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('debug') }}" class="btn btn-outline-info w-100">
                            <i class="bi bi-bug"></i> Debug Console
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('debug_database_check') }}" class="btn btn-outline-warning w-100" target="_blank">
                            <i class="bi bi-database"></i> Database Check
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Documentation -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-book"></i> API Documentation
                </h5>
            </div>
            <div class="card-body">
                <h6>Outgoing SMS API Format:</h6>
                <div class="bg-light p-3 rounded mb-3">
                    <code>
                        http://[IP]/cgi/WebCGI?1500101=account=[account]&password=[password]&port=[port]&destination=[phone]&content=[message]
                    </code>
                </div>

                <h6>TG SMS Server:</h6>
                <ul class="mb-3">
                    <li><strong>Protocol:</strong> TCP connection</li>
                    <li><strong>Default Port:</strong> 5038</li>
                    <li><strong>Authentication:</strong> Username/password based</li>
                    <li><strong>Rate Limit:</strong> 1 SMS per 10 seconds per port</li>
                </ul>

                <h6>Environment Variables:</h6>
                <div class="bg-light p-3 rounded">
                    <pre><code># SMS API Configuration
SMS_API_IP=*************
SMS_API_ACCOUNT=apiuser
SMS_API_PASSWORD=apipass
SMS_API_PORT=1

# TG SMS Server Configuration
TG_SMS_IP=*************
TG_SMS_PORT=5038
TG_SMS_USERNAME=apiuser
TG_SMS_PASSWORD=apipass</code></pre>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form submission handlers
document.getElementById('smsApiForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = {
        sms_api_ip: document.getElementById('sms_api_ip').value,
        sms_api_account: document.getElementById('sms_api_account').value,
        sms_api_password: document.getElementById('sms_api_password').value,
        sms_api_port: document.getElementById('sms_api_port').value
    };

    // In a real implementation, this would send data to the backend
    console.log('SMS API Settings:', formData);

    // Show success message
    Utils.showNotification('SMS API settings saved successfully!', 'success');
});

document.getElementById('tgSmsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = {
        tg_sms_ip: document.getElementById('tg_sms_ip').value,
        tg_sms_port: document.getElementById('tg_sms_port').value,
        tg_sms_username: document.getElementById('tg_sms_username').value,
        tg_sms_password: document.getElementById('tg_sms_password').value
    };

    // In a real implementation, this would send data to the backend
    console.log('TG SMS Settings:', formData);

    // Show success message
    Utils.showNotification('TG SMS settings saved successfully!', 'success');
});

// Connection test functions
function testSmsApi() {
    const resultsDiv = document.getElementById('connectionResults');
    resultsDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-hourglass-split"></i> Testing SMS API connection...
        </div>
    `;

    // Simulate connection test
    setTimeout(function() {
        const success = Math.random() > 0.3; // 70% success rate for demo

        if (success) {
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> SMS API connection successful!
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle"></i> SMS API connection failed. Please check your settings.
                </div>
            `;
        }
    }, 2000);
}

function testTgConnection() {
    const resultsDiv = document.getElementById('connectionResults');
    resultsDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-hourglass-split"></i> Testing TG SMS server connection...
        </div>
    `;

    // Simulate connection test
    setTimeout(function() {
        const success = Math.random() > 0.3; // 70% success rate for demo

        if (success) {
            resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> TG SMS server connection successful!
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle"></i> TG SMS server connection failed. Please check your settings.
                </div>
            `;
        }
    }, 2000);
}
</script>
{% endblock %}
