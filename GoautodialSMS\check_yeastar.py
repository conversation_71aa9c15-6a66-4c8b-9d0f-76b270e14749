#!/usr/bin/env python3
"""
Yeastar Configuration Checker
Helps identify what needs to be configured on the Yeastar system
"""

import socket
import requests
import subprocess
import sys

YEASTAR_IP = '*************'

def check_yeastar_web_interface():
    """Check if Yeastar web interface is accessible"""
    print("🌐 Checking Yeastar web interface...")
    
    try:
        # Try HTTP first
        response = requests.get(f'http://{YEASTAR_IP}', timeout=10)
        print(f"✅ HTTP web interface accessible (Status: {response.status_code})")
        return True
    except requests.exceptions.ConnectTimeout:
        print("❌ HTTP web interface - Connection timeout")
    except requests.exceptions.ConnectionError:
        print("❌ HTTP web interface - Connection refused")
    except Exception as e:
        print(f"❌ HTTP web interface - Error: {e}")
    
    try:
        # Try HTTPS
        response = requests.get(f'https://{YEASTAR_IP}', timeout=10, verify=False)
        print(f"✅ HTTPS web interface accessible (Status: {response.status_code})")
        return True
    except:
        print("❌ HTTPS web interface - Not accessible")
    
    return False

def check_tg_sms_port():
    """Check TG SMS port specifically"""
    print("📡 Checking TG SMS port 5038...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((YEASTAR_IP, 5038))
        sock.close()
        
        if result == 0:
            print("✅ Port 5038 is OPEN - TG SMS service is running")
            return True
        else:
            print("❌ Port 5038 is CLOSED - TG SMS service is NOT running")
            return False
    except Exception as e:
        print(f"❌ Error checking port 5038: {e}")
        return False

def check_other_common_ports():
    """Check other common Yeastar ports"""
    print("🔍 Checking other Yeastar ports...")
    
    common_ports = {
        80: "HTTP Web Interface",
        443: "HTTPS Web Interface", 
        5060: "SIP",
        5038: "TG SMS (AMI)",
        8088: "WebRTC",
        8089: "WebRTC TLS"
    }
    
    for port, description in common_ports.items():
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((YEASTAR_IP, port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port} ({description}) - OPEN")
            else:
                print(f"❌ Port {port} ({description}) - CLOSED")
        except:
            print(f"❌ Port {port} ({description}) - ERROR")

def test_sms_api_endpoint():
    """Test SMS API endpoint"""
    print("📱 Testing SMS API endpoint...")
    
    # Test with a simple request (will likely fail auth but should connect)
    test_url = f"http://{YEASTAR_IP}/cgi/WebCGI"
    
    try:
        response = requests.get(test_url, timeout=10)
        print(f"✅ SMS API endpoint accessible (Status: {response.status_code})")
        print(f"Response: {response.text[:100]}...")
        return True
    except requests.exceptions.ConnectTimeout:
        print("❌ SMS API endpoint - Connection timeout")
    except requests.exceptions.ConnectionError:
        print("❌ SMS API endpoint - Connection refused")
    except Exception as e:
        print(f"❌ SMS API endpoint - Error: {e}")
    
    return False

def ping_yeastar():
    """Test basic connectivity with ping"""
    print(f"🏓 Pinging {YEASTAR_IP}...")
    
    try:
        if sys.platform.startswith('win'):
            result = subprocess.run(['ping', '-n', '4', YEASTAR_IP], 
                                  capture_output=True, text=True, timeout=15)
        else:
            result = subprocess.run(['ping', '-c', '4', YEASTAR_IP], 
                                  capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print(f"✅ Ping successful - {YEASTAR_IP} is reachable")
            # Extract timing info
            lines = result.stdout.split('\n')
            for line in lines:
                if 'time=' in line or 'Average' in line:
                    print(f"   {line.strip()}")
            return True
        else:
            print(f"❌ Ping failed - {YEASTAR_IP} is not reachable")
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ Ping timeout - {YEASTAR_IP} is not responding")
        return False
    except Exception as e:
        print(f"❌ Ping error: {e}")
        return False

def generate_yeastar_config_guide():
    """Generate configuration guide for Yeastar"""
    print("\n" + "="*60)
    print(" YEASTAR CONFIGURATION GUIDE")
    print("="*60)
    
    print("\n📋 To enable SMS receiving, configure these on your Yeastar:")
    
    print("\n1. Enable TG SMS Service:")
    print("   • Log into Yeastar web interface")
    print("   • Go to: Settings → PBX → General → API")
    print("   • Enable 'TG SMS API'")
    print("   • Set port to 5038")
    print("   • Apply changes and restart if needed")
    
    print("\n2. Create API User:")
    print("   • Go to: Settings → PBX → General → API")
    print("   • Add new API user:")
    print("     - Username: apiuser")
    print("     - Password: apipass")
    print("     - Permissions: SMS Read/Write")
    
    print("\n3. Configure SMS Events:")
    print("   • Ensure SMS events are enabled")
    print("   • Events should be sent to connected clients")
    print("   • Format should include '--END SMS EVENT--' terminator")
    
    print("\n4. GSM Module Setup:")
    print("   • Go to: Settings → Trunks → GSM/3G/4G")
    print("   • Ensure GSM module is detected")
    print("   • Insert active SIM card")
    print("   • Configure SMS center number")
    print("   • Test SMS sending/receiving manually")
    
    print("\n5. Test Configuration:")
    print("   • Send SMS to GSM number manually")
    print("   • Check if SMS appears in Yeastar interface")
    print("   • Verify TG SMS service is running")
    print("   • Test API connection with telnet")

def main():
    """Main checker function"""
    print("Yeastar Configuration Checker")
    print("=" * 40)
    print(f"Target Yeastar: {YEASTAR_IP}")
    print()
    
    # Test basic connectivity
    ping_success = ping_yeastar()
    print()
    
    if not ping_success:
        print("❌ CRITICAL: Cannot reach Yeastar system!")
        print("   Check IP address, network connection, and power")
        return
    
    # Test web interface
    web_success = check_yeastar_web_interface()
    print()
    
    # Test TG SMS port (most important for receiving)
    tg_sms_success = check_tg_sms_port()
    print()
    
    # Test SMS API (for sending)
    api_success = test_sms_api_endpoint()
    print()
    
    # Check other ports
    check_other_common_ports()
    
    # Generate summary
    print("\n" + "="*60)
    print(" SUMMARY")
    print("="*60)
    
    if ping_success:
        print("✅ Basic connectivity: WORKING")
    else:
        print("❌ Basic connectivity: FAILED")
    
    if web_success:
        print("✅ Web interface: ACCESSIBLE")
    else:
        print("❌ Web interface: NOT ACCESSIBLE")
    
    if tg_sms_success:
        print("✅ TG SMS service: RUNNING")
        print("   SMS receiving should work")
    else:
        print("❌ TG SMS service: NOT RUNNING")
        print("   This is why SMS receiving is not working!")
    
    if api_success:
        print("✅ SMS API: ACCESSIBLE")
    else:
        print("❌ SMS API: NOT ACCESSIBLE")
    
    # Recommendations
    print("\n📋 RECOMMENDATIONS:")
    
    if not tg_sms_success:
        print("🔧 CRITICAL: Enable TG SMS service on Yeastar")
        print("   This is the main reason SMS receiving is not working")
    
    if not web_success:
        print("🔧 Check Yeastar web interface accessibility")
    
    if not api_success:
        print("🔧 Check SMS API configuration")
    
    # Show configuration guide
    generate_yeastar_config_guide()

if __name__ == "__main__":
    main()
