{% extends "base.html" %}

{% block title %}Conversation with {{ phone_number }} - SMS Manager{% endblock %}

{% block extra_css %}
<style>
.chat-container {
    height: 70vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.chat-header {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    color: white;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: rgba(255,255,255,0.05);
    backdrop-filter: blur(5px);
}

.message-group {
    margin-bottom: 20px;
}

.message-bubble {
    max-width: 70%;
    margin-bottom: 8px;
    animation: messageSlide 0.3s ease-out;
}

.message-bubble.sent {
    margin-left: auto;
    margin-right: 0;
}

.message-bubble.received {
    margin-left: 0;
    margin-right: auto;
}

.message-content {
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.message-content.sent {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-bottom-right-radius: 4px;
}

.message-content.received {
    background: white;
    color: #333;
    border-bottom-left-radius: 4px;
}

.message-meta {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.6;
    margin-top: 4px;
}

.typing-indicator {
    display: none;
    padding: 12px 16px;
    background: white;
    border-radius: 18px;
    border-bottom-left-radius: 4px;
    max-width: 70%;
    margin-bottom: 8px;
}

.typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #999;
    animation: typingPulse 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

.chat-input-container {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.chat-input-form {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    background: white;
    resize: none;
    max-height: 100px;
    min-height: 44px;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chat-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255,255,255,0.3);
}

.send-button {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.send-button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.send-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.conversation-stats {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    color: white;
}

.quick-replies {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.quick-reply-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-reply-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-sent { background: #28a745; }
.status-delivered { background: #17a2b8; }
.status-failed { background: #dc3545; }
.status-pending { background: #ffc107; }

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typingPulse {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.message-actions {
    position: absolute;
    top: -30px;
    right: 0;
    background: rgba(0,0,0,0.8);
    border-radius: 15px;
    padding: 4px 8px;
    display: none;
    gap: 8px;
}

.message-bubble:hover .message-actions {
    display: flex;
}

.action-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.7rem;
}

.action-btn:hover {
    background: rgba(255,255,255,0.2);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.connection-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.char-counter {
    font-size: 0.7rem;
    color: rgba(255,255,255,0.7);
    margin-left: 8px;
}

.char-counter.warning { color: #ffc107; }
.char-counter.danger { color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-white mb-1">
                        <i class="bi bi-chat-dots"></i> {{ phone_number }}
                    </h2>
                    <div class="connection-status">
                        <div class="connection-dot"></div>
                        <span class="text-white-50">Connected via Port {{ current_port or 'Auto' }}</span>
                    </div>
                </div>
                <div class="btn-group">
                    <a href="{{ url_for('inbox') }}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i> Back to Inbox
                    </a>
                    <button onclick="refreshConversation()" class="btn btn-outline-light">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                    <button onclick="clearConversation()" class="btn btn-outline-danger">
                        <i class="bi bi-trash"></i> Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Conversation Stats -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="conversation-stats">
                <div class="row text-center">
                    <div class="col-3">
                        <h5 class="mb-1">{{ stats.total_messages }}</h5>
                        <small>Total Messages</small>
                    </div>
                    <div class="col-3">
                        <h5 class="mb-1">{{ stats.sent_count }}</h5>
                        <small>Sent</small>
                    </div>
                    <div class="col-3">
                        <h5 class="mb-1">{{ stats.received_count }}</h5>
                        <small>Received</small>
                    </div>
                    <div class="col-3">
                        <h5 class="mb-1">{{ stats.last_message_time or 'Never' }}</h5>
                        <small>Last Message</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Container -->
    <div class="row">
        <div class="col-12">
            <div class="chat-container">
                <!-- Chat Header -->
                <div class="chat-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1">{{ phone_number }}</h5>
                            <small class="opacity-75">{{ messages|length }} messages in conversation</small>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <span class="badge bg-success">Online</span>
                            <button onclick="toggleNotifications()" class="btn btn-sm btn-outline-light">
                                <i class="bi bi-bell"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Messages Area -->
                <div class="chat-messages" id="messagesContainer">
                    {% if messages %}
                        {% for message in messages %}
                        <div class="message-bubble {{ 'sent' if message.direction == 'outbound' else 'received' }}">
                            <div class="message-content {{ 'sent' if message.direction == 'outbound' else 'received' }}">
                                <div class="message-actions">
                                    <button class="action-btn" onclick="copyMessage('{{ message.content|e }}')">
                                        <i class="bi bi-copy"></i>
                                    </button>
                                    {% if message.direction == 'inbound' %}
                                    <button class="action-btn" onclick="replyToMessage('{{ message.content|e }}')">
                                        <i class="bi bi-reply"></i>
                                    </button>
                                    {% endif %}
                                    <button class="action-btn" onclick="deleteMessage({{ message.id }})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                                
                                <div class="message-text">{{ message.content }}</div>
                                
                                <div class="message-meta">
                                    <div class="message-time">
                                        {{ message.created_at.strftime('%H:%M') }}
                                        {% if message.status %}
                                            <span class="status-indicator status-{{ message.status }}"></span>
                                        {% endif %}
                                    </div>
                                    {% if message.gsm_port %}
                                        <small class="opacity-50">Port {{ message.gsm_port }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-chat-dots text-white-50" style="font-size: 3rem;"></i>
                            <h5 class="text-white mt-3">No messages yet</h5>
                            <p class="text-white-50">Start the conversation by sending a message below</p>
                        </div>
                    {% endif %}

                    <!-- Typing Indicator -->
                    <div class="typing-indicator" id="typingIndicator">
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>

                <!-- Input Area -->
                <div class="chat-input-container">
                    <!-- Quick Replies -->
                    <div class="quick-replies">
                        <button class="quick-reply-btn" onclick="insertQuickReply('Thank you!')">Thank you!</button>
                        <button class="quick-reply-btn" onclick="insertQuickReply('Please call me.')">Please call me</button>
                        <button class="quick-reply-btn" onclick="insertQuickReply('I will get back to you.')">I will get back to you</button>
                        <button class="quick-reply-btn" onclick="insertQuickReply('Received.')">Received</button>
                    </div>

                    <!-- Input Form -->
                    <form class="chat-input-form" onsubmit="sendMessage(event)">
                        <input type="hidden" name="phone_number" value="{{ phone_number }}">
                        <textarea 
                            class="chat-input" 
                            name="message" 
                            placeholder="Type your message..." 
                            required
                            maxlength="1024"
                            rows="1"
                            id="messageInput"
                            onkeydown="handleKeyDown(event)"
                            oninput="updateCharCounter()"
                        ></textarea>
                        <button type="submit" class="send-button" id="sendButton">
                            <i class="bi bi-send"></i>
                        </button>
                    </form>
                    
                    <!-- Character Counter -->
                    <div class="d-flex justify-content-between align-items-center mt-2">
                        <small class="text-white-50">Press Enter to send, Shift+Enter for new line</small>
                        <span class="char-counter" id="charCounter">0/1024</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let isTyping = false;
let typingTimeout;

// Auto-scroll to bottom
function scrollToBottom() {
    const container = document.getElementById('messagesContainer');
    container.scrollTop = container.scrollHeight;
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    scrollToBottom();
    
    // Auto-refresh every 10 seconds
    setInterval(refreshMessages, 10000);
    
    // Focus input
    document.getElementById('messageInput').focus();
});

// Handle keyboard shortcuts
function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        document.querySelector('.chat-input-form').dispatchEvent(new Event('submit'));
    }
}

// Update character counter
function updateCharCounter() {
    const input = document.getElementById('messageInput');
    const counter = document.getElementById('charCounter');
    const length = input.value.length;
    
    counter.textContent = `${length}/1024`;
    
    if (length > 900) {
        counter.className = 'char-counter danger';
    } else if (length > 700) {
        counter.className = 'char-counter warning';
    } else {
        counter.className = 'char-counter';
    }
}

// Send message
async function sendMessage(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    
    if (!messageInput.value.trim()) return;
    
    // Disable form
    sendButton.disabled = true;
    messageInput.disabled = true;
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        const response = await fetch('/send_sms', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            // Clear input
            messageInput.value = '';
            updateCharCounter();
            
            // Refresh messages
            setTimeout(refreshMessages, 1000);
        } else {
            alert('Failed to send message');
        }
    } catch (error) {
        console.error('Error sending message:', error);
        alert('Error sending message');
    } finally {
        // Re-enable form
        sendButton.disabled = false;
        messageInput.disabled = false;
        messageInput.focus();
        hideTypingIndicator();
    }
}

// Quick reply functions
function insertQuickReply(text) {
    const input = document.getElementById('messageInput');
    input.value = text;
    input.focus();
    updateCharCounter();
}

// Message actions
function copyMessage(content) {
    navigator.clipboard.writeText(content).then(() => {
        showToast('Message copied to clipboard');
    });
}

function replyToMessage(content) {
    const input = document.getElementById('messageInput');
    input.value = `Re: ${content}\n\n`;
    input.focus();
    updateCharCounter();
}

function deleteMessage(messageId) {
    if (confirm('Are you sure you want to delete this message?')) {
        fetch(`/messages/${messageId}/delete`, {
            method: 'POST'
        }).then(response => {
            if (response.ok) {
                refreshMessages();
                showToast('Message deleted');
            } else {
                alert('Failed to delete message');
            }
        });
    }
}

// Utility functions
function showTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'block';
    scrollToBottom();
}

function hideTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'none';
}

function refreshConversation() {
    location.reload();
}

function refreshMessages() {
    // In a real implementation, this would fetch new messages via AJAX
    console.log('Refreshing messages...');
}

function clearConversation() {
    if (confirm('Are you sure you want to clear this conversation? This action cannot be undone.')) {
        // Implementation for clearing conversation
        alert('Conversation cleared');
    }
}

function toggleNotifications() {
    // Implementation for notification toggle
    showToast('Notifications toggled');
}

function showToast(message) {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = 'position-fixed top-0 end-0 m-3 alert alert-success alert-dismissible';
    toast.innerHTML = `${message} <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>`;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}

// Auto-resize textarea
document.getElementById('messageInput').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = Math.min(this.scrollHeight, 100) + 'px';
});
</script>
{% endblock %}
