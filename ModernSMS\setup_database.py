#!/usr/bin/env python3
"""
Database setup script for Modern SMS Manager
Creates MySQL database and user with proper permissions
"""
import mysql.connector
import sys
import os
from getpass import getpass

def create_database_and_user():
    """Create database and user for Modern SMS Manager"""
    
    print("="*60)
    print("🗄️  Modern SMS Manager - Database Setup")
    print("="*60)
    
    # Get MySQL root credentials
    print("\n📋 MySQL Root Credentials:")
    mysql_host = input("MySQL Host (default: localhost): ").strip() or "localhost"
    mysql_port = input("MySQL Port (default: 3306): ").strip() or "3306"
    mysql_root_user = input("MySQL Root Username (default: root): ").strip() or "root"
    mysql_root_password = getpass("MySQL Root Password: ")
    
    # Get database configuration
    print("\n📋 Database Configuration:")
    db_name = input("Database Name (default: modern_sms): ").strip() or "modern_sms"
    db_user = input("Database User (default: sms_user): ").strip() or "sms_user"
    db_password = getpass("Database Password: ")
    
    if not db_password:
        print("❌ Database password cannot be empty!")
        sys.exit(1)
    
    try:
        # Connect to MySQL as root
        print(f"\n🔌 Connecting to MySQL at {mysql_host}:{mysql_port}...")
        connection = mysql.connector.connect(
            host=mysql_host,
            port=int(mysql_port),
            user=mysql_root_user,
            password=mysql_root_password
        )
        
        cursor = connection.cursor()
        
        # Create database
        print(f"📊 Creating database '{db_name}'...")
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"✅ Database '{db_name}' created successfully")
        
        # Create user
        print(f"👤 Creating user '{db_user}'...")
        cursor.execute(f"CREATE USER IF NOT EXISTS '{db_user}'@'%' IDENTIFIED BY '{db_password}'")
        cursor.execute(f"CREATE USER IF NOT EXISTS '{db_user}'@'localhost' IDENTIFIED BY '{db_password}'")
        print(f"✅ User '{db_user}' created successfully")
        
        # Grant permissions
        print(f"🔐 Granting permissions...")
        cursor.execute(f"GRANT ALL PRIVILEGES ON {db_name}.* TO '{db_user}'@'%'")
        cursor.execute(f"GRANT ALL PRIVILEGES ON {db_name}.* TO '{db_user}'@'localhost'")
        cursor.execute("FLUSH PRIVILEGES")
        print(f"✅ Permissions granted successfully")
        
        # Test connection with new user
        print(f"🧪 Testing connection with new user...")
        test_connection = mysql.connector.connect(
            host=mysql_host,
            port=int(mysql_port),
            user=db_user,
            password=db_password,
            database=db_name
        )
        test_connection.close()
        print(f"✅ Connection test successful")
        
        # Create .env file
        print(f"\n📝 Creating .env file...")
        env_content = f"""# Database Configuration
DB_HOST={mysql_host}
DB_PORT={mysql_port}
DB_NAME={db_name}
DB_USER={db_user}
DB_PASSWORD={db_password}

# Flask Configuration
SECRET_KEY={generate_secret_key()}
FLASK_ENV=development
FLASK_DEBUG=True

# Default GSM Gateway Configuration (Fallback)
DEFAULT_GSM_IP=************
DEFAULT_GSM_USERNAME=apiuser
DEFAULT_GSM_PASSWORD=apipass
DEFAULT_GSM_API_PORT=80
DEFAULT_GSM_TG_PORT=5038
DEFAULT_GSM_MAX_PORTS=8

# Security
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5

# Application Settings
APP_NAME=Modern SMS Manager
APP_VERSION=1.0.0
ITEMS_PER_PAGE=20
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print(f"✅ .env file created successfully")
        
        print("\n" + "="*60)
        print("🎉 Database setup completed successfully!")
        print("="*60)
        print(f"📊 Database: {db_name}")
        print(f"👤 User: {db_user}")
        print(f"🌐 Host: {mysql_host}:{mysql_port}")
        print(f"📝 Configuration saved to .env file")
        print("\n🚀 You can now run the application with:")
        print("   python run.py")
        print("="*60)
        
    except mysql.connector.Error as err:
        print(f"❌ MySQL Error: {err}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()

def generate_secret_key():
    """Generate a random secret key"""
    import secrets
    return secrets.token_hex(32)

def check_mysql_connection():
    """Check if MySQL is accessible"""
    try:
        # Try to import mysql.connector
        import mysql.connector
        return True
    except ImportError:
        print("❌ mysql-connector-python is not installed!")
        print("   Install it with: pip install mysql-connector-python")
        return False

def main():
    """Main setup function"""
    if not check_mysql_connection():
        sys.exit(1)
    
    print("Welcome to Modern SMS Manager Database Setup!")
    print("This script will create a MySQL database and user for the application.")
    
    confirm = input("\nDo you want to continue? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("Setup cancelled.")
        sys.exit(0)
    
    create_database_and_user()

if __name__ == '__main__':
    main()
