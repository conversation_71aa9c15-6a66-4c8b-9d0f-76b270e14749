#!/usr/bin/env python3
"""
Test Flask routes to see what's available
"""

import sys
import os
sys.path.append('backend')

def test_routes():
    print("🔍 Testing Flask Routes")
    print("=" * 25)
    
    try:
        from app import app
        
        with app.app_context():
            print("📋 Available Routes:")
            print("-" * 20)
            
            for rule in app.url_map.iter_rules():
                methods = ','.join(rule.methods - {'HEAD', 'OPTIONS'})
                print(f"   {rule.endpoint:30} {methods:15} {rule.rule}")
            
            print(f"\n🔍 Looking for GSM Gateway routes...")
            gsm_routes = [rule for rule in app.url_map.iter_rules() if 'gsm' in rule.rule.lower()]
            
            if gsm_routes:
                print("✅ Found GSM Gateway routes:")
                for route in gsm_routes:
                    methods = ','.join(route.methods - {'HEAD', 'OPTIONS'})
                    print(f"   {route.endpoint:30} {methods:15} {route.rule}")
            else:
                print("❌ No GSM Gateway routes found!")
                print("   This means the routes weren't registered properly.")
            
            return len(gsm_routes) > 0
    
    except Exception as e:
        print(f"❌ Error testing routes: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_routes()
    if success:
        print("\n✅ GSM Gateway routes are registered")
    else:
        print("\n❌ GSM Gateway routes are missing")
