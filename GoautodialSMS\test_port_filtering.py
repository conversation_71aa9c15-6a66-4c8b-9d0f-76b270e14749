#!/usr/bin/env python3
"""
Test port-based filtering for different users
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSMessage, User

def test_port_filtering():
    with app.app_context():
        print("🔒 Testing Port-Based Access Control")
        print("=" * 50)
        
        # Get users
        admin = User.query.filter_by(username='admin').first()
        test1 = User.query.filter_by(username='test1').first()  # Port 1
        diet = User.query.filter_by(username='diet').first()    # Port 2
        test2 = User.query.filter_by(username='test2').first()  # No ports
        
        users_to_test = [
            ('admin', admin),
            ('test1', test1),
            ('diet', diet),
            ('test2', test2)
        ]
        
        for username, user in users_to_test:
            if not user:
                print(f"❌ User {username} not found")
                continue
                
            print(f"\n👤 Testing user: {username}")
            print(f"   Role: {user.role}")
            print(f"   Assigned ports: {user.get_assigned_ports()}")
            
            # Test inbox filtering (inbound messages)
            if user.role == 'admin':
                # Ad<PERSON> sees all messages
                inbox_query = SMSMessage.query.filter_by(direction='inbound')
            else:
                # Regular users only see messages from assigned ports
                assigned_ports = user.get_assigned_ports()
                if assigned_ports:
                    inbox_query = SMSMessage.query.filter_by(direction='inbound').filter(SMSMessage.gsm_port.in_(assigned_ports))
                else:
                    # If no ports assigned, show all (backward compatibility)
                    inbox_query = SMSMessage.query.filter_by(direction='inbound')
            
            inbox_messages = inbox_query.all()
            
            # Test outbox filtering (outbound messages)
            if user.role == 'admin':
                # Admin sees all messages
                outbox_query = SMSMessage.query.filter_by(direction='outbound')
            else:
                # Regular users only see messages from assigned ports
                assigned_ports = user.get_assigned_ports()
                if assigned_ports:
                    outbox_query = SMSMessage.query.filter_by(direction='outbound').filter(SMSMessage.gsm_port.in_(assigned_ports))
                else:
                    # If no ports assigned, show all (backward compatibility)
                    outbox_query = SMSMessage.query.filter_by(direction='outbound')
            
            outbox_messages = outbox_query.all()
            
            print(f"   📥 Inbox messages visible: {len(inbox_messages)}")
            for msg in inbox_messages[-3:]:  # Show last 3
                print(f"      Port {msg.gsm_port}: {msg.phone_number} - {msg.content[:30]}...")
            
            print(f"   📤 Outbox messages visible: {len(outbox_messages)}")
            for msg in outbox_messages[-3:]:  # Show last 3
                print(f"      Port {msg.gsm_port}: {msg.phone_number} - {msg.content[:30]}...")
            
            # Test port usage permissions
            print(f"   🔌 Port usage permissions:")
            for port in ['1', '2', '3']:
                can_use = user.can_use_port(port)
                print(f"      Port {port}: {'✅ Can use' if can_use else '❌ Cannot use'}")

if __name__ == "__main__":
    test_port_filtering()
