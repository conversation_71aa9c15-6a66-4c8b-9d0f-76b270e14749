#!/usr/bin/env python3
"""
Force restart SMS receiver and monitor connection
"""
import os
import sys
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def force_restart_receiver():
    """Force restart SMS receiver"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔄 FORCE RESTARTING SMS RECEIVER")
        print("="*60)
        
        # Stop any existing receiver
        receiver = getattr(app, 'sms_receiver', None)
        if receiver:
            print("🛑 Stopping existing receiver...")
            try:
                receiver.stop_receiver()
                time.sleep(2)
                print("✅ Existing receiver stopped")
            except Exception as e:
                print(f"⚠️  Error stopping receiver: {str(e)}")
        
        # Get gateway info
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        if not gateway:
            print("❌ No primary gateway found!")
            return False
        
        print(f"📡 Gateway: {gateway.name}")
        print(f"   IP: {gateway.ip_address}:{gateway.tg_sms_port}")
        print(f"   Credentials: {gateway.username}/{'*' * len(gateway.password)}")
        
        # Create and start new receiver
        print("\n🚀 Creating new SMS receiver...")
        try:
            from app.sms.receiver import SMSReceiver
            
            # Create new receiver
            new_receiver = SMSReceiver(app)
            
            # Start receiver
            print("▶️  Starting receiver...")
            new_receiver.start_receiver()
            
            # Store in app
            app.sms_receiver = new_receiver
            
            print("✅ SMS receiver started!")
            
            # Monitor status for 10 seconds
            print("\n📊 Monitoring receiver status...")
            for i in range(10):
                time.sleep(1)
                status = f"Running: {'✅' if new_receiver.running else '❌'} | Connected: {'✅' if new_receiver.connected else '❌'} | Thread: {'✅' if (new_receiver.thread and new_receiver.thread.is_alive()) else '❌'}"
                print(f"   {i+1:2d}s: {status}")
                
                if not new_receiver.running:
                    print("⚠️  Receiver stopped running!")
                    break
                    
                if not new_receiver.connected:
                    print("⚠️  Receiver lost connection!")
                    break
            
            # Final status
            print(f"\n🎯 Final Status:")
            print(f"   Running: {'✅ Yes' if new_receiver.running else '❌ No'}")
            print(f"   Connected: {'✅ Yes' if new_receiver.connected else '❌ No'}")
            print(f"   Thread alive: {'✅ Yes' if (new_receiver.thread and new_receiver.thread.is_alive()) else '❌ No'}")
            
            if new_receiver.running and new_receiver.connected:
                print("🎉 SMS receiver is working properly!")
                print("📱 Try sending an SMS to test receiving")
                return True
            else:
                print("❌ SMS receiver has issues")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start receiver: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def test_manual_connection():
    """Test manual TG SMS connection to see raw events"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔍 TESTING MANUAL TG SMS CONNECTION")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True, status='active').first()
        if not gateway:
            print("❌ No gateway found!")
            return
        
        import socket
        
        try:
            print(f"🔌 Connecting to {gateway.ip_address}:{gateway.tg_sms_port}...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Read initial response
            initial = sock.recv(1024).decode()
            print(f"📥 Initial: {initial.strip()}")
            
            # Send login
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            # Read login response
            login_resp = sock.recv(1024).decode()
            print(f"📥 Login: {login_resp.strip()}")
            
            if "Response: Success" in login_resp:
                print("✅ Login successful")
                
                # Subscribe to events
                event_cmd = "Action: Events\r\nEventMask: sms\r\n\r\n"
                sock.send(event_cmd.encode())
                
                event_resp = sock.recv(1024).decode()
                print(f"📥 Events: {event_resp.strip()}")
                
                # Listen for events for 15 seconds
                print(f"\n👂 Listening for SMS events for 15 seconds...")
                print("   📱 Send an SMS now to see the raw event format!")
                
                sock.settimeout(1)
                start_time = time.time()
                
                while time.time() - start_time < 15:
                    try:
                        data = sock.recv(4096).decode()
                        if data:
                            print(f"\n📨 RAW EVENT DATA:")
                            print(f"   {repr(data)}")
                            print(f"📝 FORMATTED:")
                            print(f"   {data}")
                            
                    except socket.timeout:
                        print(".", end="", flush=True)
                        continue
                    except Exception as e:
                        print(f"\n❌ Error: {str(e)}")
                        break
                
                print(f"\n⏰ Listening period ended")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Connection failed: {str(e)}")

if __name__ == '__main__':
    print("🚀 SMS Receiver Force Restart Tool")
    
    # Force restart receiver
    success = force_restart_receiver()
    
    if success:
        print(f"\n✅ SMS receiver restart successful!")
    else:
        print(f"\n❌ SMS receiver restart failed!")
        
        # Try manual connection test
        print(f"\n🔍 Testing manual connection to see raw events...")
        test_manual_connection()
    
    print("\n" + "="*60)
