#!/usr/bin/env python3
"""
Test SMS events after enabling on gateway
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def test_events_enabled():
    """Test if SMS events are now enabled"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🧪 TESTING SMS EVENTS AFTER GATEWAY CONFIGURATION")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        print(f"📡 Testing: {gateway.name} ({gateway.ip_address}:{gateway.tg_sms_port})")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Read initial and login
            initial = sock.recv(1024).decode()
            print(f"📥 Initial: {initial.strip()}")
            
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            login_resp = sock.recv(1024).decode()
            print(f"📥 Login: {login_resp.strip()}")
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed!")
                return False
            
            # Test event subscription
            event_cmd = "Action: Events\r\nEventMask: all\r\n\r\n"
            sock.send(event_cmd.encode())
            
            event_resp = sock.recv(1024).decode()
            print(f"📥 Events: {event_resp.strip()}")
            
            if "Events: On" in event_resp:
                print("🎉 SMS EVENTS ARE NOW ENABLED!")
                
                # Listen for events
                print("👂 Listening for SMS events for 15 seconds...")
                print("📱 Send an SMS to your gateway number NOW!")
                
                sock.settimeout(1)
                start_time = time.time()
                event_count = 0
                
                while time.time() - start_time < 15:
                    try:
                        data = sock.recv(4096).decode()
                        if data and "Event:" in data:
                            event_count += 1
                            print(f"\n📨 SMS EVENT {event_count} RECEIVED!")
                            print(f"   {data}")
                            
                            if "ReceivedSMS" in data or "SMS" in data:
                                print("✅ This is an SMS event - SMS receiving will work!")
                                
                    except socket.timeout:
                        print(".", end="", flush=True)
                        continue
                
                print(f"\n📊 Total events received: {event_count}")
                
                if event_count > 0:
                    print("🎉 SMS events are working! Restarting SMS receiver...")
                    
                    # Restart SMS receiver
                    try:
                        from app.sms.receiver import SMSReceiver
                        new_receiver = SMSReceiver(app)
                        new_receiver.start_receiver()
                        app.sms_receiver = new_receiver
                        
                        time.sleep(2)
                        
                        print(f"✅ SMS receiver restarted and should now work!")
                        return True
                        
                    except Exception as e:
                        print(f"❌ Error restarting receiver: {str(e)}")
                        return False
                else:
                    print("⚠️  No events received during test")
                    print("   Try sending an SMS and run this test again")
                    return False
                    
            else:
                print("❌ SMS events are still disabled")
                print("💡 Check gateway web interface SMS event settings")
                return False
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False

if __name__ == '__main__':
    print("🚀 SMS Events Test Tool")
    print("📋 Run this AFTER enabling SMS events on gateway")
    
    success = test_events_enabled()
    
    if success:
        print(f"\n🎉 SUCCESS! SMS receiving is now working!")
        print(f"📱 Try sending an SMS to test")
    else:
        print(f"\n❌ SMS events still not working")
        print(f"🔧 Check gateway SMS event configuration")
    
    print("\n" + "="*60)
