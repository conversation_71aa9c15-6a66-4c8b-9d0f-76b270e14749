"""
Gateway management routes
"""
from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app.gateways import bp
from app.models import GSMGateway, SMSPort
from app.sms.services import GSMPortService
from app import db
from datetime import datetime

def require_gateway_permission(f):
    """Decorator to require gateway management permission"""
    def decorated_function(*args, **kwargs):
        if not current_user.has_permission('manage_gateways'):
            flash('Access denied. Gateway management permission required.', 'error')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@bp.route('/')
@login_required
@require_gateway_permission
def index():
    """Gateway management dashboard"""
    try:
        gateways = GSMGateway.query.all()
        
        # Calculate statistics
        stats = {
            'total_gateways': len(gateways),
            'active_gateways': len([g for g in gateways if g.status == 'active']),
            'total_ports': sum(g.max_ports for g in gateways),
            'active_ports': SMSPort.query.filter_by(status='active').count()
        }
        
        return render_template('gateways/index.html', gateways=gateways, stats=stats)
        
    except Exception as e:
        current_app.logger.error(f"Gateway index error: {str(e)}")
        flash('Error loading gateways.', 'error')
        return render_template('gateways/index.html', gateways=[], stats={})

@bp.route('/add', methods=['GET', 'POST'])
@login_required
@require_gateway_permission
def add():
    """Add new gateway"""
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name', '').strip()
            ip_address = request.form.get('ip_address', '').strip()
            api_port = int(request.form.get('api_port', 80))
            tg_sms_port = int(request.form.get('tg_sms_port', 5038))
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '').strip()
            max_ports = int(request.form.get('max_ports', 8))
            location = request.form.get('location', '').strip()
            description = request.form.get('description', '').strip()
            is_primary = bool(request.form.get('is_primary'))
            
            # Validate required fields
            if not all([name, ip_address, username, password]):
                error_msg = 'Name, IP address, username, and password are required'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': error_msg})
                flash(error_msg, 'error')
                return render_template('gateways/add.html')
            
            # Check for duplicate IP
            existing = GSMGateway.query.filter_by(ip_address=ip_address).first()
            if existing:
                error_msg = f'Gateway with IP {ip_address} already exists'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': error_msg})
                flash(error_msg, 'error')
                return render_template('gateways/add.html')
            
            # If setting as primary, unset other primary gateways
            if is_primary:
                GSMGateway.query.filter_by(is_primary=True).update({'is_primary': False})
            
            # Create gateway
            gateway = GSMGateway(
                name=name,
                ip_address=ip_address,
                api_port=api_port,
                tg_sms_port=tg_sms_port,
                username=username,
                password=password,
                max_ports=max_ports,
                location=location,
                description=description,
                is_primary=is_primary,
                status='active'
            )
            
            db.session.add(gateway)
            db.session.flush()  # Get gateway ID
            
            # Create ports for this gateway
            for port_num in range(1, max_ports + 1):
                port = SMSPort(
                    port_number=str(port_num),
                    gateway_id=gateway.id,
                    status='unknown',
                    is_active=True
                )
                db.session.add(port)
            
            db.session.commit()
            
            success_msg = f'Gateway "{name}" added successfully with {max_ports} ports'
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': True,
                    'message': success_msg,
                    'gateway_id': gateway.id
                })
            
            flash(success_msg, 'success')
            return redirect(url_for('gateways.index'))
            
        except ValueError as e:
            error_msg = 'Invalid numeric values provided'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': error_msg})
            flash(error_msg, 'error')
            return render_template('gateways/add.html')
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Add gateway error: {str(e)}")
            error_msg = 'Error adding gateway'
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': error_msg})
            flash(error_msg, 'error')
            return render_template('gateways/add.html')
    
    return render_template('gateways/add.html')

@bp.route('/<int:gateway_id>')
@login_required
@require_gateway_permission
def detail(gateway_id):
    """Gateway detail view"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)
        ports = gateway.ports.all()
        
        return render_template('gateways/detail.html', gateway=gateway, ports=ports)
        
    except Exception as e:
        current_app.logger.error(f"Gateway detail error: {str(e)}")
        flash('Error loading gateway details.', 'error')
        return redirect(url_for('gateways.index'))

@bp.route('/<int:gateway_id>/edit', methods=['GET', 'POST'])
@login_required
@require_gateway_permission
def edit(gateway_id):
    """Edit gateway"""
    gateway = GSMGateway.query.get_or_404(gateway_id)
    
    if request.method == 'POST':
        try:
            # Update gateway fields
            gateway.name = request.form.get('name', '').strip()
            gateway.ip_address = request.form.get('ip_address', '').strip()
            gateway.api_port = int(request.form.get('api_port', 80))
            gateway.tg_sms_port = int(request.form.get('tg_sms_port', 5038))
            gateway.username = request.form.get('username', '').strip()
            gateway.password = request.form.get('password', '').strip()
            gateway.max_ports = int(request.form.get('max_ports', 8))
            gateway.location = request.form.get('location', '').strip()
            gateway.description = request.form.get('description', '').strip()
            gateway.status = request.form.get('status', 'active')
            
            is_primary = bool(request.form.get('is_primary'))
            
            # Validate required fields
            if not all([gateway.name, gateway.ip_address, gateway.username, gateway.password]):
                flash('Name, IP address, username, and password are required', 'error')
                return render_template('gateways/edit.html', gateway=gateway)

            # Check for duplicate IP (excluding current gateway)
            existing = GSMGateway.query.filter(
                GSMGateway.ip_address == gateway.ip_address,
                GSMGateway.id != gateway_id
            ).first()

            if existing:
                flash(f'Another gateway with IP {gateway.ip_address} already exists', 'error')
                return render_template('gateways/edit.html', gateway=gateway)

            # Handle primary gateway setting
            if is_primary and not gateway.is_primary:
                # Unset other primary gateways
                GSMGateway.query.filter_by(is_primary=True).update({'is_primary': False})
                gateway.is_primary = True
            elif not is_primary and gateway.is_primary:
                gateway.is_primary = False

            gateway.updated_at = datetime.now()
            db.session.commit()

            flash(f'Gateway "{gateway.name}" updated successfully', 'success')
            return redirect(url_for('gateways.detail', gateway_id=gateway_id))

        except ValueError:
            flash('Invalid numeric values provided', 'error')
            return render_template('gateways/edit.html', gateway=gateway)

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Edit gateway error: {str(e)}")
            flash('Error updating gateway', 'error')
            return render_template('gateways/edit.html', gateway=gateway)

    return render_template('gateways/edit.html', gateway=gateway)

@bp.route('/<int:gateway_id>/delete', methods=['POST'])
@login_required
@require_gateway_permission
def delete(gateway_id):
    """Delete gateway"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)

        # Check if it's the primary gateway
        if gateway.is_primary:
            error_msg = 'Cannot delete primary gateway. Set another gateway as primary first.'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': error_msg})
            flash(error_msg, 'error')
            return redirect(url_for('gateways.index'))

        gateway_name = gateway.name

        # Delete associated ports (cascade should handle this, but being explicit)
        SMSPort.query.filter_by(gateway_id=gateway_id).delete()

        # Delete gateway
        db.session.delete(gateway)
        db.session.commit()

        success_msg = f'Gateway "{gateway_name}" deleted successfully'

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': True, 'message': success_msg})

        flash(success_msg, 'success')
        return redirect(url_for('gateways.index'))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Delete gateway error: {str(e)}")
        error_msg = 'Error deleting gateway'

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'error': error_msg})
        flash(error_msg, 'error')
        return redirect(url_for('gateways.index'))

@bp.route('/<int:gateway_id>/test', methods=['POST'])
@login_required
@require_gateway_permission
def test_connection(gateway_id):
    """Test gateway connection"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)

        # Test connection by trying to get port status
        test_result = GSMPortService.detect_port_status(gateway, '1')

        if test_result['status'] != 'error':
            return jsonify({
                'success': True,
                'message': 'Connection successful',
                'details': test_result
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Connection failed - gateway not responding'
            })

    except Exception as e:
        current_app.logger.error(f"Gateway test error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/<int:gateway_id>/ports/refresh', methods=['POST'])
@login_required
@require_gateway_permission
def refresh_ports(gateway_id):
    """Refresh port status for gateway"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)

        if gateway.status != 'active':
            return jsonify({'success': False, 'error': 'Gateway is not active'})

        updated_count = 0
        ports_data = []

        for port in gateway.ports.filter_by(is_active=True).all():
            status_info = GSMPortService.detect_port_status(gateway, port.port_number)

            # Update port
            port.status = status_info['status']
            port.network_name = status_info['network_name']
            port.signal_strength = status_info['signal_strength']
            port.phone_number = status_info['phone_number']
            port.last_checked = datetime.now()

            ports_data.append(port.to_dict())
            updated_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Refreshed {updated_count} ports',
            'ports': ports_data
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Port refresh error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/api/<int:gateway_id>/ports')
@login_required
@require_gateway_permission
def api_gateway_ports(gateway_id):
    """API endpoint for gateway ports"""
    try:
        gateway = GSMGateway.query.get_or_404(gateway_id)
        ports = gateway.ports.all()

        return jsonify({
            'success': True,
            'gateway_id': gateway_id,
            'gateway_name': gateway.name,
            'ports': [port.to_dict() for port in ports]
        })

    except Exception as e:
        current_app.logger.error(f"Gateway ports API error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
