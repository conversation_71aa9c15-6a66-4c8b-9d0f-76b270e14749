#!/usr/bin/env python3
"""
Clean test for events after reboot
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def clean_event_test():
    """Clean test for event subscription"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🧪 CLEAN EVENT SUBSCRIPTION TEST")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        try:
            # Fresh connection
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            print(f"📡 Connected to {gateway.ip_address}:{gateway.tg_sms_port}")
            
            # Read initial and login
            initial = sock.recv(1024).decode()
            print(f"📥 Initial: {initial.strip()}")
            
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            login_resp = sock.recv(1024).decode()
            print(f"📥 Login: {login_resp.strip()}")
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed!")
                return False
            
            print("✅ Login successful!")
            
            # Test ONLY event subscription (no other commands)
            print(f"\n📡 Testing event subscription...")
            event_cmd = "Action: Events\r\n\r\n"
            sock.send(event_cmd.encode())
            
            # Read event response
            event_resp = sock.recv(1024).decode()
            print(f"📥 Event response: {event_resp.strip()}")
            
            if "Events: On" in event_resp:
                print("🎉 EVENTS ARE ENABLED!")
                
                # Listen for events
                print(f"\n👂 Listening for SMS events for 20 seconds...")
                print(f"📱 SEND AN SMS TO YOUR GATEWAY NUMBER NOW!")
                print(f"   (GSM span 2 is active and ready)")
                
                sock.settimeout(1)
                start_time = time.time()
                event_count = 0
                
                while time.time() - start_time < 20:
                    try:
                        data = sock.recv(4096).decode()
                        if data and "Event:" in data:
                            event_count += 1
                            print(f"\n📨 SMS EVENT {event_count} RECEIVED!")
                            print(f"📄 Event data:")
                            print(f"   {data}")
                            
                            if "ReceivedSMS" in data:
                                print(f"🎉 SMS RECEIVING IS WORKING!")
                                
                                # Parse SMS details
                                lines = data.split('\n')
                                sender = None
                                content = None
                                port = None
                                
                                for line in lines:
                                    if 'Sender:' in line:
                                        sender = line.split(':', 1)[1].strip()
                                    elif 'Content:' in line:
                                        content = line.split(':', 1)[1].strip()
                                    elif 'GsmPort:' in line:
                                        port = line.split(':', 1)[1].strip()
                                
                                print(f"📞 From: {sender}")
                                print(f"💬 Message: {content}")
                                print(f"📱 Port: {port}")
                                
                                sock.close()
                                return True
                                
                    except socket.timeout:
                        print(".", end="", flush=True)
                        continue
                    except Exception as e:
                        print(f"\n❌ Error reading events: {str(e)}")
                        break
                
                print(f"\n📊 Total events received: {event_count}")
                
                if event_count > 0:
                    print(f"✅ Events are working! (No SMS sent during test)")
                else:
                    print(f"⚠️  No events received (try sending SMS)")
                
                sock.close()
                return True
                
            elif "Success" in event_resp:
                print("✅ Event subscription successful!")
                
                # Try alternative event check
                print(f"\n🔍 Checking event status...")
                status_cmd = "Action: Events\r\nEventMask: all\r\n\r\n"
                sock.send(status_cmd.encode())
                
                status_resp = sock.recv(1024).decode()
                print(f"📥 Status response: {status_resp.strip()}")
                
                if "Events: On" in status_resp:
                    print("🎉 Events are enabled with EventMask!")
                    sock.close()
                    return True
                
                sock.close()
                return False
                
            else:
                print("❌ Event subscription failed")
                print(f"💡 Response: {event_resp}")
                sock.close()
                return False
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return False

def test_gsm_status():
    """Test GSM status to see which ports have SIM cards"""
    app = create_app()
    
    with app.app_context():
        print(f"\n📱 GSM STATUS CHECK")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((gateway.ip_address, gateway.tg_sms_port))
            
            # Login
            initial = sock.recv(1024).decode()
            login_cmd = f"Action: Login\r\nUsername: {gateway.username}\r\nSecret: {gateway.password}\r\n\r\n"
            sock.send(login_cmd.encode())
            login_resp = sock.recv(1024).decode()
            
            if "Response: Success" not in login_resp:
                print("❌ Login failed")
                return
            
            # Check GSM spans
            gsm_cmd = "Action: smscommand\r\ncommand: gsm show spans\r\n\r\n"
            sock.send(gsm_cmd.encode())
            
            gsm_resp = sock.recv(2048).decode()
            print(f"📱 GSM Spans Status:")
            print(f"   {gsm_resp}")
            
            # Check which spans are ready for SMS
            if "GSM span 2: Power on, Up, Active" in gsm_resp:
                print(f"✅ GSM span 2 is ready for SMS!")
                print(f"📱 Send SMS to the phone number of GSM span 2")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Error checking GSM status: {str(e)}")

if __name__ == '__main__':
    print("🚀 Clean Event Test After Reboot")
    
    # Test GSM status first
    test_gsm_status()
    
    # Test events
    events_working = clean_event_test()
    
    if events_working:
        print(f"\n🎉 SUCCESS! SMS EVENTS ARE WORKING!")
        print(f"📱 SMS receiving should be operational")
        print(f"💡 Try sending an SMS to GSM span 2's phone number")
    else:
        print(f"\n⚠️  Events need more configuration")
        print(f"💡 But major progress made - GSM spans are now visible!")
    
    print("\n" + "="*60)
