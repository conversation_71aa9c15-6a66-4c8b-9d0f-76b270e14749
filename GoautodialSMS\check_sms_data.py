#!/usr/bin/env python3
"""
Check SMS data in database
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSMessage, SMSPort

def check_sms_data():
    with app.app_context():
        print("📱 SMS Data in Database")
        print("=" * 40)
        
        # Get all messages
        messages = SMSMessage.query.all()
        print(f"Total messages: {len(messages)}")
        
        # Group by direction
        inbound = SMSMessage.query.filter_by(direction='inbound').all()
        outbound = SMSMessage.query.filter_by(direction='outbound').all()
        
        print(f"Inbound messages: {len(inbound)}")
        print(f"Outbound messages: {len(outbound)}")
        
        print(f"\n📥 Inbound Messages:")
        for msg in inbound:
            print(f"  ID: {msg.id}")
            print(f"  From: {msg.phone_number}")
            print(f"  Content: {msg.content}")
            print(f"  Port: {msg.gsm_port}")
            print(f"  Status: {msg.status}")
            print(f"  Time: {msg.created_at}")
            print(f"  ---")
        
        print(f"\n📤 Outbound Messages:")
        for msg in outbound:
            print(f"  ID: {msg.id}")
            print(f"  To: {msg.phone_number}")
            print(f"  Content: {msg.content}")
            print(f"  Port: {msg.gsm_port}")
            print(f"  Status: {msg.status}")
            print(f"  Time: {msg.created_at}")
            print(f"  ---")
        
        # Check ports
        ports = SMSPort.query.all()
        print(f"\n🔌 SMS Ports: {len(ports)}")
        for port in ports:
            print(f"  Port {port.port_number}: {port.status}")

if __name__ == "__main__":
    check_sms_data()
