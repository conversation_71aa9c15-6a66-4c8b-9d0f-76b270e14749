{% extends "base.html" %}

{% block title %}Outbox - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">SMS Outbox</h1>
        <p class="text-muted">Sent messages</p>
    </div>
    <div>
        {% if current_user.can_export_data %}
        <button class="btn btn-outline-success" onclick="exportMessages('outbox')">
            <i class="bi bi-download"></i> Export
        </button>
        {% endif %}

        {% if current_user.can_delete_messages %}
        <button class="btn btn-outline-danger" onclick="deleteSelectedMessages()" id="deleteSelectedBtn" style="display: none;">
            <i class="bi bi-trash"></i> Delete Selected
        </button>
        {% endif %}

        <a href="{{ url_for('sms.compose') }}" class="btn btn-primary">
            <i class="bi bi-pencil-square"></i> Compose SMS
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-send"></i> Sent Messages</h5>
        <span class="badge bg-primary">{{ messages.total if messages else 0 }} messages</span>
    </div>
    <div class="card-body">
        {% if messages and messages.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            {% if current_user.can_delete_messages %}
                            <th width="40">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            {% endif %}
                            <th>To</th>
                            <th>Message</th>
                            <th>Status</th>
                            <th>Port</th>
                            <th>Sent</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for message in messages.items %}
                        <tr>
                            {% if current_user.can_delete_messages %}
                            <td>
                                <input type="checkbox" class="message-checkbox" value="{{ message.id }}" onchange="updateDeleteButton()">
                            </td>
                            {% endif %}
                            <td>
                                <a href="{{ url_for('sms.conversation', phone_number=message.phone_number) }}"
                                   class="text-decoration-none fw-bold">
                                    {{ message.phone_number }}
                                </a>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 300px;">
                                    {{ message.content }}
                                </div>
                            </td>
                            <td>
                                {% if message.status == 'sent' %}
                                    <span class="badge bg-success">Sent</span>
                                {% elif message.status == 'delivered' %}
                                    <span class="badge bg-success">Delivered</span>
                                {% elif message.status == 'failed' %}
                                    <span class="badge bg-danger">Failed</span>
                                {% elif message.status == 'pending' %}
                                    <span class="badge bg-warning">Pending</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ message.status.title() }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if message.port %}
                                    <span class="badge bg-info">Port {{ message.port.port_number }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">Unknown</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ message.created_at.strftime('%m/%d/%Y %H:%M') }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('sms.conversation', phone_number=message.phone_number) }}"
                                       class="btn btn-outline-primary" title="View Conversation">
                                        <i class="bi bi-chat-dots"></i>
                                    </a>
                                    <a href="{{ url_for('sms.message_detail', message_id=message.id) }}"
                                       class="btn btn-outline-info" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    {% if current_user.can_delete_messages %}
                                    <button class="btn btn-outline-danger" onclick="deleteMessage({{ message.id }})" title="Delete Message">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if messages.pages > 1 %}
            <nav aria-label="Outbox pagination">
                <ul class="pagination justify-content-center">
                    {% if messages.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sms.outbox', page=messages.prev_num) }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in messages.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != messages.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('sms.outbox', page=page_num) }}">{{ page_num }}</a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if messages.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sms.outbox', page=messages.next_num) }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-send text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">No Messages Sent</h4>
                <p class="text-muted">You haven't sent any SMS messages yet.</p>
                <a href="{{ url_for('sms.compose') }}" class="btn btn-primary">
                    <i class="bi bi-pencil-square"></i> Send Your First Message
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Export messages
    function exportMessages(type) {
        const url = `/api/export-messages?type=${type}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Create download link
                    const blob = new Blob([data.csv_data], { type: 'text/csv' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = data.filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showToast(`Exported ${data.count} messages successfully`, 'success');
                } else {
                    showToast('Export failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('Export error: ' + error.message, 'error');
            });
    }

    // Delete single message
    function deleteMessage(messageId) {
        if (!confirm('Are you sure you want to delete this message? This action cannot be undone.')) {
            return;
        }

        fetch(`/api/delete-message/${messageId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Message deleted successfully', 'success');
                location.reload();
            } else {
                showToast('Delete failed: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Delete error: ' + error.message, 'error');
        });
    }

    // Delete selected messages
    function deleteSelectedMessages() {
        const checkboxes = document.querySelectorAll('.message-checkbox:checked');
        if (checkboxes.length === 0) {
            showToast('Please select messages to delete', 'warning');
            return;
        }

        const messageIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

        if (!confirm(`Are you sure you want to delete ${messageIds.length} selected message(s)? This action cannot be undone.`)) {
            return;
        }

        fetch('/api/delete-messages', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message_ids: messageIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                location.reload();
            } else {
                showToast('Delete failed: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Delete error: ' + error.message, 'error');
        });
    }

    // Toggle select all checkboxes
    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.message-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        updateDeleteButton();
    }

    // Update delete button visibility
    function updateDeleteButton() {
        const checkboxes = document.querySelectorAll('.message-checkbox:checked');
        const deleteBtn = document.getElementById('deleteSelectedBtn');

        if (deleteBtn) {
            if (checkboxes.length > 0) {
                deleteBtn.style.display = 'inline-block';
            } else {
                deleteBtn.style.display = 'none';
            }
        }
    }
</script>
{% endblock %}
