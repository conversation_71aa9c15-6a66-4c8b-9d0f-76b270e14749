{% extends "base.html" %}

{% block title %}GSM Gateway{% endblock %}

{% block extra_css %}
<style>
.gateway-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
}

.port-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-top: 10px;
}

.port-status {
    padding: 8px;
    border-radius: 4px;
    text-align: center;
    font-size: 0.875rem;
    border: 1px solid #dee2e6;
}

.port-active {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.port-inactive {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-router"></i> GSM Gateway</h2>
                    <p class="text-muted">Single GSM Gateway Status</p>
                </div>
            </div>

            <!-- Simple Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">1</h4>
                                    <p class="mb-0">Gateway</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-router" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">4</h4>
                                    <p class="mb-0">Total Ports</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-hdd-stack" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ active_ports }}</h4>
                                    <p class="mb-0">Active Ports</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-signal" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ 4 - active_ports }}</h4>
                                    <p class="mb-0">Inactive Ports</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-x-circle" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Single Gateway Card -->
            <div class="row">
                {% if gateways and gateways|length > 0 %}
                    {% set gateway = gateways[0] %}
                    <div class="col-12">
                        <div class="card gateway-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-router text-success"></i>
                                    {{ gateway.name }}
                                    <span class="badge bg-primary ms-2">Primary</span>
                                </h5>
                                <small class="text-muted">{{ gateway.ip_address }}:{{ gateway.api_port }}</small>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <strong>Status:</strong><br>
                                        <span class="status-active">Active</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>IP Address:</strong><br>
                                        <span class="text-muted">{{ gateway.ip_address }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>API Port:</strong><br>
                                        <span class="text-muted">{{ gateway.api_port }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>TG SMS Port:</strong><br>
                                        <span class="text-muted">{{ gateway.tg_sms_port }}</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <strong>SMS Ports (4):</strong>
                                    <div class="port-grid">
                                        {% for i in range(1, 5) %}
                                            {% set port_status = gateway.port_status_map[i] if gateway.port_status_map and i in gateway.port_status_map else 'inactive' %}
                                            <div class="port-status port-{{ port_status }}">
                                                <strong>Port {{ i }}</strong><br>
                                                <small>{{ 'Active' if port_status == 'active' else 'No SIM' }}</small>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>Note:</strong> This is your single GSM gateway. Port 2 has no SIM card installed.
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="bi bi-router text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No GSM Gateway Found</h4>
                            <p class="text-muted">The main GSM gateway is not configured.</p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}