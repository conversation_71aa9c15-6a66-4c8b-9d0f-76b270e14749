#!/usr/bin/env python3
"""
Compare gateway configurations to find working settings
"""
import os
import sys
import socket
import time
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import GSMGateway

def test_gateway_ip(ip_address, port=5038):
    """Test a specific gateway IP with different credentials"""
    print(f"\n📡 Testing Gateway: {ip_address}:{port}")
    print("-" * 50)
    
    # Try different credential combinations
    credential_sets = [
        ("apiuser", "apipass"),
        ("admin", "admin"),
        ("admin", "password"),
        ("manager", "manager"),
        ("tgsms", "tgsms"),
        ("user", "user"),
        ("sms", "sms")
    ]
    
    for username, password in credential_sets:
        try:
            print(f"🔐 Testing: {username}/{password}")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((ip_address, port))
            
            if result != 0:
                print(f"   ❌ Cannot connect to {ip_address}:{port}")
                sock.close()
                continue
            
            # Read initial response
            initial = sock.recv(1024).decode()
            
            # Send login
            login_cmd = f"Action: Login\r\nUsername: {username}\r\nSecret: {password}\r\n\r\n"
            sock.send(login_cmd.encode())
            
            # Read login response
            login_resp = sock.recv(1024).decode()
            
            if "Response: Success" in login_resp:
                print(f"   ✅ Login successful!")
                
                # Test event subscription
                event_cmd = "Action: Events\r\nEventMask: all\r\n\r\n"
                sock.send(event_cmd.encode())
                
                event_resp = sock.recv(1024).decode()
                print(f"   📥 Events: {event_resp.strip()}")
                
                if "Events: On" in event_resp:
                    print(f"   🎉 SMS EVENTS ENABLED! This is the working configuration!")
                    sock.close()
                    return (username, password, True)
                else:
                    print(f"   ⚠️  Events disabled")
                    sock.close()
                    return (username, password, False)
            else:
                print(f"   ❌ Login failed")
            
            sock.close()
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    return None

def find_working_configuration():
    """Find the working gateway configuration"""
    print("="*60)
    print("🔍 FINDING WORKING GATEWAY CONFIGURATION")
    print("="*60)
    
    # Test the IP where it was working (*************)
    print("🎯 Testing the IP where SMS was working before...")
    working_config_115 = test_gateway_ip("*************")
    
    # Test the current IP (*************)
    print("\n🎯 Testing the current IP...")
    working_config_127 = test_gateway_ip("*************")
    
    # Test the other gateway IP (************)
    print("\n🎯 Testing the other gateway IP...")
    working_config_96 = test_gateway_ip("************")
    
    return {
        "*************": working_config_115,
        "*************": working_config_127,
        "************": working_config_96
    }

def update_gateway_with_working_config(ip, username, password, events_enabled):
    """Update the gateway with working configuration"""
    app = create_app()
    
    with app.app_context():
        print(f"\n🔧 UPDATING GATEWAY CONFIGURATION")
        print("="*60)
        
        gateway = GSMGateway.query.filter_by(is_primary=True).first()
        
        print(f"📝 Current config:")
        print(f"   IP: {gateway.ip_address}")
        print(f"   Username: {gateway.username}")
        print(f"   Password: {gateway.password}")
        
        print(f"\n📝 New config:")
        print(f"   IP: {ip}")
        print(f"   Username: {username}")
        print(f"   Password: {password}")
        print(f"   Events: {'✅ Enabled' if events_enabled else '❌ Disabled'}")
        
        # Update gateway
        gateway.ip_address = ip
        gateway.username = username
        gateway.password = password
        
        from datetime import datetime
        gateway.updated_at = datetime.now()
        
        db.session.commit()
        
        print(f"✅ Gateway configuration updated!")
        
        # Restart SMS receiver
        print(f"\n🔄 Restarting SMS receiver...")
        
        try:
            from app.sms.receiver import SMSReceiver
            
            # Stop existing receiver
            receiver = getattr(app, 'sms_receiver', None)
            if receiver:
                receiver.stop_receiver()
                time.sleep(2)
            
            # Start new receiver
            new_receiver = SMSReceiver(app)
            new_receiver.start_receiver()
            app.sms_receiver = new_receiver
            
            time.sleep(3)
            
            print(f"📊 SMS Receiver Status:")
            print(f"   Running: {'✅' if new_receiver.running else '❌'}")
            print(f"   Connected: {'✅' if new_receiver.connected else '❌'}")
            
            if new_receiver.running and new_receiver.connected:
                print(f"🎉 SMS receiver successfully restarted!")
                return True
            else:
                print(f"❌ SMS receiver failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Error restarting receiver: {str(e)}")
            return False

if __name__ == '__main__':
    print("🚀 Gateway Configuration Recovery Tool")
    print("🎯 Goal: Find and restore the working SMS configuration")
    
    # Find working configurations
    configs = find_working_configuration()
    
    print(f"\n📊 RESULTS SUMMARY:")
    print("="*60)
    
    working_found = False
    best_config = None
    
    for ip, config in configs.items():
        if config:
            username, password, events_enabled = config
            status = "🎉 WORKING" if events_enabled else "⚠️  Login OK, Events Disabled"
            print(f"{ip}: {username}/{password} - {status}")
            
            if events_enabled and not working_found:
                working_found = True
                best_config = (ip, username, password, events_enabled)
        else:
            print(f"{ip}: ❌ No working credentials found")
    
    if working_found:
        ip, username, password, events_enabled = best_config
        print(f"\n✅ Found working configuration: {ip} with {username}/{password}")
        
        response = input(f"\n🔄 Update gateway to use this working configuration? (y/n): ")
        
        if response.lower() == 'y':
            success = update_gateway_with_working_config(ip, username, password, events_enabled)
            
            if success:
                print(f"\n🎉 SUCCESS! SMS receiving should now work!")
                print(f"📱 Try sending an SMS to test")
            else:
                print(f"\n❌ Failed to update configuration")
        else:
            print(f"\n💡 Manual update needed:")
            print(f"   1. Go to Gateways → Edit Primary Gateway")
            print(f"   2. Set IP: {ip}")
            print(f"   3. Set Username: {username}")
            print(f"   4. Set Password: {password}")
            print(f"   5. Save changes")
    else:
        print(f"\n❌ No working configuration found with SMS events enabled")
        print(f"💡 You may need to:")
        print(f"   1. Enable SMS events in gateway web interface")
        print(f"   2. Check TG SMS service configuration")
        print(f"   3. Verify gateway SMS functionality")
    
    print("\n" + "="*60)
