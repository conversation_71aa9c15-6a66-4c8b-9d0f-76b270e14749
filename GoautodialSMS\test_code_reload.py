#!/usr/bin/env python3
"""
Test Code Reload
"""

import requests
import time

def test_code_reload():
    """Test if new code is loaded"""
    print("🔍 Testing Code Reload")
    print("=" * 25)
    
    try:
        # Wait for Flask app
        time.sleep(2)
        
        # Test the new debug route
        print("🧪 Testing New Debug Route:")
        response = requests.get('http://127.0.0.1:5000/debug/code_reload_test')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ New code is loaded!")
            print(f"   Message: {data['message']}")
            print(f"   Database check:")
            print(f"      Gateways: {data['database_check']['gateways']}")
            print(f"      Ports: {data['database_check']['ports']}")
            print(f"      Gateway max_ports: {data['database_check']['gateway_max_ports']}")
            
            if data['database_check']['gateway_max_ports'] == 8:
                print("✅ Database shows 8 max_ports!")
            else:
                print(f"❌ Database shows {data['database_check']['gateway_max_ports']} max_ports")
            
            return True
        else:
            print(f"❌ New route not found: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_code_reload()
