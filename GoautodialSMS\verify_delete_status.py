#!/usr/bin/env python3
"""
Verify delete functionality status
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSMessage, User
import requests

def check_delete_routes():
    """Check if delete routes are properly registered"""
    with app.app_context():
        print("🔍 Checking Delete Routes Registration")
        print("=" * 40)
        
        # Get all registered routes
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'rule': rule.rule
            })
        
        # Check for delete routes
        delete_routes = [r for r in routes if 'delete' in r['rule'].lower()]
        
        print(f"📋 Found {len(delete_routes)} delete-related routes:")
        for route in delete_routes:
            methods = [m for m in route['methods'] if m not in ['HEAD', 'OPTIONS']]
            print(f"   {route['rule']} - {methods}")
        
        # Check specific routes
        single_delete_found = any('/sms/<int:message_id>/delete' in r['rule'] for r in routes)
        bulk_delete_found = any('/sms/bulk_delete' in r['rule'] for r in routes)
        
        print(f"\n✅ Single delete route (/sms/<id>/delete): {'Found' if single_delete_found else 'Missing'}")
        print(f"✅ Bulk delete route (/sms/bulk_delete): {'Found' if bulk_delete_found else 'Missing'}")
        
        return single_delete_found, bulk_delete_found

def check_user_permissions():
    """Check user permissions for delete functionality"""
    with app.app_context():
        print(f"\n👥 Checking User Permissions")
        print("=" * 30)
        
        users = User.query.all()
        
        for user in users:
            print(f"\n   👤 {user.username} ({user.role}):")
            
            # Check relevant permissions
            permissions_to_check = [
                'can_view_inbox',
                'can_view_outbox', 
                'can_send_sms',
                'can_manage_users',
                'can_manage_settings'
            ]
            
            for perm in permissions_to_check:
                has_perm = user.has_permission(perm)
                status = "✅" if has_perm else "❌"
                print(f"      {status} {perm}: {has_perm}")
            
            # Check assigned ports
            assigned_ports = user.get_assigned_ports()
            if assigned_ports:
                print(f"      🔌 Assigned ports: {assigned_ports}")
            else:
                print(f"      🔌 Assigned ports: All (no restrictions)")

def check_database_messages():
    """Check current messages in database"""
    with app.app_context():
        print(f"\n📊 Current Database Status")
        print("=" * 30)
        
        total_messages = SMSMessage.query.count()
        inbound_count = SMSMessage.query.filter_by(direction='inbound').count()
        outbound_count = SMSMessage.query.filter_by(direction='outbound').count()
        
        print(f"   Total Messages: {total_messages}")
        print(f"   Inbound: {inbound_count}")
        print(f"   Outbound: {outbound_count}")
        
        if total_messages > 0:
            print(f"\n   Recent Messages:")
            recent = SMSMessage.query.order_by(SMSMessage.created_at.desc()).limit(3).all()
            for msg in recent:
                print(f"     ID {msg.id}: {msg.direction} from {msg.phone_number}")
                print(f"       Content: {msg.content[:40]}...")
                print(f"       Port: {msg.gsm_port}")

def test_web_connectivity():
    """Test if web interface is accessible"""
    print(f"\n🌐 Testing Web Interface Connectivity")
    print("=" * 40)
    
    try:
        # Test main page
        response = requests.get('http://127.0.0.1:5000/', timeout=5)
        print(f"   Main page: {response.status_code} {'✅' if response.status_code in [200, 302] else '❌'}")
        
        # Test login page
        response = requests.get('http://127.0.0.1:5000/login', timeout=5)
        print(f"   Login page: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
        
        # Test outbox page (should redirect to login)
        response = requests.get('http://127.0.0.1:5000/outbox', timeout=5)
        print(f"   Outbox page: {response.status_code} {'✅' if response.status_code in [200, 302] else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error connecting to web interface: {str(e)}")
        return False

def show_delete_instructions():
    """Show instructions for testing delete functionality"""
    print(f"\n📋 DELETE FUNCTIONALITY TEST INSTRUCTIONS")
    print("=" * 50)
    
    print(f"🔧 **SINGLE MESSAGE DELETE:**")
    print(f"   1. Go to http://127.0.0.1:5000")
    print(f"   2. Login as admin (admin/admin123)")
    print(f"   3. Go to Inbox or Outbox")
    print(f"   4. Click the trash icon (🗑️) next to any message")
    print(f"   5. Message should be deleted immediately")
    
    print(f"\n🔧 **BULK DELETE:**")
    print(f"   1. Go to Inbox or Outbox")
    print(f"   2. Check the checkboxes next to multiple messages")
    print(f"   3. Click the 'Delete' button in the bulk actions bar")
    print(f"   4. Confirm deletion")
    print(f"   5. Selected messages should be deleted")
    
    print(f"\n🔧 **PERMISSION TESTING:**")
    print(f"   1. Login as different users (sales_team/sales123, etc.)")
    print(f"   2. Try to delete messages from different ports")
    print(f"   3. Users should only be able to delete messages from their assigned ports")

def main():
    print("🔍 DELETE FUNCTIONALITY STATUS CHECK")
    print("=" * 45)
    
    # Check routes
    single_delete, bulk_delete = check_delete_routes()
    
    # Check permissions
    check_user_permissions()
    
    # Check database
    check_database_messages()
    
    # Test connectivity
    web_accessible = test_web_connectivity()
    
    # Show summary
    print(f"\n🎯 SUMMARY:")
    print("=" * 15)
    print(f"   Single Delete Route: {'✅ Available' if single_delete else '❌ Missing'}")
    print(f"   Bulk Delete Route: {'✅ Available' if bulk_delete else '❌ Missing'}")
    print(f"   Web Interface: {'✅ Accessible' if web_accessible else '❌ Not accessible'}")
    
    if single_delete and web_accessible:
        print(f"\n✅ **DELETE FUNCTIONALITY IS READY FOR TESTING!**")
        show_delete_instructions()
    else:
        print(f"\n❌ **ISSUES DETECTED - DELETE FUNCTIONALITY MAY NOT WORK**")
        if not single_delete:
            print(f"   - Single delete route is missing")
        if not bulk_delete:
            print(f"   - Bulk delete route is missing")
        if not web_accessible:
            print(f"   - Web interface is not accessible")

if __name__ == "__main__":
    main()
