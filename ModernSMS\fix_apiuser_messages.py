#!/usr/bin/env python3
"""
Fix apiuser messages - create received message for their assigned port
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import SMSMessage, SMSPort, User
from datetime import datetime
import uuid

def fix_apiuser_messages():
    """Create received message for apiuser's assigned port"""
    app = create_app()
    
    with app.app_context():
        print("="*60)
        print("🔧 FIX APIUSER MESSAGES")
        print("="*60)
        
        try:
            # Get apiuser
            apiuser = User.query.filter_by(username='apiuser').first()
            if not apiuser:
                print("❌ API user not found!")
                return
            
            print(f"👤 API User: {apiuser.username}")
            print(f"📡 Assigned ports: {apiuser.assigned_ports}")
            
            # Get their assigned port
            if not apiuser.assigned_ports:
                print("❌ API user has no assigned ports!")
                return
            
            assignment = apiuser.assigned_ports[0]  # Get first assignment
            gateway_id = assignment.get('gateway_id')
            port_number = assignment.get('port')
            
            print(f"🎯 Target: Gateway {gateway_id}, Port {port_number}")
            
            # Find the actual port record
            port = SMSPort.query.filter_by(
                gateway_id=gateway_id,
                port_number=str(port_number),
                is_active=True
            ).first()
            
            if not port:
                print(f"❌ Port not found: Gateway {gateway_id}, Port {port_number}")
                return
            
            print(f"✅ Found port: ID {port.id}")
            
            # Create test message for this port
            test_message = SMSMessage(
                message_id=str(uuid.uuid4()),
                phone_number='+************',
                content='Test message for API user assigned port',
                direction='inbound',
                status='received',
                port_id=port.id,
                created_at=datetime.now()
            )
            
            # Check if similar message exists
            existing = SMSMessage.query.filter_by(
                phone_number='+************',
                content='Test message for API user assigned port',
                direction='inbound'
            ).first()
            
            if existing:
                print("⚠️  Test message already exists")
            else:
                db.session.add(test_message)
                db.session.commit()
                print("✅ Created test message for API user's port")
            
            # Test dashboard stats for apiuser
            print(f"\n📊 Testing API user dashboard stats...")
            from app.main.routes import get_dashboard_stats
            from flask_login import login_user
            
            with app.test_request_context():
                login_user(apiuser)
                stats = get_dashboard_stats()
                print(f"  Total messages: {stats['total_messages']}")
                print(f"  Sent messages: {stats['sent_messages']}")
                print(f"  Received messages: {stats['received_messages']}")
                print(f"  Today received: {stats['today_received']}")
            
            # Show all received messages for this port
            port_messages = SMSMessage.query.filter_by(
                direction='inbound',
                port_id=port.id
            ).all()
            
            print(f"\n📥 All received messages for Port {port.id}:")
            for msg in port_messages:
                print(f"  - From: {msg.phone_number}")
                print(f"    Content: {msg.content[:50]}...")
                print(f"    Created: {msg.created_at}")
            
            print(f"\n✅ Fix completed successfully!")
            
        except Exception as e:
            print(f"❌ Error during fix: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    fix_apiuser_messages()
