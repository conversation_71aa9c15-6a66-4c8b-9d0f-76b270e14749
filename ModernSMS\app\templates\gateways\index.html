{% extends "base.html" %}

{% block title %}Gateway Management - Modern SMS Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Gateway Management</h1>
        <p class="text-muted">Manage your GSM gateways and monitor their status</p>
    </div>
    <div>
        <a href="{{ url_for('gateways.add') }}" class="btn btn-primary">
            <i class="bi bi-plus"></i> Add Gateway
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <h3>{{ stats.total_gateways or 0 }}</h3>
            <p><i class="bi bi-hdd-network"></i> Total Gateways</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #059669 0%, #10b981 100%);">
            <h3>{{ stats.active_gateways or 0 }}</h3>
            <p><i class="bi bi-check-circle"></i> Active</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);">
            <h3>{{ stats.total_ports or 0 }}</h3>
            <p><i class="bi bi-diagram-3"></i> Total Ports</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);">
            <h3>{{ stats.active_ports or 0 }}</h3>
            <p><i class="bi bi-wifi"></i> Active Ports</p>
        </div>
    </div>
</div>

<!-- Gateways List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-list"></i> Gateways</h5>
        <button class="btn btn-sm btn-outline-primary" onclick="refreshAllGateways()">
            <i class="bi bi-arrow-clockwise"></i> Refresh All
        </button>
    </div>
    <div class="card-body">
        {% if gateways %}
            <div class="row">
                {% for gateway in gateways %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 gateway-card" data-gateway-id="{{ gateway.id }}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle {{ 'bg-success' if gateway.status == 'active' else 'bg-danger' }}" 
                                     style="width: 12px; height: 12px; margin-right: 10px;"></div>
                                <h6 class="mb-0">{{ gateway.name }}</h6>
                            </div>
                            {% if gateway.is_primary %}
                                <span class="badge bg-primary">Primary</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <small class="text-muted">IP Address:</small><br>
                                <code>{{ gateway.ip_address }}:{{ gateway.api_port }}</code>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Location:</small><br>
                                {{ gateway.location or 'Not specified' }}
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Ports:</small><br>
                                <span class="fw-bold">{{ gateway.max_ports }}</span> configured
                            </div>
                            
                            <!-- Port Status Summary -->
                            {% set port_summary = gateway.get_port_status_summary() %}
                            <div class="mb-3">
                                <div class="row text-center">
                                    <div class="col-3">
                                        <div class="text-success fw-bold">{{ port_summary.active }}</div>
                                        <small class="text-muted">Active</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-warning fw-bold">{{ port_summary.sim_only }}</div>
                                        <small class="text-muted">SIM Only</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-danger fw-bold">{{ port_summary.no_sim }}</div>
                                        <small class="text-muted">No SIM</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="text-secondary fw-bold">{{ port_summary.unknown }}</div>
                                        <small class="text-muted">Unknown</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="{{ url_for('gateways.detail', gateway_id=gateway.id) }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i> View
                                </a>
                                <a href="{{ url_for('gateways.edit', gateway_id=gateway.id) }}" 
                                   class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <button class="btn btn-outline-info btn-sm" 
                                        onclick="testGateway({{ gateway.id }})">
                                    <i class="bi bi-wifi"></i> Test
                                </button>
                                <button class="btn btn-outline-success btn-sm" 
                                        onclick="refreshPorts({{ gateway.id }})">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                                {% if not gateway.is_primary %}
                                <button class="btn btn-outline-danger btn-sm" 
                                        onclick="deleteGateway({{ gateway.id }}, '{{ gateway.name }}')">
                                    <i class="bi bi-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-hdd-network text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">No Gateways Configured</h4>
                <p class="text-muted">Add your first GSM gateway to start managing SMS communications.</p>
                <a href="{{ url_for('gateways.add') }}" class="btn btn-primary">
                    <i class="bi bi-plus"></i> Add Your First Gateway
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the gateway <strong id="deleteGatewayName"></strong>?</p>
                <p class="text-danger"><i class="bi bi-exclamation-triangle"></i> This action cannot be undone and will also delete all associated ports.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Gateway</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .gateway-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .gateway-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .btn-group .btn {
        flex: 1;
    }
    
    code {
        background: rgba(var(--bs-primary-rgb), 0.1);
        color: var(--primary-color);
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let deleteGatewayId = null;

    function testGateway(gatewayId) {
        const btn = event.target.closest('button');
        const originalHtml = btn.innerHTML;
        
        btn.innerHTML = '<span class="loading-spinner"></span>';
        btn.disabled = true;
        
        fetch(`/gateways/${gatewayId}/test`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Gateway connection successful!', 'success');
            } else {
                showToast('Gateway connection failed: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Error testing gateway: ' + error.message, 'error');
        })
        .finally(() => {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        });
    }

    function refreshPorts(gatewayId) {
        const btn = event.target.closest('button');
        const originalHtml = btn.innerHTML;
        
        btn.innerHTML = '<span class="loading-spinner"></span>';
        btn.disabled = true;
        
        fetch(`/gateways/${gatewayId}/ports/refresh`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                // Optionally reload the page to show updated port status
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Error refreshing ports: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Error refreshing ports: ' + error.message, 'error');
        })
        .finally(() => {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        });
    }

    function deleteGateway(gatewayId, gatewayName) {
        deleteGatewayId = gatewayId;
        document.getElementById('deleteGatewayName').textContent = gatewayName;
        
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }

    function refreshAllGateways() {
        location.reload();
    }

    // Handle delete confirmation
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (!deleteGatewayId) return;
        
        const btn = this;
        const originalHtml = btn.innerHTML;
        
        btn.innerHTML = '<span class="loading-spinner"></span> Deleting...';
        btn.disabled = true;
        
        fetch(`/gateways/${deleteGatewayId}/delete`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Error deleting gateway: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Error deleting gateway: ' + error.message, 'error');
        })
        .finally(() => {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
            bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
        });
    });

    // Auto-refresh every 60 seconds
    setInterval(() => {
        // Refresh port status indicators
        refreshAllGateways();
    }, 60000);
</script>
{% endblock %}
