#!/usr/bin/env python3
"""
Fix database schema issues
"""

import sys
import os
sys.path.append('backend')

from app import app, db, SMSPort, User
from sqlalchemy import text

def check_database_tables():
    """Check which tables exist in the database"""
    with app.app_context():
        print("🔍 Checking Database Tables")
        print("=" * 30)
        
        try:
            # Get all table names
            result = db.engine.execute(text("SELECT name FROM sqlite_master WHERE type='table';"))
            tables = [row[0] for row in result]
            
            print(f"📊 Found {len(tables)} tables:")
            for table in tables:
                print(f"   ✅ {table}")
            
            # Check specific tables we need
            required_tables = ['users', 'sms_messages', 'sms_ports']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                print(f"\n❌ Missing required tables: {missing_tables}")
                return False, missing_tables
            else:
                print(f"\n✅ All required tables exist")
                return True, []
                
        except Exception as e:
            print(f"❌ Error checking tables: {str(e)}")
            return False, []

def check_sms_ports_schema():
    """Check SMS ports table schema"""
    with app.app_context():
        print(f"\n🔍 Checking SMS Ports Table Schema")
        print("=" * 40)
        
        try:
            # Get table schema
            result = db.engine.execute(text("PRAGMA table_info(sms_ports);"))
            columns = [(row[1], row[2]) for row in result]
            
            print(f"📊 SMS Ports table columns:")
            for col_name, col_type in columns:
                print(f"   ✅ {col_name}: {col_type}")
            
            # Check required columns
            required_columns = ['id', 'port_number', 'status', 'network_name', 'signal_quality', 'is_active', 'last_updated']
            existing_columns = [col[0] for col in columns]
            missing_columns = [col for col in required_columns if col not in existing_columns]
            
            if missing_columns:
                print(f"\n❌ Missing columns: {missing_columns}")
                return False, missing_columns
            else:
                print(f"\n✅ All required columns exist")
                return True, []
                
        except Exception as e:
            print(f"❌ Error checking SMS ports schema: {str(e)}")
            return False, []

def recreate_sms_ports_table():
    """Recreate SMS ports table with correct schema"""
    with app.app_context():
        print(f"\n🔧 Recreating SMS Ports Table")
        print("=" * 35)
        
        try:
            # Drop existing table if it exists
            db.engine.execute(text("DROP TABLE IF EXISTS sms_ports;"))
            print("   🗑️ Dropped existing sms_ports table")
            
            # Create new table with correct schema
            db.create_all()
            print("   ✅ Created new sms_ports table")
            
            # Initialize with default ports
            initialize_default_ports()
            
            return True
            
        except Exception as e:
            print(f"❌ Error recreating SMS ports table: {str(e)}")
            return False

def initialize_default_ports():
    """Initialize default SMS ports"""
    with app.app_context():
        print(f"\n📱 Initializing Default SMS Ports")
        print("=" * 35)
        
        try:
            # Check if ports already exist
            existing_ports = SMSPort.query.count()
            if existing_ports > 0:
                print(f"   ℹ️ {existing_ports} ports already exist, skipping initialization")
                return True
            
            # Create default ports (1-8)
            default_ports = []
            for port_num in range(1, 9):
                port = SMSPort(
                    port_number=str(port_num),
                    status='unknown',
                    network_name='',
                    signal_quality='',
                    sim_imsi='',
                    is_active=True
                )
                default_ports.append(port)
                db.session.add(port)
            
            db.session.commit()
            
            print(f"   ✅ Created {len(default_ports)} default ports")
            for port in default_ports:
                print(f"      - Port {port.port_number}: {port.status}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error initializing default ports: {str(e)}")
            db.session.rollback()
            return False

def test_sms_ports_query():
    """Test SMS ports query"""
    with app.app_context():
        print(f"\n🧪 Testing SMS Ports Query")
        print("=" * 30)
        
        try:
            ports = SMSPort.query.all()
            print(f"   ✅ Successfully queried {len(ports)} ports")
            
            for port in ports:
                print(f"      - Port {port.port_number}: {port.status} ({'Active' if port.is_active else 'Inactive'})")
            
            return True
            
        except Exception as e:
            print(f"❌ Error querying SMS ports: {str(e)}")
            return False

def test_user_editing():
    """Test user editing functionality"""
    with app.app_context():
        print(f"\n🧪 Testing User Editing Functionality")
        print("=" * 40)
        
        try:
            # Get admin user
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("   ❌ Admin user not found")
                return False
            
            print(f"   ✅ Found admin user: {admin_user.username}")
            print(f"      Email: {admin_user.email}")
            print(f"      Role: {admin_user.role}")
            print(f"      Active: {admin_user.is_active}")
            
            # Test getting assigned ports
            assigned_ports = admin_user.get_assigned_ports()
            print(f"      Assigned ports: {assigned_ports}")
            
            # Test querying ports for user editing
            ports = SMSPort.query.all()
            print(f"   ✅ Successfully loaded {len(ports)} ports for user editing")
            
            return True
            
        except Exception as e:
            print(f"❌ Error testing user editing: {str(e)}")
            return False

def fix_database_issues():
    """Main function to fix database issues"""
    print("🔧 DATABASE SCHEMA FIX UTILITY")
    print("=" * 35)
    
    # Check tables
    tables_ok, missing_tables = check_database_tables()
    
    if not tables_ok:
        print(f"\n🔧 Creating missing tables...")
        with app.app_context():
            db.create_all()
        print(f"   ✅ Database tables created")
    
    # Check SMS ports schema
    schema_ok, missing_columns = check_sms_ports_schema()
    
    if not schema_ok:
        print(f"\n🔧 Fixing SMS ports table schema...")
        if recreate_sms_ports_table():
            print(f"   ✅ SMS ports table fixed")
        else:
            print(f"   ❌ Failed to fix SMS ports table")
            return False
    
    # Test queries
    if test_sms_ports_query():
        print(f"\n✅ SMS ports query test passed")
    else:
        print(f"\n❌ SMS ports query test failed")
        return False
    
    # Test user editing
    if test_user_editing():
        print(f"\n✅ User editing test passed")
    else:
        print(f"\n❌ User editing test failed")
        return False
    
    print(f"\n🎉 DATABASE SCHEMA FIX COMPLETED SUCCESSFULLY!")
    print("=" * 45)
    print("✅ All database issues have been resolved")
    print("✅ User editing should now work properly")
    print("✅ SMS ports are properly initialized")
    
    return True

if __name__ == "__main__":
    success = fix_database_issues()
    if success:
        print(f"\n🚀 You can now edit users without errors!")
    else:
        print(f"\n⚠️ Some issues remain. Check the output above for details.")
