#!/usr/bin/env python3
"""
SMS System Diagnostic Tool
Comprehensive diagnosis of SMS sending and receiving issues
"""

import os
import sys
import socket
import requests
import subprocess
from datetime import datetime

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def check_python_environment():
    """Check Python environment and dependencies"""
    print_header("PYTHON ENVIRONMENT CHECK")
    
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    
    # Check required modules
    required_modules = ['socket', 'requests', 'urllib.parse', 'flask']
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - Available")
        except ImportError:
            print(f"❌ {module} - Missing")

def check_network_basics():
    """Check basic network connectivity"""
    print_header("NETWORK CONNECTIVITY CHECK")
    
    # Test internet connectivity
    try:
        response = requests.get('http://google.com', timeout=5)
        print("✅ Internet connectivity - Working")
    except:
        print("❌ Internet connectivity - Failed")
    
    # Test local network
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"✅ Local IP address: {local_ip}")
    except:
        print("❌ Could not determine local IP address")

def check_yeastar_connectivity():
    """Check connectivity to Yeastar system"""
    print_header("YEASTAR CONNECTIVITY CHECK")
    
    yeastar_ip = '*************'
    
    # Test ping
    print(f"Testing ping to {yeastar_ip}...")
    try:
        if sys.platform.startswith('win'):
            result = subprocess.run(['ping', '-n', '1', yeastar_ip], 
                                  capture_output=True, text=True, timeout=10)
        else:
            result = subprocess.run(['ping', '-c', '1', yeastar_ip], 
                                  capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ Ping to {yeastar_ip} - Success")
        else:
            print(f"❌ Ping to {yeastar_ip} - Failed")
    except:
        print(f"❌ Ping to {yeastar_ip} - Error")
    
    # Test SMS API port (HTTP)
    print(f"Testing SMS API (HTTP) to {yeastar_ip}...")
    try:
        response = requests.get(f'http://{yeastar_ip}', timeout=5)
        print(f"✅ HTTP connection to {yeastar_ip} - Success (Status: {response.status_code})")
    except requests.exceptions.ConnectTimeout:
        print(f"❌ HTTP connection to {yeastar_ip} - Timeout")
    except requests.exceptions.ConnectionError:
        print(f"❌ HTTP connection to {yeastar_ip} - Connection refused")
    except Exception as e:
        print(f"❌ HTTP connection to {yeastar_ip} - Error: {e}")
    
    # Test TG SMS port (TCP 5038)
    print(f"Testing TG SMS port 5038 to {yeastar_ip}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((yeastar_ip, 5038))
        sock.close()
        
        if result == 0:
            print(f"✅ TG SMS port 5038 to {yeastar_ip} - Open")
        else:
            print(f"❌ TG SMS port 5038 to {yeastar_ip} - Closed")
    except Exception as e:
        print(f"❌ TG SMS port 5038 to {yeastar_ip} - Error: {e}")

def check_sms_api():
    """Test SMS API functionality"""
    print_header("SMS API TEST")
    
    yeastar_ip = '*************'
    test_url = f"http://{yeastar_ip}/cgi/WebCGI?1500101=account=apiuser&password=apipass&port=1&destination=+**********&content=test"
    
    print(f"Testing SMS API URL: {test_url}")
    
    try:
        response = requests.get(test_url, timeout=10)
        print(f"✅ SMS API Response - Status: {response.status_code}")
        print(f"Response content: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ SMS API is responding")
        else:
            print(f"⚠️  SMS API returned status {response.status_code}")
            
    except requests.exceptions.ConnectTimeout:
        print("❌ SMS API - Connection timeout")
    except requests.exceptions.ConnectionError:
        print("❌ SMS API - Connection refused")
    except Exception as e:
        print(f"❌ SMS API - Error: {e}")

def check_environment_variables():
    """Check environment variables"""
    print_header("ENVIRONMENT VARIABLES CHECK")
    
    sms_vars = [
        'SMS_API_IP', 'SMS_API_ACCOUNT', 'SMS_API_PASSWORD', 'SMS_API_PORT',
        'TG_SMS_IP', 'TG_SMS_PORT', 'TG_SMS_USERNAME', 'TG_SMS_PASSWORD'
    ]
    
    for var in sms_vars:
        value = os.environ.get(var)
        if value:
            if 'PASSWORD' in var or 'SECRET' in var:
                print(f"✅ {var} = {'*' * len(value)}")
            else:
                print(f"✅ {var} = {value}")
        else:
            print(f"❌ {var} = Not set")

def check_flask_app():
    """Check if Flask app is running"""
    print_header("FLASK APPLICATION CHECK")
    
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        print(f"✅ Flask app is running - Status: {response.status_code}")
        
        # Test debug endpoints
        try:
            config_response = requests.get('http://localhost:5000/debug/config', timeout=5)
            print(f"✅ Debug config endpoint - Status: {config_response.status_code}")
        except:
            print("❌ Debug config endpoint - Not accessible")
            
        try:
            status_response = requests.get('http://localhost:5000/debug/sms_status', timeout=5)
            print(f"✅ SMS status endpoint - Status: {status_response.status_code}")
        except:
            print("❌ SMS status endpoint - Not accessible")
            
    except:
        print("❌ Flask app is not running")
        print("   Start with: cd backend && python app.py")

def generate_report():
    """Generate diagnostic report"""
    print_header("DIAGNOSTIC REPORT")
    
    print(f"Diagnostic run at: {datetime.now()}")
    print(f"System: {sys.platform}")
    print(f"Python: {sys.version}")
    
    print("\nRecommended troubleshooting steps:")
    print("1. Run: python test_network.py")
    print("2. Run: python test_yeastar_sms.py")
    print("3. Check Yeastar TG SMS service configuration")
    print("4. Verify firewall settings")
    print("5. Check SMS API credentials")
    
    print("\nCommon issues and solutions:")
    print("• Connection refused → Check if Yeastar TG SMS service is running")
    print("• Timeout → Check network connectivity and firewall")
    print("• Authentication failed → Verify username/password")
    print("• SMS not sending → Check SMS API URL and credentials")
    print("• SMS not receiving → Check TG SMS server connection and event format")

def main():
    """Run all diagnostic checks"""
    print("SMS System Diagnostic Tool")
    print(f"Starting diagnosis at {datetime.now()}")
    
    check_python_environment()
    check_network_basics()
    check_yeastar_connectivity()
    check_environment_variables()
    check_sms_api()
    check_flask_app()
    generate_report()
    
    print("\n" + "=" * 60)
    print(" DIAGNOSIS COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
