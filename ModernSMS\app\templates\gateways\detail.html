{% extends "base.html" %}

{% block title %}{{ gateway.name }} - Gateway Details{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">{{ gateway.name }}</h1>
        <p class="text-muted">Gateway details and port status</p>
    </div>
    <div>
        <a href="{{ url_for('gateways.edit', gateway_id=gateway.id) }}" class="btn btn-outline-primary">
            <i class="bi bi-pencil"></i> Edit
        </a>
        <a href="{{ url_for('gateways.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Gateway Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    {% if gateway.status == 'active' %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-danger">{{ gateway.status.title() }}</span>
                    {% endif %}
                    {% if gateway.is_primary %}
                        <span class="badge bg-primary ms-1">Primary</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>IP Address:</strong><br>
                    <code>{{ gateway.ip_address }}:{{ gateway.api_port }}</code>
                </div>
                
                <div class="mb-3">
                    <strong>TG SMS Port:</strong><br>
                    <code>{{ gateway.tg_sms_port }}</code>
                </div>
                
                <div class="mb-3">
                    <strong>Username:</strong><br>
                    <span class="text-muted">{{ gateway.username }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Max Ports:</strong><br>
                    <span class="text-muted">{{ gateway.max_ports }}</span>
                </div>
                
                {% if gateway.location %}
                <div class="mb-3">
                    <strong>Location:</strong><br>
                    <span class="text-muted">{{ gateway.location }}</span>
                </div>
                {% endif %}
                
                {% if gateway.description %}
                <div class="mb-3">
                    <strong>Description:</strong><br>
                    <span class="text-muted">{{ gateway.description }}</span>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    <span class="text-muted">{{ gateway.created_at.strftime('%B %d, %Y') if gateway.created_at else 'Unknown' }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    <span class="text-muted">{{ gateway.updated_at.strftime('%B %d, %Y at %I:%M %p') if gateway.updated_at else 'Never' }}</span>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-tools"></i> Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-info" onclick="testConnection()">
                        <i class="bi bi-wifi"></i> Test Connection
                    </button>
                    
                    <button class="btn btn-outline-success" onclick="refreshPorts()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh Ports
                    </button>
                    
                    <a href="{{ url_for('gateways.edit', gateway_id=gateway.id) }}" class="btn btn-outline-primary">
                        <i class="bi bi-pencil"></i> Edit Gateway
                    </a>
                    
                    {% if not gateway.is_primary %}
                    <button class="btn btn-outline-danger" onclick="deleteGateway()">
                        <i class="bi bi-trash"></i> Delete Gateway
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-diagram-3"></i> Port Status</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshPorts()">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
            </div>
            <div class="card-body">
                {% if ports %}
                    <div class="row">
                        {% for port in ports %}
                        <div class="col-md-6 mb-3">
                            <div class="card border">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Port {{ port.port_number }}</h6>
                                        {% if port.status == 'active' %}
                                            <span class="badge bg-success">Active</span>
                                        {% elif port.status == 'sim_only' %}
                                            <span class="badge bg-warning">SIM Only</span>
                                        {% elif port.status == 'no_sim' %}
                                            <span class="badge bg-danger">No SIM</span>
                                        {% elif port.status == 'error' %}
                                            <span class="badge bg-danger">Error</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ port.status.title() }}</span>
                                        {% endif %}
                                    </div>
                                    
                                    {% if port.network_name and port.network_name != 'Unknown' %}
                                    <div class="mb-1">
                                        <small class="text-muted">Network:</small>
                                        <span class="fw-bold">{{ port.network_name }}</span>
                                    </div>
                                    {% endif %}
                                    
                                    {% if port.signal_strength and port.signal_strength != 'Unknown' %}
                                    <div class="mb-1">
                                        <small class="text-muted">Signal:</small>
                                        <span>{{ port.signal_strength }}</span>
                                    </div>
                                    {% endif %}
                                    
                                    {% if port.phone_number and port.phone_number != 'Unknown' %}
                                    <div class="mb-1">
                                        <small class="text-muted">Number:</small>
                                        <span>{{ port.phone_number }}</span>
                                    </div>
                                    {% endif %}
                                    
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            Last checked: {{ port.last_checked.strftime('%m/%d %H:%M') if port.last_checked else 'Never' }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-diagram-3 text-muted" style="font-size: 3rem;"></i>
                        <h6 class="text-muted mt-2">No ports configured</h6>
                        <p class="text-muted">Ports will be created automatically when the gateway is added.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function testConnection() {
        const btn = event.target;
        const originalHtml = btn.innerHTML;
        
        btn.innerHTML = '<span class="loading-spinner"></span> Testing...';
        btn.disabled = true;
        
        fetch(`/gateways/{{ gateway.id }}/test`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Gateway connection successful!', 'success');
            } else {
                showToast('Gateway connection failed: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Error testing gateway: ' + error.message, 'error');
        })
        .finally(() => {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        });
    }

    function refreshPorts() {
        const btn = event.target;
        const originalHtml = btn.innerHTML;
        
        btn.innerHTML = '<span class="loading-spinner"></span>';
        btn.disabled = true;
        
        fetch(`/gateways/{{ gateway.id }}/ports/refresh`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Error refreshing ports: ' + data.error, 'error');
            }
        })
        .catch(error => {
            showToast('Error refreshing ports: ' + error.message, 'error');
        })
        .finally(() => {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        });
    }

    function deleteGateway() {
        if (confirm('Are you sure you want to delete this gateway? This action cannot be undone.')) {
            fetch(`/gateways/{{ gateway.id }}/delete`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = "{{ url_for('gateways.index') }}";
                    }, 1000);
                } else {
                    showToast('Error deleting gateway: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('Error deleting gateway: ' + error.message, 'error');
            });
        }
    }
</script>
{% endblock %}
